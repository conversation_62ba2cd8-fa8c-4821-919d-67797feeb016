import React, {useState} from 'react';
import {View, StyleSheet, useWindowDimensions} from 'react-native';
import {List} from 'react-native-paper';
import {colors} from '../../theme/colors';
import customColors from '../../theme/customColors';
import RenderHtml from 'react-native-render-html';
interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQAccordion: React.FC<FAQItemProps> = ({question, answer}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const {width} = useWindowDimensions();
  return (
    <View style={styles.container}>
      <List.Accordion
        title={question}
        expanded={isExpanded}
        onPress={() => setIsExpanded(!isExpanded)}
        titleStyle={styles.question}
        style={styles.accordion}
        // eslint-disable-next-line react/no-unstable-nested-components
        right={({isExpanded}) => (
          <List.Icon icon={isExpanded ? 'chevron-up' : 'chevron-down'} />
        )}
        titleNumberOfLines={0}>
        <View style={styles.answerContainer}>
          <RenderHtml contentWidth={width} source={{html: answer}} />
        </View>
      </List.Accordion>
    </View>
  );
};

export default FAQAccordion;

const styles = StyleSheet.create({
  accordion: {
    backgroundColor: colors.background,
    borderColor: colors.gray.medium,
    borderRadius: 10,
    borderWidth: 1,
  },
  answer: {
    color: customColors.textBlack,
    fontSize: 14,
  },
  answerContainer: {
    backgroundColor: colors.background,
    borderColor: colors.gray.dark,
    borderRadius: 8,
    minHeight: 20,
    padding: 10,
  },
  container: {
    backgroundColor: colors.background,
    borderRadius: 10,
    elevation: 2,
    marginVertical: 5,
    padding: 0,
    shadowColor: customColors.textBlack,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  question: {
    color: customColors.textBlack,
    fontSize: 16,
    fontWeight: 'bold',
  },
});
