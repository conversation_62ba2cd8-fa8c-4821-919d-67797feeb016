export interface Seller {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string;
  createdBy: string | null;
  modifiedBy: string | null;
  id: string;
  sellerId: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  verificationCode: string;
  status: string;
  rejectionReason: string;
  userTenantId: string;
  userTenant: UserTenant;
}

export interface UserTenant {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string;
  createdBy: string | null;
  modifiedBy: string | null;
  id: string;
  status: number;
  locale: string | null;
  tenantId: string;
  userId: string;
  roleId: string;
  tenant_id: string;
  user_id: string;
  role_id: string;
  user: User;
}

export interface User {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string;
  createdBy: string | null;
  modifiedBy: string | null;
  id: string;
  firstName: string;
  lastName: string;
  middleName: string | null;
  username: string;
  email: string;
  phone: string;
  authClientIds: number[];
  lastLogin: string | null;
  photoUrl: string | null;
  designation: string;
  dob: string;
  gender: string;
  defaultTenantId: string;
  default_tenant_id: string;
}
export type ImageValue =
  | string
  | {path: string; mime: string; filename: string};

export interface SellerStore {
  id?: string;
  storeName: string;
  website?: string;
  signature?: ImageValue;
  description?: string;
  fbId?: string;
  instaId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  dp?: ImageValue;
  banner?: ImageValue;
  logo?: ImageValue;
  sellerId: string;
  storeDescription?: string;
  legalName?: string;
}
