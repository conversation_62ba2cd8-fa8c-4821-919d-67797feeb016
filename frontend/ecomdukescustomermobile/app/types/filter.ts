export interface IFilter {
  limit?: number;
  skip?: number;
  order?: Array<Record<string, unknown> | string>;
  where?: Record<string, unknown>;
  fields?: Record<string, boolean>;
  include?: Array<Record<string, unknown> | string>;
}

export interface IFilterExcludingWhere {
  limit?: number;
  skip?: number;
  order?: Array<Record<string, unknown> | string>;
  fields?: Record<string, boolean>;
  include?: Array<Record<string, unknown> | string>;
}

export interface IFilterWithKeyword extends IFilter {
  keyword?: string;
  facetValueIds?: string[];
  collectionIds?: string[];
}
export const metaFields = [
  'deleted',
  'deletedOn',
  'deletedBy',
  'createdBy',
  'createdOn',
  'modifiedOn',
  'modifiedBy',
];
export const fieldsExcludeMetaFields = metaFields.reduce((acc, field) => {
  acc[field] = false;
  return acc;
}, {} as Record<string, boolean>);
