import {ProductVariant} from '../redux/product/product';

export const infoList = [
  {label: 'Return & Policy', type: 'returnPolicy'},
  {label: 'Disclaimer', type: 'disclaimer'},
  {label: 'Terms & Conditions', type: 'terms'},
];

export const getModalContent = (
  type: string | null,
  productVariant: ProductVariant | undefined,
) => {
  switch (type) {
    case 'returnPolicy':
      return {
        title: 'Return Policy',
        content: productVariant?.productReturnPolicy?.returnPolicy,
        fallback: 'No return policy available.',
      };
    case 'disclaimer':
      return {
        title: 'Disclaimer',
        content: productVariant?.productDetail?.details,
        fallback: 'No disclaimer available.',
      };
    case 'terms':
      return {
        title: 'Terms & Conditions',
        content: productVariant?.productTermsAndCondition?.terms,
        fallback: 'No terms available.',
      };
    default:
      return {title: '', content: '', fallback: ''};
  }
};
