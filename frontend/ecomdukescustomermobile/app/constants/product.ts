import {IFilterWithKeyword} from '../Types/filter';

export const getSearchParam = (
  isLoggedIn: boolean,
  profileId?: string,
): IFilterWithKeyword => ({
  include: [
    {
      relation: 'featuredAsset',
      scope: {
        fields: {preview: true, id: true},
      },
    },
    {
      relation: 'productVariantPrice',
      scope: {
        fields: {
          price: true,
          mrp: true,
          currencyCode: true,
        },
      },
    },
    ...(isLoggedIn
      ? [
          {
            relation: 'wishlist',
            scope: {
              where: {
                deleted: false,
                customerId: profileId,
              },
              fields: {id: true},
            },
          },
        ]
      : []),
  ],
  fields: {
    name: true,
    id: true,
    featuredAssetId: true,
    productId: true,
  },
});
