// src/constants/menuData.ts

import {Legals} from '../enums/legal-category.enum';
import {SCREEN_NAME} from './screenNames';

export const menuItems = [
  {label: 'My Order', screen: SCREEN_NAME.MY_ORDER},
  {label: 'Wish List', screen: SCREEN_NAME.WISHLIST},
  {label: 'Messages', screen: SCREEN_NAME.CHAT_LIST},
  {
    label: 'My Account',
    subItems: [
      {label: 'My Profile Information', screen: SCREEN_NAME.PROFILE},
      {label: 'Manage Address', screen: SCREEN_NAME.ADDRESS},
      {
        label: 'Login and Security',
        subItems: [
          {label: 'Change Password', screen: SCREEN_NAME.CHANGE_PASSWORD},
          {label: 'Deactivate account', screen: 'DeactivateAccount'},
        ],
      },
    ],
  },
  {
    label: 'Help and Support',
    subItems: [
      {label: 'Help', screen: SCREEN_NAME.HELP},
      {label: 'FAQ', screen: SCREEN_NAME.FAQ},
      {label: 'Customer Service', screen: SCREEN_NAME.CUSTOMER_SERVICE},
    ],
  },
  {
    label: 'Legal',
    subItems: [
      {
        label: 'Terms of Use',
        screen: SCREEN_NAME.LEGAL,
        params: {title: 'Terms of Use', type: Legals.TermsofUse},
      },
      {
        label: 'Privacy Policy',
        screen: SCREEN_NAME.LEGAL,
        params: {title: 'Privacy Policy', type: Legals.PrivacyPolicy},
      },
      {
        label: 'Affiliate Policy',
        screen: SCREEN_NAME.LEGAL,
        params: {title: 'Affiliate Policy', type: Legals.AffiliatePolicy},
      },
      {
        label: 'Return & Refund Policy',
        screen: SCREEN_NAME.LEGAL,
        params: {
          title: 'Return & Refund Policy',
          type: Legals.ReturnRefundPolicy,
        },
      },
      {
        label: 'License Agreement',
        screen: SCREEN_NAME.LEGAL,
        params: {title: 'License Agreement', type: Legals.Licence},
      },
      {
        label: 'General Disclaimer',
        screen: SCREEN_NAME.LEGAL,
        params: {title: 'General Disclaimer', type: Legals.Disclaimer},
      },
      {
        label: 'Guidelines',
        screen: SCREEN_NAME.LEGAL,
        params: {title: 'Guidelines', type: Legals.Guideline},
      },
    ],
  },
  {label: 'Switch Account', screen: SCREEN_NAME.SWITCH_ACCOUNT},
  {label: 'Referral', screen: SCREEN_NAME.REFERRAL},
];
