import AsyncStorage from '@react-native-async-storage/async-storage';

export interface StoredToken {
  accessToken: string;
  refreshToken: string;
  expires: number;
}

export interface Account {
  id: number;
  email: string;
  isActive?: boolean;
  token?: StoredToken;
}

// Save token for user
export const saveTokenForUser = async (email: string, token: StoredToken) => {
  const tokens = JSON.parse(
    (await AsyncStorage.getItem('auth_tokens')) || '{}',
  );
  tokens[email] = token;
  await AsyncStorage.setItem('auth_tokens', JSON.stringify(tokens));
};

// Get token by email
export const getTokenForUser = async (
  email: string,
): Promise<StoredToken | null> => {
  const tokens = JSON.parse(
    (await AsyncStorage.getItem('auth_tokens')) || '{}',
  );
  return tokens[email] || null;
};

// Save a new account
export const saveAccount = async (account: Account) => {
  const existing = await getAccounts();
  if (!existing.find(acc => acc.email === account.email)) {
    existing.push(account);
    await AsyncStorage.setItem('auth_accounts', JSON.stringify(existing));
  }
};

// Get all saved accounts
export const getAccounts = async (): Promise<Account[]> => {
  const json = await AsyncStorage.getItem('auth_accounts');
  return json ? JSON.parse(json) : [];
};

// Set current user
export const setCurrentUser = async (email: string) => {
  await AsyncStorage.setItem('current_user', email);
};

// Get current user
export const getCurrentUser = async (): Promise<string | null> => {
  return await AsyncStorage.getItem('current_user');
};

// Remove user
export const removeAccount = async (email: string) => {
  const accounts = await getAccounts();
  const updated = accounts.filter(acc => acc.email !== email);
  await AsyncStorage.setItem('auth_accounts', JSON.stringify(updated));
};

// Remove token
export const removeTokenForUser = async (email: string) => {
  const tokens = JSON.parse(
    (await AsyncStorage.getItem('auth_tokens')) || '{}',
  );
  delete tokens[email];
  await AsyncStorage.setItem('auth_tokens', JSON.stringify(tokens));
};
