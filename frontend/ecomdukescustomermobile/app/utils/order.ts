import {OrderStatus} from '../redux/order/order';

const OrderStatusLabels: Record<OrderStatus, string> = {
  [OrderStatus.Pending]: 'Pending',
  [OrderStatus.Paid]: 'Paid',
  [OrderStatus.PaymentFailed]: 'Payment Failed',
  [OrderStatus.Failed]: 'Order Failed',
  [OrderStatus.Picked]: 'Picked',
  [OrderStatus.InTransit]: 'In Transit',
  [OrderStatus.Delivered]: 'Delivered',
  [OrderStatus.DeliveryFailed]: 'Delivery Failed',
  [OrderStatus.Cancelled]: 'Cancelled',
  [OrderStatus.Returned]: 'Returned',
  [OrderStatus.Refunded]: 'Refunded',
  [OrderStatus.RefundInitiated]: 'Refund Initiated',
  [OrderStatus.RefundFailed]: 'Refund Failed',
};

const OrderStatusColors: Record<OrderStatus, string> = {
  [OrderStatus.Pending]: '#FFA500', // Orange
  [OrderStatus.Paid]: '#007BFF', // Blue
  [OrderStatus.PaymentFailed]: '#FF4C4C', // Red
  [OrderStatus.Failed]: '#D32F2F', // Dark Red
  [OrderStatus.Picked]: '#6A1B9A', // Purple
  [OrderStatus.InTransit]: '#1E88E5', // Bright Blue
  [OrderStatus.Delivered]: '#42C900', // Green ✅
  [OrderStatus.DeliveryFailed]: '#FF6F00', // Deep Orange
  [OrderStatus.Cancelled]: '#9E9E9E', // Grey
  [OrderStatus.Returned]: '#FF7043', // Coral
  [OrderStatus.Refunded]: '#42C900', // Green ✅
  [OrderStatus.RefundInitiated]: '#42C900', // Green ✅
  [OrderStatus.RefundFailed]: '#C62828', // Darker Red
};

export const getOrderStatusLabel = (orderStatus: OrderStatus): string => {
  return OrderStatusLabels[orderStatus] || 'Unknown Status';
};

export const getOrderStatusColor = (orderStatus: OrderStatus): string => {
  return OrderStatusColors[orderStatus] || '#007BFF';
};
