import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {RootState} from '../store';

export interface ApiMonitorData {
  currentApi: string | null;
  loginCallBack: string | null;
}
export interface ApiMonitorResData {
  currentApi: string;
  loginCallBack: string;
}
const initialState: ApiMonitorData = {
  currentApi: null,
  loginCallBack: null,
};
const ApiMonitorSlice = createSlice({
  name: 'apiMonitor',
  initialState,
  reducers: {
    setMonitor: (state, action: PayloadAction<ApiMonitorResData>) => {
      const {currentApi, loginCallBack} = action.payload;
      state.currentApi = currentApi;
      state.loginCallBack = loginCallBack;
    },
    unsetMonitor: state => {
      state.currentApi = null;
      state.loginCallBack = null;
    },
  },
});
export const {setMonitor, unsetMonitor} = ApiMonitorSlice.actions;
export default ApiMonitorSlice.reducer;
export const selectCurrentApi = (state: RootState) =>
  state.apiMonitor.currentApi;
export const selectCurrentLoginCallBack = (state: RootState) =>
  state.apiMonitor.loginCallBack;
