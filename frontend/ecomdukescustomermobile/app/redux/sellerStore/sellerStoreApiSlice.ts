import {ApiSliceIdentifier} from '../../constants/enums';
import {buildFilterParams, IFilter} from '../../types/api';
import {SellerStore} from '../../types/seller';
import {apiSlice} from '../apiSlice';

export const sellerStoreApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getSellerStore: builder.query<SellerStore[], IFilter>({
      query: filter => ({
        url: '/seller-stores',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getSellerStoreById: builder.query<SellerStore, string>({
      query: sellerId => ({
        url: `/seller-stores/seller/${sellerId}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            order: ['createdOn DESC'],
          }),
        },
      }),
    }),
  }),
});

export const {useGetSellerStoreByIdQuery, useGetSellerStoreQuery} =
  sellerStoreApiSlice;
