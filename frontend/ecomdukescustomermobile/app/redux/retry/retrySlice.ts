import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {BaseQueryApi} from '@reduxjs/toolkit/query';
import {Args} from '../apiSlice';

export interface ApiRetryData {
  baseUrl: string | null;
  args: Args | null;
  api: BaseQueryApi | null;
  extraOptions: object | null;
}

const initialState: ApiRetryData = {
  baseUrl: null,
  args: null,
  api: null,
  extraOptions: null,
};

const apiRetrySlice = createSlice({
  name: 'apiRetry',
  initialState,
  reducers: {
    setRetryArgs: (state, action: PayloadAction<ApiRetryData>) => {
      const {api, baseUrl, args, extraOptions} = action.payload;
      state.api = api;
      state.args = args;
      state.baseUrl = baseUrl;
      state.extraOptions = extraOptions;
    },
    unsetRetryArgs: state => {
      state.api = null;
      state.args = null;
      state.baseUrl = null;
      state.extraOptions = null;
    },
  },
});

export const {setRetryArgs, unsetRetryArgs} = apiRetrySlice.actions;

export default apiRetrySlice.reducer;
