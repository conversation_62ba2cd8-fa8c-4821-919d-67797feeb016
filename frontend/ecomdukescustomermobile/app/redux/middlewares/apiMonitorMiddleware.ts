import {Middleware} from '@reduxjs/toolkit';
import {endpointCallbackMap} from '../../constants/endpoint';
import {setMonitor} from '../apiMonitor/apiMonitorSlice';

const apiMonitorMiddleware: Middleware =
  ({dispatch}) =>
  next =>
  (action: any) => {
    const endpointName = action?.meta?.arg?.endpointName;

    if (endpointName && endpointCallbackMap.has(endpointName)) {
      const callback = endpointCallbackMap.get(endpointName);

      if (callback) {
        dispatch(
          setMonitor({
            currentApi: endpointName,
            loginCallBack: callback,
          }),
        );
      }
    }

    return next(action);
  };

export default apiMonitorMiddleware;
