import {ApiSliceIdentifier} from '../../constants/enums';
import {LegalVisibility} from '../../enums/legal-category.enum';
import {CreateTicketDto, HelpItem, SupportContactInfo} from '../../types/help';
import {apiSlice} from '../apiSlice';

export const supportApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    createSupportTicket: builder.mutation<void, CreateTicketDto>({
      query: formData => ({
        url: '/tickets',
        method: 'POST',
        body: formData,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getHelp: builder.query<HelpItem[], void>({
      query: () => ({
        url: '/helps',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {
                inq: [LegalVisibility.CUSTOMER, LegalVisibility.ALL],
              },
            },
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getSupportContact: builder.query<SupportContactInfo[], void>({
      query: () => ({
        url: '/support',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {
                inq: [LegalVisibility.CUSTOMER, LegalVisibility.ALL],
              },
            },
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useCreateSupportTicketMutation,
  useGetHelpQuery,
  useGetSupportContactQuery,
} = supportApiSlice;
