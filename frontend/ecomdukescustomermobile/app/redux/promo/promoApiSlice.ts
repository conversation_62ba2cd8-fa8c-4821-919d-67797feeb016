import {ApiSliceIdentifier} from '../../constants/enums';
import {apiSlice} from '../../redux/apiSlice';
import {IFilterWithKeyword} from '../../Types/filter';
import {PromoCode} from './promocode';

export const promoCodeApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPromoCodes: builder.query<PromoCode[], void>({
      query: () => ({
        url: '/promo-codes',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    getPromoCodesSearch: builder.query<PromoCode[], IFilterWithKeyword>({
      query: ({keyword, ...rest}) => {
        const where: Record<string, unknown> = {
          ...(keyword && {code: {like: `%${keyword}%`}}),
          ...rest.where,
        };

        return {
          url: '/promo-codes',
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            filter: JSON.stringify({where}),
          },
        };
      },
    }),
  }),
  overrideExisting: false,
});

export const {useGetPromoCodesQuery, useGetPromoCodesSearchQuery} =
  promoCodeApi;
