export enum AppliesTo {
  all = 'all',
  products = 'products',
  categories = 'categories',
}
export interface PromoCode {
  id: string;
  code: string;
  value: number;
  minCartValue?: number;
  maxDiscountCap?: number;
  usageLimitPerUser?: number;
  usageLimitTotal?: number;
  isFirstTimeUserOnly?: boolean;
  isRepeatUserOnly?: boolean;
  validFrom: string;
  validTill: string;
  isActive: boolean;
  categoryIds?: string[];
  productIds?: string[];
  appliesTo: AppliesTo;
}
