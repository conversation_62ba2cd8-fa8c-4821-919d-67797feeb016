import {ApiSliceIdentifier} from '../../constants/enums';
import {buildFilterParams} from '../../types/cartApi';
import {IFilter} from '../../types/filter';
import {apiSlice} from '../apiSlice';
import {Facet} from './facet';
import {Product, ProductCustomField, ProductVariant} from './product';

export const productApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getProducts: builder.query<Product[], void>({
      query: () => ({
        url: '/products',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'featuredAsset',
              },
            ],
          }),
        },
      }),
    }),

    getFacets: builder.query<Facet[], void>({
      query: () => ({
        url: '/facets',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'facetValues',
              },
            ],
            order: 'name ASC',
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),
    getProductVariantById: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getProductCustomizations: builder.query<ProductCustomField[], IFilter>({
      query: filter => ({
        url: '/product-customization-fields',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getProductVariants: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/product-variants',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
  }),
});

export const {
  useGetProductsQuery,
  useLazyGetProductsQuery,
  useLazyGetProductVariantsQuery,
  useGetFacetsQuery,
  useGetProductVariantByIdQuery,
  useGetProductCustomizationsQuery,
  useLazyGetProductCustomizationsQuery,
  useGetProductVariantsQuery,
} = productApiSlice;
