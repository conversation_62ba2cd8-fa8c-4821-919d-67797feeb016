import {ApiSliceIdentifier} from '../../constants/enums';
import {Count} from '../../types/api';
import {buildFilterParams} from '../../types/cartApi';
import {IFilter} from '../../Types/filter';
import {Wishlist} from '../../types/wishlist';
import {apiSlice} from '../apiSlice';

export const wishlistApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    // Get user's wishlist
    getWishlists: builder.query<Wishlist[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/wishlists',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getWishlistsCount: builder.query<Count, {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/wishlists/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get wishlist by ID
    getWishlistById: builder.query<
      Wishlist,
      {id: string; include?: Array<Record<string, unknown> | string>}
    >({
      query: ({id, include}) => ({
        url: `/wishlists/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({include}),
        },
      }),
    }),

    // Add item to wishlist
    addItemToWishlist: builder.mutation<Wishlist, string>({
      query: productVariantId => ({
        url: '/wishlists',
        method: 'POST',
        body: {productVariantId},
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    // Remove item from wishlist
    removeItemFromWishlist: builder.mutation<void, string>({
      query: wishlistId => ({
        url: `/wishlists/${wishlistId}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useAddItemToWishlistMutation,
  useGetWishlistsQuery,
  useLazyGetWishlistsQuery,
  useLazyGetWishlistsCountQuery,
  useRemoveItemFromWishlistMutation,
  useGetWishlistByIdQuery,
} = wishlistApiSlice;
