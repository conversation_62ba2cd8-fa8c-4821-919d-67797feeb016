import {ApiSliceIdentifier} from '../../constants/enums';
import {apiSlice} from '../../redux/apiSlice';
import {buildFilterParams, Count, IFilter} from '../../types/api';
import {Review} from '../product/product';
import {ApiTags} from '../types';

export const reviewApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    // Create Review
    createReview: builder.mutation<Review, Omit<Review, 'id'>>({
      query: review => ({
        url: '/reviews',
        method: 'POST',
        body: review,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    // Get Reviews with filter
    getReviews: builder.query<Review[], IFilter>({
      query: filter => ({
        url: '/reviews',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get Review by ID
    getReviewById: builder.query<Review, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/reviews/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: filter ? buildFilterParams(filter) : undefined,
        },
      }),
      providesTags: [ApiTags.PRODUCT_REVIEWS],
    }),

    // Update Review by ID
    updateReview: builder.mutation<void, {id: string; review: Partial<Review>}>(
      {
        query: ({id, review}) => ({
          url: `/reviews/${id}`,
          method: 'PATCH',
          body: review,
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        }),
      },
    ),

    // Delete Review by ID
    deleteReview: builder.mutation<void, string>({
      query: id => ({
        url: `/reviews/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      invalidatesTags: [ApiTags.PRODUCT_REVIEWS],
    }),

    // Get Review Count
    getReviewCount: builder.query<Count, IFilter>({
      query: filter => ({
        url: '/reviews/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          where: filter?.where ? JSON.stringify(filter.where) : undefined,
        },
      }),
    }),
  }),
});

export const {
  useCreateReviewMutation,
  useGetReviewsQuery,
  useLazyGetReviewsQuery,
  useGetReviewByIdQuery,
  useLazyGetReviewByIdQuery,
  useUpdateReviewMutation,
  useDeleteReviewMutation,
  useGetReviewCountQuery,
  useLazyGetReviewCountQuery,
} = reviewApiSlice;
