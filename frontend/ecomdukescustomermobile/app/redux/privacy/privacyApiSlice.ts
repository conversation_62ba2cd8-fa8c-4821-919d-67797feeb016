import {ApiSliceIdentifier} from '../../constants/enums';
import {Category} from '../../enums/terms.enum';
import {Privacy} from '../../types/terms';
import {apiSlice} from '../apiSlice';

export const privacyApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPrivacyAndPolicy: builder.query<Privacy[], string>({
      query: code => ({
        url: '/privacy-policies',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              category: {inq: [Category.CUSTOMER]},
            },
          }),
        },
        headers: {
          Authorization: `Bearer ${code}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),
  }),
});

export const {useGetPrivacyAndPolicyQuery} = privacyApiSlice;
