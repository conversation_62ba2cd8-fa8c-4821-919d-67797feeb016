import {apiSlice} from '../apiSlice';
import {Faq} from '../../types/faq';
import {FaqVisibility} from '../../enums/faq.enum';
import {ApiSliceIdentifier} from '../../constants/enums';

export const faqApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getFaqs: builder.query<Faq[], void>({
      query: () => ({
        url: '/faqs',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {inq: [FaqVisibility.CUSTOMER, FaqVisibility.ALL]},
            },
            order: ['priority DESC'],
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {useGetFaqsQuery} = faqApiSlice;
