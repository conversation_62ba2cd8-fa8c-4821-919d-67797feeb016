import type {
  BaseQueryApi,
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
  FetchBaseQueryMeta,
} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {getItem, removeItem, setItem} from './mmkvStorage';
import {
  AuthResData,
  setCredentials,
  unsetCredentials,
  unsetGuestToken,
} from './auth/authSlice';
import {getBaseUrl} from './redux.helper';
import {handleApiError} from '../hooks/useApiErrorHandler';
import {RootState} from './store';
import {ApiSliceIdentifier} from '../constants/enums';
import {ApiRetryData, setRetryArgs} from './retry/retrySlice';
import {ApiTags} from './types';

export interface Args {
  url: string;
  method?: string;
  body?: any;
  apiSliceIdentifier?: ApiSliceIdentifier;
  isFormData?: boolean;
}

const RESULT_ERROR_STATUS = 401;
const FORBIDDEN_ERROR_STATUS = 403;
const authEndpoints = [
  'login',
  'signUp',
  'createSignUpToken',
  'updatePassword',
];

const getBaseQuery = (baseUrl: string, args: Args) => {
  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers) {
      const accessToken = getItem<string>('accessToken');
      const guestToken = getItem<string>('guestToken');
      const token = accessToken ?? guestToken;

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('x-origin', 'ecomdukes-customer');
      if (!args.isFormData) {
        headers.set('Content-Type', 'application/json');
      }
      return headers;
    },
  });
  return baseQuery;
};
/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */

const baseQueryWithReauth: BaseQueryFn<
  Args,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;

  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);

  const baseQuery = getBaseQuery(baseUrl as string, args);

  let result = await baseQuery(args, api, extraOptions);

  if (result.error?.status === FORBIDDEN_ERROR_STATUS) {
    api.dispatch(unsetCredentials());
    api.dispatch(unsetGuestToken());
    api.dispatch(
      setRetryArgs({baseUrl: baseUrl as string, args, api, extraOptions}),
    );
  }

  if (
    result.error?.status === RESULT_ERROR_STATUS &&
    !authEndpoints.includes(api.endpoint)
  ) {
    const refreshToken = getItem('refreshToken');

    if (!refreshToken) {
      removeItem('guestToken');
      // No refresh token: try guest token flow

      return await handleGuestTokenFlow(
        baseQuery,
        args,
        api,
        extraOptions,
        state,
      );
    }

    // Try refreshing access token
    const refreshResult = await baseQuery(
      {
        url: getBaseUrl(state) + '/auth/refresh',
        method: 'POST',
        body: {refreshToken},
      },
      api,
      extraOptions,
    );

    if (refreshResult.data) {
      api.dispatch(setCredentials(refreshResult.data as AuthResData));
      return await baseQuery(args, api, extraOptions);
    }

    // Refresh failed: clear credentials and fallback to guest token
    api.dispatch(unsetCredentials());
    return await handleGuestTokenFlow(
      baseQuery,
      args,
      api,
      extraOptions,
      state,
    );
  }

  if (result?.error) {
    handleApiError(result.error);
  }
  return result;
};

const handleGuestTokenFlow = async (
  baseQuery: BaseQueryFn<
    string | FetchArgs,
    unknown,
    FetchBaseQueryError,
    {},
    FetchBaseQueryMeta
  >,
  args: Args,
  api: BaseQueryApi,
  extraOptions: any,
  state: RootState,
) => {
  const guestToken = getItem('guestToken');

  if (guestToken) {
    return await baseQuery(args, api, extraOptions);
  }

  const guestTokenResponse = await baseQuery(
    {
      url: getBaseUrl(state, args.apiSliceIdentifier) + '/auth/guest',
      method: 'POST',
    },
    api,
    extraOptions,
  );

  if (guestTokenResponse.data) {
    const newGuestToken = guestTokenResponse.data as AuthResData;
    setItem('guestToken', newGuestToken.accessToken);

    return await baseQuery(args, api, extraOptions);
  } else {
    console.log('Failed to generate guest token.');
    return guestTokenResponse;
  }
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
  tagTypes: [ApiTags.PRODUCT_REVIEWS],
});

export const retryApi = async (params: ApiRetryData) => {
  const {baseUrl, args, api, extraOptions} = params;
  if (!args || !baseUrl || !api) {
    return;
  }
  const baseQuery = getBaseQuery(baseUrl as string, args);
  let result = await baseQuery(args, api, extraOptions ?? {});
  if (result?.error) {
    handleApiError(result.error);
  }
  return result;
};
