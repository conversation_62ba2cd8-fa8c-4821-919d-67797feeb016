import {ApiSliceIdentifier} from '../constants/enums';
import {buildFilterParams, IFilter} from '../types/api';
import {IPinnedProduct} from '../types/pinnedProduct';
import {apiSlice} from './apiSlice';

export const pinnedProductApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPinnedProducts: builder.query<IPinnedProduct[], IFilter>({
      query: filter => ({
        url: '/pinned-products',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
  }),
});

export const {useGetPinnedProductsQuery, useLazyGetPinnedProductsQuery} =
  pinnedProductApiSlice;
