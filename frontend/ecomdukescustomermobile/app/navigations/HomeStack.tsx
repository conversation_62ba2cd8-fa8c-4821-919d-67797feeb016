import {createNativeStackNavigator} from '@react-navigation/native-stack';
import HomeScreen from '../screens/main/home/<USER>';
import ProductListingScreen from '../screens/main/Products/ProudctListingScreen';
import OrderSuccessScreen from '../screens/main/Orders/OrderSuccessScreen';
import FAQScreen from '../screens/main/faq/FAQScreen';
import AddressScreen from '../screens/main/Address/AddressScreen';
import ReferralScreen from '../screens/main/referral/ReferralScreen';
import ChatListScreen from '../screens/main/message/ChatListScreen';
import MessageScreen from '../screens/main/message/MessageScreen';

const Stack = createNativeStackNavigator();

export const HomeStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen name="HomeScreen" component={HomeScreen} />
      <Stack.Screen name="ProductList" component={ProductListingScreen} />
      <Stack.Screen name="OrderSuccess" component={OrderSuccessScreen} />
      <Stack.Screen name="FAQ" component={FAQScreen} />
      <Stack.Screen name="Address" component={AddressScreen} />
      <Stack.Screen name="Referral" component={ReferralScreen} />
      {/* For screens you want to hide the footer */}
      <Stack.Screen
        name="ChatList"
        component={ChatListScreen}
        options={{presentation: 'modal'}} // or tabBarStyle: { display: 'none' }
      />
      <Stack.Screen
        name="Message"
        component={MessageScreen}
        options={{presentation: 'modal'}} // or tabBarStyle: { display: 'none' }
      />
    </Stack.Navigator>
  );
};
