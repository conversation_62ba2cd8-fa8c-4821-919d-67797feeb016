import React, {useMemo} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import {OnboardStackParamList} from './types';
import {useTypedSelector} from '../redux/store';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import ProductListingScreen from '../screens/main/Products/ProudctListingScreen';
import {Images} from '../assets/images';
import BottomTabNavigation from './BottomTab';
import NotificationBadge from '../components/notification/NotificationBadge';
import {SCREEN_NAME} from '../constants/screenNames';
import ProductDetailsScreen from '../screens/main/ProductDetails/ProductDetailsScreen';
import SearchSuggestionScreen from '../screens/main/search/SearchSuggestionScreen';
import {colors} from '../theme/colors';
import OrderConfirmation from '../screens/main/cart/OrderConfirmation';
import HomeScreen from '../screens/main/home/<USER>';
import {useSelector} from 'react-redux';
import {selectCurrentLoginCallBack} from '../redux/apiMonitor/apiMonitorSlice';
import OrderSuccessScreen from '../screens/main/Orders/OrderSuccessScreen';
import AddressScreen from '../screens/main/Address/AddressScreen';
import FAQScreen from '../screens/main/faq/FAQScreen';
import ProfileScreen from '../screens/main/profile/ProfileScreen';
import PrivacyPolicyScreen from '../screens/main/privacy/PrivacyPolicyScreen';
import RefundPolicyScreen from '../screens/main/refund/RefundPolicyScreen';
import MessageScreen from '../screens/main/message/MessageScreen';
import ChangePasswordScreen from '../screens/auth/changePassword/ChangePassword';
import ChatListScreen from '../screens/main/message/ChatListScreen';
import ReferralScreen from '../screens/main/referral/ReferralScreen';
import HelpScreen from '../screens/main/help/HelpScreen';
import CustomerService from '../screens/main/customerService/customerService';
import SellerStorePage from '../screens/main/sellerStore/SellerStore';
import ViewAllGiftsScreen from '../screens/main/home/<USER>/ViewAllGiftScreen';
import GiftProductsSection from '../screens/main/home/<USER>/GiftProductSection';
import ExploreAllProducts from '../screens/main/home/<USER>/ExploreAllScreen';
import LegalScreen from '../screens/main/legal/LegalScreen';

const Stack = createNativeStackNavigator<OnboardStackParamList>();
const OnboardNavigationStack = () => {
  const {isLoggedIn} = useTypedSelector(state => state.auth);
  const callback = useSelector(selectCurrentLoginCallBack);

  const initialRoute = useMemo(() => {
    if (isLoggedIn && callback) {
      return callback as keyof OnboardStackParamList;
    }
    return 'mainHome';
  }, [isLoggedIn, callback]);

  return (
    <Stack.Navigator
      key={initialRoute}
      screenOptions={{
        headerShown: true,
        headerBackVisible: true,
        headerBackTitle: '',
        headerTintColor: colors.tertiary,
        headerBackButtonDisplayMode: 'minimal',
        title: '',
        // eslint-disable-next-line react/no-unstable-nested-components
        headerRight: () => <NotificationBadge />,
        // eslint-disable-next-line react/no-unstable-nested-components
        headerTitle: () => (
          <View style={styles.headerContainer}>
            <Image source={Images.logowithouttag} style={styles.image} />
          </View>
        ),
        headerTitleAlign: 'center',
      }}
      initialRouteName={initialRoute}>
      <Stack.Screen
        name={'mainHome'}
        component={BottomTabNavigation}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name={SCREEN_NAME.HOME}
        component={HomeScreen}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name={SCREEN_NAME.PRODUCT_DETAILS}
        component={ProductListingScreen}
      />
      <Stack.Screen
        name={SCREEN_NAME.SUGGESTION}
        component={SearchSuggestionScreen}
      />
      <Stack.Screen
        name={SCREEN_NAME.PRODUCTS}
        component={ProductDetailsScreen}
      />
      <Stack.Screen
        name={SCREEN_NAME.ORDER_CONFIRM}
        component={OrderConfirmation}
        headerShown={true}
      />
      <Stack.Screen
        name={SCREEN_NAME.ORDER_SUCCESS}
        component={OrderSuccessScreen}
      />
      <Stack.Screen
        name={SCREEN_NAME.ADDRESS}
        component={AddressScreen}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name={SCREEN_NAME.FAQ}
        component={FAQScreen}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name={SCREEN_NAME.PROFILE}
        component={ProfileScreen}
        options={{headerShown: true}}
      />

      <Stack.Screen
        name={SCREEN_NAME.PRIVACY}
        component={PrivacyPolicyScreen}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name={SCREEN_NAME.REFUND_POLICY}
        component={RefundPolicyScreen}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name={SCREEN_NAME.MESSAGE}
        component={MessageScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={SCREEN_NAME.CHAT_LIST}
        component={ChatListScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={SCREEN_NAME.REFERRAL}
        component={ReferralScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name={SCREEN_NAME.CHANGE_PASSWORD}
        component={ChangePasswordScreen}
      />

      <Stack.Screen name={SCREEN_NAME.HELP} component={HelpScreen} />
      <Stack.Screen
        name={SCREEN_NAME.CUSTOMER_SERVICE}
        component={CustomerService}
      />
      <Stack.Screen
        name={SCREEN_NAME.SELLER_STORE}
        component={SellerStorePage}
      />
      <Stack.Screen
        name={SCREEN_NAME.VIEW_ALL_PRODUCT}
        component={ViewAllGiftsScreen}
      />
      <Stack.Screen
        name={SCREEN_NAME.GIFT_PRODUCT}
        component={GiftProductsSection}
      />
      <Stack.Screen
        name={SCREEN_NAME.EXPLORE_ALL}
        component={ExploreAllProducts}
      />
      <Stack.Screen name={SCREEN_NAME.LEGAL} component={LegalScreen} />
    </Stack.Navigator>
  );
};
export default OnboardNavigationStack;
const styles = StyleSheet.create({
  image: {width: 120, height: 60},
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    gap: 8,
    paddingBottom: 10,
    height: 10,
  },
});
