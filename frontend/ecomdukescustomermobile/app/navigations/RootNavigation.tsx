import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootStackParamList} from './types';
import AuthNavigator from './AuthNavigation';
import OnboardNavigationStack from './OnboardingStack';

const Stack = createNativeStackNavigator<RootStackParamList>();

const RootNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      <Stack.Screen name="AuthStack" component={AuthNavigator} />
      <Stack.Screen name="OnboardStack" component={OnboardNavigationStack} />
    </Stack.Navigator>
  );
};

export default RootNavigator;
