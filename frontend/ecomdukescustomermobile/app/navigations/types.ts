import {NavigatorScreenParams} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Collection} from '../redux/product/product';
import {Legals} from '../enums/legal-category.enum';
export type RootStackParamList = {
  AuthStack: NavigatorScreenParams<AuthStackParamList>;
  OnboardStack: NavigatorScreenParams<OnboardStackParamList>;
};

export type AuthStackParamList = {
  login: undefined;
  register: undefined;
  forgotPassword: undefined;
  checkMail: undefined;
  home: undefined;
};
export type OnboardStackParamList = {
  mainHome: {screen: string};
  demo: undefined;
  home: undefined;
  productDetails: {
    searchQuery?: string;
    collectionId?: string[];
    facetValueIds?: string[];
  };
  category: undefined;
  profile: undefined;
  products: {productId: string};
  suggestion: {searchQuery?: string};
  orderConfirm: undefined;
  orderSuccess: {orderId?: string};
  wishlist: undefined;
  myOrder: undefined;
  referral: undefined;
  address: undefined;
  faq: undefined;
  sidebar: undefined;
  terms: undefined;
  privacy: undefined;
  refundPolicy: undefined;
  message: {sellerId?: string};
  changePassword: undefined;
  chatList: undefined;
  help: undefined;
  customerService: undefined;
  sellerStore: {sellerId?: string};
  giftProduct: undefined;
  viewAllGift: {
    type: string;
    title: string;
    variants?: any[];
    allCollections?: Collection[];
  };
  allCategories: undefined;
  exploreAll: undefined;
  legal: {title: string; type: Legals};
};
export type MainStackParamList = {
  mainHome: undefined;
};
export type LoginScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'login'
>;

export type RegisterScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'register'
>;
export type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'forgotPassword'
>;
export type checkMailScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'checkMail'
>;
export type DemoScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'demo'
>;
export type ProductListScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'productDetails'
>;
export type CategoryScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'category'
>;

export type ProfileScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'profile'
>;
export type ProductScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'products'
>;
export type SuggestionScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'suggestion'
>;
export type OrderConfirmScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'orderConfirm'
>;
export type OrderSuccessScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'orderSuccess'
>;
export type HomeScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'home'
>;
export type wishlistScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'wishlist'
>;
export type MyOrderScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'myOrder'
>;
export type ReferralScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'referral'
>;
export type AddressScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'address'
>;
export type FAQScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'faq'
>;
export type TermsNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'terms'
>;
export type PrivacyPolicyNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'privacy'
>;
export type RefunPolicyNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'refundPolicy'
>;
export type SidebarNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'sidebar'
>;
export type MessageNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'message'
>;
export type ChangePasswordScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'changePassword'
>;
export type ChatNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'chatList'
>;
export type HelpNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'help'
>;
export type CustomerServiceNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'customerService'
>;
export type SellerStoreNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'sellerStore'
>;
export type GiftProductNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'giftProduct'
>;
export type viewAllGiftNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'viewAllGift'
>;
export type AllCategoriesNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'allCategories'
>;
export type ExploreAllNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'exploreAll'
>;
export type LegalNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'legal'
>;
