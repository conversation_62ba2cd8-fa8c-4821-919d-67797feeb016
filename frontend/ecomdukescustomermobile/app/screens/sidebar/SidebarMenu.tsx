import React, {useState} from 'react';
import {
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
  UIManager,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {Icon} from 'react-native-paper';
import {menuItems} from '../../constants/menuData';
import {colors} from '../../theme/colors';

if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental?.(true);
}

import {NavigationProp} from '@react-navigation/native';
import {useGetUserQuery} from '../../redux/auth/authApiSlice';
import customColors from '../../theme/customColors';
import {SCREEN_NAME} from '../../constants/screenNames';
import SwitchAccountModal from '../main/switch-account/SwitchAccount';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Account} from '../../utils/AccountStorage';
import {RootStackParamList} from '../../navigations/types';

const MenuItem = ({
  item,
  level = 0,
  navigation,
  onSwitchAccount,
}: {
  item: any;
  level?: number;
  onSwitchAccount: () => void;
  navigation: NavigationProp<any>;
}) => {
  const [expanded, setExpanded] = useState(false);

  const handlePress = () => {
    if (!item) return;

    if (item.subItems) {
      setExpanded(prev => !prev);
    } else if (item.screen === SCREEN_NAME.SWITCH_ACCOUNT) {
      onSwitchAccount();
    } else if (item.screen) {
      navigation.navigate(item.screen, item.params);
    }
  };

  return (
    <View style={[styles.menuItemWrapper, {paddingLeft: 16 * level}]}>
      <TouchableOpacity onPress={handlePress} style={styles.menuItem}>
        <View style={styles.menuItemContent}>
          <Text
            style={[
              styles.menuLabel,
              level === 0 ? styles.sectionTitle : styles.subLabel,
            ]}>
            {item.label}
          </Text>

          {item.subItems && (
            <Icon
              source={expanded ? 'chevron-down' : 'chevron-right'}
              size={20}
              color={colors.gray.dark}
            />
          )}
        </View>
      </TouchableOpacity>

      {expanded && Array.isArray(item.subItems) && (
        <>
          {item.subItems.map((subItem: {label: any}, idx: any) => {
            if (!subItem || !subItem.label) {
              return null;
            }
            return (
              <MenuItem
                key={`${item.label}-${subItem.label}-${idx}`}
                item={subItem}
                level={level + 1}
                navigation={navigation}
                onSwitchAccount={onSwitchAccount}
              />
            );
          })}
        </>
      )}
    </View>
  );
};

const SidebarMenu = () => {
  // const navigation = useNavigation() as NavigationProp<any>;
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {data: user} = useGetUserQuery();
  const [showSwitchAccountModal, setShowSwitchAccountModal] = useState(false);
  const getInitials = (firstName?: string, lastName?: string) => {
    const first = typeof firstName === 'string' ? firstName.charAt(0) : '';
    const last = typeof lastName === 'string' ? lastName.charAt(0) : '';
    return (first + last).toUpperCase();
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.profileView}>
        <View style={styles.profileContainer}>
          <View style={styles.profileImageWrapper}>
            {user?.photoUrl &&
            user.photoUrl !== 'null' &&
            user.photoUrl.trim() !== '' ? (
              <Image
                source={{uri: user.photoUrl}}
                style={styles.profileImage}
                onError={() => console.warn('Profile image failed to load')}
              />
            ) : (
              <View style={styles.initialsContainer}>
                <Text style={styles.initialsText}>
                  {getInitials(user?.firstName, user?.lastName)}
                </Text>
              </View>
            )}
          </View>
          <View>
            <Text style={styles.helloText}>Hello,</Text>
            <Text style={styles.nameText}>
              {user?.firstName} {user?.lastName}
            </Text>
          </View>
        </View>

        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <MenuItem
              onSwitchAccount={() => setShowSwitchAccountModal(true)}
              key={`${item.label}-${item.screen ?? index}`}
              item={item}
              navigation={navigation}
            />
          ))}
        </View>
      </View>
      <SwitchAccountModal
        visible={showSwitchAccountModal}
        onClose={() => setShowSwitchAccountModal(false)}
        onAddAccount={() => {
          console.log('onAddAccount triggered from SwitchAccountModal');

          navigation.navigate('AuthStack', {
            screen: SCREEN_NAME.LOGIN,
            params: {
              onLoginSuccess: (account: Account) => {
                console.log('login success callback triggered', account);
              },
            },
          });
        }}
        onAccountSwitch={account => {
          console.log('Switched to', account.email);
        }}
      />
    </ScrollView>
  );
};

export default SidebarMenu;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: customColors.white,
    padding: 20,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  helloText: {
    fontSize: 16,
    color: customColors.textBlack,
    fontWeight: 'bold',
  },
  nameText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
  menuContainer: {
    paddingHorizontal: 16,
  },
  menuItemWrapper: {
    marginVertical: 4,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  menuLabel: {
    fontSize: 15,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
  subLabel: {
    color: customColors.textBlack,
  },
  profileView: {
    padding: 10,
    backgroundColor: customColors.white,
    borderRadius: 8,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  profileImageWrapper: {
    width: 40,
    height: 40,
    borderRadius: 30,
    overflow: 'hidden',
    backgroundColor: customColors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  initialsContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  initialsText: {
    fontSize: 22,
    color: customColors.white,
    fontWeight: 'bold',
  },
});
