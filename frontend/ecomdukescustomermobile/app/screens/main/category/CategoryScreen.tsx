import React from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {Text} from 'react-native-paper';

const CategoryScreen = () => {
  return (
    <View style={styles.mainContainer}>
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.whiteCard}>
          <Text>Cart Screen</Text>
        </View>
      </ScrollView>
    </View>
  );
};
export default CategoryScreen;
const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.gray.elevation,
    padding: 15,
  },
  whiteCard: {
    backgroundColor: customColors.white,
    borderRadius: 12,
    padding: 12,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    gap: 20,
  },

  container: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.gray.elevation,
  },
  greetingCard: {
    borderRadius: 10,
    marginBottom: 20,
    padding: 30,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: colors.gray.card,
  },
});
