import React, {useEffect, useRef, useState} from 'react';
import {View, ScrollView, StyleSheet, TouchableOpacity} from 'react-native';
import {
  Card,
  TextInput,
  Button,
  Text,
  IconButton,
  Icon,
} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {
  useCreateCustomerAddressMutation,
  useDeleteCustomerByIdMutation,
  useGetAddressesQuery,
  useUpdateCustomerByIdMutation,
} from '../../../redux/customer/customerApiSlice';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {FlatList} from 'react-native-gesture-handler';
import {AddressDto} from '../../../types/customerApi';
import Toast from 'react-native-toast-message';

const AddressScreen = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<AddressDto | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const [formPositionY, setFormPositionY] = useState(0);
  const [addressType, setAddressType] = useState('Home');
  const [createCustomer] = useCreateCustomerAddressMutation();
  const [updateCustomer] = useUpdateCustomerByIdMutation();

  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;
  const [deleteCustomer] = useDeleteCustomerByIdMutation();

  const {data: addresses = [], refetch: refetchAddresses} =
    useGetAddressesQuery(customerId ?? '', {
      skip: !customerId,
    });
  const [formData, setFormData] = useState({
    name: '',
    phoneNumber: '',
    zipCode: '',
    locality: '',
    addressLine1: '',
    addressLine2: '',
    state: '',
    city: '',
    landmark: '',
    alternativePhoneNumber: '',
    country: '',
    addressType: '',
  });
  useEffect(() => {
    if (editingAddress) {
      setFormData({
        name: editingAddress.name,
        phoneNumber: editingAddress.phoneNumber,
        zipCode: editingAddress.zipCode,
        locality: editingAddress.locality,
        addressLine1: editingAddress.addressLine1,
        addressLine2: editingAddress?.addressLine2 ?? '',
        state: editingAddress.state ?? 'YourState',
        city: editingAddress.city,
        landmark: editingAddress.landmark ?? '',
        alternativePhoneNumber: editingAddress.alternativePhoneNumber ?? '',
        country: editingAddress.country ?? 'India',
        addressType: editingAddress.addressType ?? 'Home',
      });
      setAddressType(editingAddress.addressType ?? 'Home');
    } else {
      setFormData({
        name: '',
        phoneNumber: '',
        zipCode: '',
        locality: '',
        addressLine1: '',
        addressLine2: '',
        state: '',
        city: '',
        landmark: '',
        alternativePhoneNumber: '',
        country: '',
        addressType: '',
      });
      setAddressType('Home');
    }
  }, [editingAddress]);
  const handleDeleteAddress = async (id: string) => {
    await deleteCustomer(id).unwrap();
    refetchAddresses();
    Toast.show({
      text1: 'Address deleted successfully',
      type: 'info',
    });
  };
  const handleSaveAddress = async () => {
    // eslint-disable-next-line no-lone-blocks
    {
      if (!customerId) {
        return;
      }
      if (
        !formData.name ||
        !formData.phoneNumber ||
        !formData.zipCode ||
        !formData.locality ||
        !formData.addressLine1 ||
        !formData.city
      ) {
        Toast.show({
          text1: 'Please fill all required fields',
          type: 'error',
        });
        return;
      }

      const payload = {
        name: formData.name,
        phoneNumber: formData.phoneNumber,
        zipCode: formData.zipCode,
        locality: formData.locality,
        addressLine1: formData.addressLine1.trim(),
        addressLine2: formData.addressLine2?.trim() || '',
        city: formData.city,
        state: formData.state,
        country: formData.country,
        landmark: formData.landmark || '',
        alternativePhoneNumber: formData.alternativePhoneNumber || '',
        addressType,
        customerId,
      };

      if (editingAddress) {
        await updateCustomer({
          id: editingAddress.id ?? '',
          data: payload,
        }).unwrap();
        Toast.show({
          text1: 'Address updated successfully',
          type: 'success',
        });
      } else {
        await createCustomer(payload).unwrap();
        Toast.show({
          text1: 'Address created successfully',
          type: 'success',
        });
      }

      setShowForm(false);
      setEditingAddress(null);
      refetchAddresses();
    }
  };

  const renderAddressCard = ({item}: {item: AddressDto}) => (
    <Card style={styles.addressCard}>
      <Card.Content>
        <View style={styles.headerRow}>
          <Text style={styles.tag}>{item.addressType}</Text>
          <View style={styles.subRow}>
            <IconButton
              icon="pencil"
              size={18}
              iconColor={colors.tertiary}
              onPress={() => {
                setEditingAddress(item);
                setShowForm(true);
                setTimeout(() => {
                  scrollViewRef.current?.scrollTo({
                    y: formPositionY,
                    animated: true,
                  });
                }, 100);
              }}
            />
            <IconButton
              icon="trash-can-outline"
              size={20}
              iconColor={colors.tertiary}
              onPress={() =>
                handleDeleteAddress(
                  addresses.find(a => a.id === item.id)?.id ?? '',
                )
              }
            />
          </View>
        </View>

        <View style={styles.addressDetailsContainer}>
          <Text style={styles.name}>{item.name}</Text>
          <Text>
            {item.addressLine1}, {item.addressLine2}
          </Text>
          <Text>
            {item.locality}, {item.city}
          </Text>
          <Text>
            {item.state} - {item.zipCode}
          </Text>
          <Text>{item.country}</Text>
          <Text>Phone: {item.phoneNumber}</Text>
          {item.alternativePhoneNumber ? (
            <Text>Alt Phone: {item.alternativePhoneNumber}</Text>
          ) : null}
          {item.landmark ? <Text>Landmark: {item.landmark}</Text> : null}
        </View>
      </Card.Content>
    </Card>
  );
  return (
    <ScrollView ref={scrollViewRef} contentContainerStyle={styles.content}>
      <View style={styles.card}>
        <TouchableOpacity
          onPress={() => {
            setEditingAddress(null);
            setShowForm(true);
            setTimeout(() => {
              scrollViewRef.current?.scrollTo({
                y: formPositionY,
                animated: true,
              });
            }, 100);
          }}>
          <View style={styles.addAddress}>
            <Icon
              size={20}
              color={colors.gray.dark}
              source={'plus-circle-outline'}
            />
            <Text style={styles.text}>Add Address</Text>
          </View>
        </TouchableOpacity>
        <FlatList
          data={addresses}
          keyExtractor={item => item.id ?? ''}
          renderItem={renderAddressCard}
          contentContainerStyle={styles.flatlist}
        />
        <View onLayout={e => setFormPositionY(e.nativeEvent.layout.y)}>
          {showForm && (
            <Card style={styles.formCard}>
              <Card.Content>
                <Text>{editingAddress ? 'EDIT ADDRESS' : 'ADD ADDRESS'}</Text>
                <Button
                  buttonColor={colors.tertiary}
                  icon="crosshairs-gps"
                  mode="contained"
                  onPress={() => {}}
                  style={styles.locationBtn}>
                  Use my current location
                </Button>
                <TextInput
                  label="Name"
                  mode="outlined"
                  onChangeText={text =>
                    setFormData(prev => ({...prev, name: text}))
                  }
                  activeOutlineColor={colors.tertiary}
                  value={formData.name}
                  style={styles.input}
                />
                <TextInput
                  label="10-digit mobile number"
                  mode="outlined"
                  onChangeText={text =>
                    setFormData(prev => ({...prev, phoneNumber: text}))
                  }
                  value={formData.phoneNumber}
                  keyboardType="phone-pad"
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />

                <TextInput
                  value={formData.zipCode}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, zipCode: text}))
                  }
                  label="Pincode"
                  mode="outlined"
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="Locality"
                  value={formData.locality}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, locality: text}))
                  }
                  mode="outlined"
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="AddressLine 1 (Area & Street)"
                  value={formData.addressLine1}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, addressLine1: text}))
                  }
                  mode="outlined"
                  multiline
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="AddressLine 2 (Area & Street)"
                  value={formData.addressLine2}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, addressLine2: text}))
                  }
                  mode="outlined"
                  multiline
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="City/District/Town"
                  value={formData.city}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, city: text}))
                  }
                  mode="outlined"
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="State/Province/Region"
                  value={formData.state}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, state: text}))
                  }
                  mode="outlined"
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="Country"
                  value={formData.country}
                  onChangeText={text =>
                    setFormData(prev => ({...prev, country: text}))
                  }
                  mode="outlined"
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="Landmark (Optional)"
                  mode="outlined"
                  onChangeText={text =>
                    setFormData(prev => ({...prev, landmark: text}))
                  }
                  value={formData.landmark}
                  activeOutlineColor={colors.tertiary}
                  style={styles.input}
                />
                <TextInput
                  label="Alternate Phone (Optional)"
                  mode="outlined"
                  value={formData.alternativePhoneNumber}
                  onChangeText={number =>
                    setFormData(prev => ({
                      ...prev,
                      alternativePhoneNumber: number,
                    }))
                  }
                  keyboardType="phone-pad"
                  style={styles.input}
                  activeOutlineColor={colors.tertiary}
                />
                <View style={styles.radioGroup}>
                  <Text style={styles.addressType}>Address Type</Text>
                  <View style={styles.radioView}>
                    <TouchableOpacity
                      style={styles.radioOption}
                      onPress={() => {
                        setAddressType('Home');
                        setFormData(prev => ({...prev, addressType: 'Home'}));
                      }}>
                      <Icon
                        size={18}
                        source={
                          addressType === 'Home'
                            ? 'checkbox-blank-circle'
                            : 'checkbox-blank-circle-outline'
                        }
                      />
                      <Text style={styles.radioLabel}>Home</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.radioOption}
                      onPress={() => {
                        setAddressType('Work');
                        setFormData(prev => ({...prev, addressType: 'Work'}));
                      }}>
                      <Icon
                        size={18}
                        source={
                          addressType === 'Work'
                            ? 'checkbox-blank-circle'
                            : 'checkbox-blank-circle-outline'
                        }
                        color={colors.tertiary}
                      />
                      <Text style={styles.radioLabel}>Work</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.actions}>
                  <Button
                    mode="contained"
                    onPress={handleSaveAddress}
                    style={{backgroundColor: colors.tertiary}}>
                    {editingAddress ? 'UPDATE' : 'SAVE'}
                  </Button>
                  <Button
                    onPress={() => {
                      setShowForm(false);
                      setEditingAddress(null);
                    }}
                    textColor="#000">
                    Cancel
                  </Button>
                </View>
              </Card.Content>
            </Card>
          )}
        </View>
      </View>
    </ScrollView>
  );
};
export default AddressScreen;
const styles = StyleSheet.create({
  content: {
    padding: 10,
    backgroundColor: colors.gray.backGround,
    borderRadius: 10,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addressCard: {
    marginBottom: 16,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: customColors.white,
  },
  addCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addCard: {
    backgroundColor: customColors.white,
    marginBottom: 16,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderRadius: 10,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontWeight: 'bold',
    fontSize: 16,
    color: customColors.textBlack,
    marginBottom: 4,
  },
  tag: {
    backgroundColor: colors.tertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    fontSize: 13,
    borderRadius: 10,
    padding: 20,
    fontWeight: 'bold',
    color: customColors.white,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  formCard: {
    backgroundColor: customColors.white,
    borderRadius: 10,
  },
  input: {
    marginVertical: 6,
  },
  locationBtn: {
    marginVertical: 10,
  },
  radioGroup: {
    marginTop: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  radioLabel: {
    marginLeft: 8,
    fontSize: 14,
  },
  addressType: {
    fontWeight: 'bold',
    marginBottom: 20,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  fab: {
    margin: 16,

    backgroundColor: customColors.white,
  },
  addAddress: {
    backgroundColor: customColors.white,
    padding: 18,
    borderRadius: 15,
    marginBottom: 12,
    elevation: 1,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
    borderColor: colors.gray.medium,
  },
  text: {
    color: colors.gray.dark,
    marginVertical: 2,
  },
  flatlist: {padding: 10},
  radioView: {
    flex: 1,
    flexDirection: 'row',
    gap: 50,
  },
  addressDetailsContainer: {
    marginTop: 8,
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
  },
});
