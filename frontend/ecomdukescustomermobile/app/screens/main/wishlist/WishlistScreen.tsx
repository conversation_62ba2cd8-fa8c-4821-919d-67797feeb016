import React, {useCallback, useState} from 'react';
import {View, FlatList, StyleSheet} from 'react-native';
import WishlistCard from './components/WishlistCard';
import customColors from '../../../theme/customColors';
import {ActivityIndicator, Searchbar} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import {
  useGetWishlistsQuery,
  useLazyGetWishlistsQuery,
  useRemoveItemFromWishlistMutation,
} from '../../../redux/wishlist/wishlistApiSlice';
import {fieldsExcludeMetaFields, IFilter} from '../../../types/api';
import CategorySelector from './components/CategorySelector';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {wishlistScreenNavigationProp} from '../../../navigations/types';
import Toast from 'react-native-toast-message';
import {useAddItemToCartMutation} from '../../../redux/cart/cartApiSlice';
import {Text} from 'react-native-gesture-handler';

const WishlistScreen = () => {
  const navigation = useNavigation<wishlistScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [addedToCart, setAddedToCart] = useState<Set<string>>(new Set());

  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);
  const [
    triggerWishlistSearch,
    {data: wishlist, isLoading: isWishlistLoading},
  ] = useLazyGetWishlistsQuery();
  const [addItemToCart] = useAddItemToCartMutation();
  const [removeWishlistItem, {isLoading: isRemovingFromWishlist}] =
    useRemoveItemFromWishlistMutation();
  const handleProductClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };

  const {data: collectionData, isLoading: isCollectionsLoading} =
    useGetWishlistsQuery({
      filter: {
        fields: {
          id: true,
        },
        include: [
          {
            relation: 'productVariant',
            scope: {
              include: [
                {
                  relation: 'product',
                  scope: {
                    include: [
                      {
                        relation: 'collection',
                        scope: {fields: {id: true, name: true}},
                      },
                    ],
                    fields: {id: true},
                  },
                },
              ],
              fields: {id: true},
            },
          },
        ],
      },
    });
  const categories = React.useMemo(() => {
    const uniqueMap = new Map<string, string>();
    collectionData?.forEach(item => {
      const collection = item?.productVariant?.product?.collection;
      if (collection && !uniqueMap.has(collection.id)) {
        uniqueMap.set(collection.id, collection.name);
      }
    });
    return Array.from(uniqueMap.entries());
  }, [collectionData]);
  const buildAndTriggerWishlistSearch = useCallback(() => {
    const productVariantScope: IFilter = {
      where: {
        name: {ilike: `%${searchQuery}%`},
      },
      include: [
        {
          relation: 'product',
          required: true,
          scope: {
            fields: fieldsExcludeMetaFields,
            ...(selectedCollectionId && {
              where: {
                collectionId: selectedCollectionId,
              },
            }),
          },
        },
        {relation: 'reviews'},
        {
          relation: 'productVariantPrice',
          scope: {fields: fieldsExcludeMetaFields},
        },
        {
          relation: 'featuredAsset',
          scope: {fields: fieldsExcludeMetaFields},
        },
      ],
      fields: fieldsExcludeMetaFields,
    };

    const filter = {
      include: [
        {
          relation: 'productVariant',
          required: true,
          scope: productVariantScope,
        },
      ],
      fields: fieldsExcludeMetaFields,
    };

    triggerWishlistSearch({filter});
  }, [searchQuery, selectedCollectionId, triggerWishlistSearch]);
  useFocusEffect(
    useCallback(() => {
      buildAndTriggerWishlistSearch();
    }, [buildAndTriggerWishlistSearch]),
  );

  const handleRemove = async (id: string) => {
    await removeWishlistItem(id).unwrap();
    Toast.show({
      type: 'success',
      text1: 'Deleted from Wishlist',
    });

    const productVariantScope: IFilter = {
      where: {
        name: {ilike: `%${searchQuery}%`},
      },
      include: [
        {
          relation: 'product',
          required: true,
          scope: {
            fields: fieldsExcludeMetaFields,
            ...(selectedCollectionId && {
              where: {
                collectionId: selectedCollectionId,
              },
            }),
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {fields: fieldsExcludeMetaFields},
        },
        {
          relation: 'featuredAsset',
          scope: {fields: fieldsExcludeMetaFields},
        },
      ],
      fields: fieldsExcludeMetaFields,
    };

    const filter = {
      include: [
        {
          relation: 'productVariant',
          scope: productVariantScope,
          required: true,
        },
      ],
      fields: fieldsExcludeMetaFields,
    };

    triggerWishlistSearch({filter});

    const whereClause: any = {
      productVariant: {
        name: {ilike: `%${searchQuery}%`},
      },
    };

    if (selectedCollectionId) {
      whereClause.productVariant.product = {
        collectionId: selectedCollectionId,
      };
    }
  };

  const handleAddToCart = async (product: any) => {
    if (addedToCart.has(product.id)) {
      return;
    }

    await addItemToCart({
      productVariantId: product.id,
      quantity: 1,
    }).unwrap();

    setAddedToCart(prev => new Set(prev).add(product.id));

    Toast.show({
      type: 'success',
      text1: 'Added to cart!',
    });
  };

  const handleGotoCart = useCallback(() => {
    navigation.navigate(SCREEN_NAME.CART);
  }, [navigation]);

  return (
    <View style={styles.container}>
      <CategorySelector
        categories={categories}
        selectedCollectionId={selectedCollectionId}
        isCollectionsLoading={isCollectionsLoading}
        onSelect={id => setSelectedCollectionId(id)}
      />

      <Searchbar
        placeholder="Search products..."
        onChangeText={text => setSearchQuery(text)}
        onSubmitEditing={() => setSearchQuery(searchQuery)}
        onIconPress={() => setSearchQuery(searchQuery)}
        value={searchQuery}
        style={styles.searchbar}
        placeholderTextColor={colors.gray.medium}
      />
      {wishlist && wishlist.length === 0 && !isWishlistLoading ? (
        <View style={styles.noProductView}>
          <Text style={styles.noProductText}>No products in wishlist</Text>
        </View>
      ) : null}
      {(isWishlistLoading || isRemovingFromWishlist) && (
        <View style={styles.fullScreenLoader}>
          <ActivityIndicator
            size="large"
            color={customColors.primaryContainer}
          />
        </View>
      )}
      <FlatList
        data={wishlist}
        keyExtractor={item => item.id?.toString() || ''}
        renderItem={({item}) => {
          const imageUrl = item.productVariant?.featuredAsset.previewUrl || '';
          const title = item.productVariant?.product.name || '';
          const description = item.productVariant?.product.description || '';
          const price = parseFloat(
            item.productVariant?.productVariantPrice.price || '0',
          );
          const mrp = parseFloat(
            item.productVariant?.productVariantPrice.mrp || '0',
          );

          const offerPercentage =
            mrp > 0 && price > 0 && price < mrp
              ? Math.round(((mrp - price) / mrp) * 100)
              : null;

          return (
            <WishlistCard
              title={title}
              description={description}
              deal={offerPercentage !== null ? `${offerPercentage}% Off` : ''}
              rating={item.productVariant?.reviews?.[0]?.rating || 0}
              ratingCount={item.productVariant?.reviews?.length || 0}
              price={price}
              mrp={mrp}
              imageUrl={imageUrl}
              onAddToCart={() => handleAddToCart(item.productVariant)}
              onRemove={() => item.id && handleRemove(item.id)}
              onPress={() => handleProductClick(item.productVariantId)}
              isAddedToCart={addedToCart.has(item.productVariantId)}
              onGoToCart={handleGotoCart}
            />
          );
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: customColors.white, padding: 15},
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
    marginBottom: 20,
  },
  fullScreenLoader: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  noProductView: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  noProductText: {color: customColors.textBlack, fontSize: 16},
});

export default WishlistScreen;
