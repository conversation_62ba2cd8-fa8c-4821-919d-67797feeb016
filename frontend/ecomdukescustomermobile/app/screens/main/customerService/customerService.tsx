import {SafeAreaView, StyleSheet, View} from 'react-native';
import {Text} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {CustomerServiceNavigationProp} from '../../../navigations/types';
interface ScreenProps {
  navigation: CustomerServiceNavigationProp;
}
const CustomerService: React.FC<ScreenProps> = () => (
  <SafeAreaView style={styles.mainContainer}>
    <View style={styles.footerContainer}>
      <View style={styles.footerItem}>
        <Text variant="titleMedium" style={styles.footerLabel}>
          Mail Us:
        </Text>
        <Text variant="titleMedium" style={styles.footerValue}>
          <EMAIL>
        </Text>
      </View>

      <View style={styles.footerItem}>
        <Text variant="titleMedium" style={styles.footerLabel}>
          Contact Through:
        </Text>
        <Text variant="titleMedium" style={styles.footerValue}>
          988766557
        </Text>
      </View>
    </View>
  </SafeAreaView>
);
export default CustomerService;
const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  footerContainer: {
    backgroundColor: colors.background,
    borderColor: colors.gray.light,
    borderTopWidth: 1,
    marginTop: styleConstants.spacing.x20,
    paddingHorizontal: styleConstants.spacing.x20,
    paddingVertical: styleConstants.spacing.x20,
  },

  footerItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: styleConstants.spacing.s10,
  },
  footerLabel: {
    color: customColors.textBlack,
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerText: {
    color: 'black',
    fontSize: 16,
    marginBottom: 5,
  },
  footerValue: {
    color: colors.primary,
    fontSize: 16,
    marginTop: 2,
  },
});
