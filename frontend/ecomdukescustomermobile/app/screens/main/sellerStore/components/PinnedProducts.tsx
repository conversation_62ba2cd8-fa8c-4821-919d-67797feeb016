import React, {useEffect, useMemo} from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  FlatList,
  StyleSheet,
} from 'react-native';
import {useGetPinnedProductsQuery} from '../../../../redux/pinnedProductApiSlice';
import {IFilter} from '../../../../types/api';
import {useLazyGetProductVariantsQuery} from '../../../../redux/product/productApiSlice';
import {ProductVariant} from '../../../../redux/product/product';
import ProductCard from '../../Products/Components/ProductCard';
interface PinnedProductsProps {
  sellerId?: string;
  handleProductPress: (id: string) => void;
}
const PinnedProducts: React.FC<PinnedProductsProps> = ({
  sellerId,
  handleProductPress,
}) => {
  const {
    data: pinnedProductsData,
    isLoading: pinnedLoading,
    isError: pinnedError,
  } = useGetPinnedProductsQuery({where: {sellerId}}, {skip: !sellerId});

  const pinnedVariantIds = useMemo(
    () =>
      pinnedProductsData?.map(p => p.productVariantId).filter(Boolean) ?? [],
    [pinnedProductsData],
  );

  const filter: IFilter = useMemo(
    () => ({
      where: {
        id: {inq: pinnedVariantIds.length ? pinnedVariantIds : ['']},
      },
      include: [
        {
          relation: 'product',
          scope: {
            where: {sellerId},
            include: [{relation: 'featuredAsset'}, {relation: 'collection'}],
          },
        },
        {relation: 'product'},
        {relation: 'featuredAsset'},
      ],
    }),
    [pinnedVariantIds, sellerId],
  );

  const [
    triggerGetProductVariants,
    {data: products, isLoading: pvLoading, isError: pvError},
  ] = useLazyGetProductVariantsQuery();

  useEffect(() => {
    if (pinnedVariantIds.length) triggerGetProductVariants(filter);
  }, [filter, pinnedVariantIds.length, triggerGetProductVariants]);

  const filteredVariants: ProductVariant[] =
    products?.filter(pv => pv.product && pv.product.sellerId === sellerId) ||
    [];

  //   const navigation = useNavigation();
  const calculateDiscount = (
    mrp?: number,
    price?: number,
  ): string | undefined => {
    if (
      typeof mrp !== 'number' ||
      typeof price !== 'number' ||
      mrp <= price ||
      mrp === 0
    ) {
      return undefined;
    }

    const discountPercent = ((mrp - price) / mrp) * 100;
    return `${Math.round(discountPercent)}%`;
  };
  //   const handleCardPress = (variantId: string) => {
  //     navigation.navigate(SCREEN_NAME.PRODUCT_DETAILS, {id: variantId});
  //   };
  if (pinnedLoading) {
    return (
      <View style={styles.stateWrapper}>
        <ActivityIndicator size="small" />
        <Text style={styles.stateText}>Loading pinned products…</Text>
      </View>
    );
  }

  if (pinnedError) {
    return (
      <View style={styles.stateWrapper}>
        <Text style={[styles.stateText, {color: 'red'}]}>
          Failed to load pinned products.
        </Text>
      </View>
    );
  }

  if (!pinnedVariantIds.length) {
    return (
      <View style={styles.stateWrapper}>
        <Text style={styles.stateText}>No pinned products found.</Text>
      </View>
    );
  }

  if (pvLoading) {
    return (
      <View style={styles.stateWrapper}>
        <ActivityIndicator size="small" />
        <Text style={styles.stateText}>Loading product variants…</Text>
      </View>
    );
  }

  if (pvError) {
    return (
      <View style={styles.stateWrapper}>
        <Text style={[styles.stateText, {color: 'red'}]}>
          Failed to load product variants.
        </Text>
      </View>
    );
  }

  /* ---------------------------- main section ----------------------------- */
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Pinned Products</Text>

      {filteredVariants.length ? (
        <FlatList
          data={filteredVariants}
          keyExtractor={item => item.id}
          renderItem={({item}) => (
            <ProductCard
              onPress={() => handleProductPress(item.id)}
              id={Number(item.id)}
              image={item.featuredAsset?.previewUrl}
              name={item.name}
              //   price={item.productVariantPrice?.price || '90'}
              //   originalPrice={item.productVariantPrice.mrp || '80'}
              //   discount={`${calculateDiscount(
              //     parseFloat(item.productVariantPrice.mrp),
              //     parseFloat(item.productVariantPrice.price),
              //   )}%`}
              rating={4}
              isWishlisted={false}
              onWishlistToggle={() => {}}
              shortDescription={item.sku}
              isAddedToCart={false}
              onAddToCart={() => {}}
              isloading={false}
            />
          )}
          numColumns={2}
          columnWrapperStyle={styles.columnWrapper}
          contentContainerStyle={{rowGap: 16}}
          scrollEnabled={false}
        />
      ) : (
        <View style={styles.stateWrapper}>
          <Text style={styles.stateText}>No pinned products available.</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 12,
    color: '#000',
  },
  columnWrapper: {
    justifyContent: 'space-between',
  },
  cardWrap: {
    flex: 1,
    marginBottom: 8,
  },
  stateWrapper: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  stateText: {
    marginTop: 6,
    fontSize: 14,
    color: '#555',
  },
});

export default PinnedProducts;
