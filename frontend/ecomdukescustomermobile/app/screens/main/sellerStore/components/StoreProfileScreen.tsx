import React, {useState} from 'react';
import {View, Text, StyleSheet, Share, Linking} from 'react-native';
import {Button, IconButton, Menu, useTheme, Icon} from 'react-native-paper';
import {ChatDto} from '../../../../types/chat';
import {colors} from '../../../../theme/colors';
import CustomButton from '../../../../components/CustomButton/CustomButton';

interface StoreProfileSectionProps {
  allowBulkOrder?: boolean;
  sellerId: string;
  phoneNumber?: number;
  email?: string;
  onChatCreated?: (chat: ChatDto) => void;
  onMessageSeller?: () => void;
  description: string;
  rating: number;
}

const StoreProfileSection: React.FC<StoreProfileSectionProps> = ({
  allowBulkOrder,
  sellerId,
  phoneNumber,
  email,
  onMessageSeller,
  description,
  rating,
}) => {
  const theme = useTheme();
  const [contactVisible, setContactVisible] = useState(false);

  const storeLink = `https://demo.ecomdukes.in/seller-stores/${sellerId}`;

  const handleSystemShare = async () => {
    const result = await Share.share({
      message: `Hey! 👋 Check out this amazing store on Ecomdukes:\n${storeLink}`,
    });

    if (result.action === Share.sharedAction) {
    } else if (result.action === Share.dismissedAction) {
    }
  };
  const handleEmailPress = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const handlePhonePress = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  interface StarRatingProps {
    rating: number;
    size?: number;
    color?: string;
  }

  // eslint-disable-next-line react/no-unstable-nested-components
  const StarRating: React.FC<StarRatingProps> = ({
    rating,
    size = 20,
    color = colors.tertiary,
  }) => {
    const fullStars = Math.floor(rating);
    const halfStar = rating - fullStars >= 0.5;
    const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

    return (
      <View style={styles.row}>
        {[...Array(fullStars)].map((_, i) => (
          <Icon key={`full-${i}`} source="star" size={size} color={color} />
        ))}
        {halfStar && <Icon source="star-half" size={size} color={color} />}
        {[...Array(emptyStars)].map((_, i) => (
          <Icon
            key={`empty-${i}`}
            source="star-outline"
            size={size}
            color={color}
          />
        ))}
      </View>
    );
  };
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>About store</Text>
          <View style={styles.ratingRow}>
            <Text style={styles.ratingLabel}>Store rating :</Text>
            <StarRating rating={rating} size={18} />
          </View>
        </View>
        <Text style={styles.orderClosed}>
          Order Closed Till :{' '}
          <Text style={{color: theme.colors.primary}}>{}</Text>
        </Text>
      </View>

      <Text style={styles.description}>{description}</Text>

      <View style={styles.actionsRow}>
        <View style={styles.shareGroup}>
          <IconButton
            icon="share-variant"
            size={24}
            style={styles.roundedIcon}
            onPress={handleSystemShare}
          />
          <Text style={styles.shareLabel}>
            Share this store with your friends
          </Text>
        </View>

        {allowBulkOrder && (
          <Button
            mode="contained"
            style={[styles.roundedButton, styles.bulkButton]}
            onPress={() => {}}>
            Custom Bulk Order Request
          </Button>
        )}

        <Menu
          visible={contactVisible}
          onDismiss={() => setContactVisible(false)}
          anchor={
            <CustomButton
              mode="outlined"
              icon="phone"
              onPress={() => setContactVisible(true)}
              title="Contact Details"
            />
          }>
          {email && (
            <Menu.Item
              title={` Email: ${email}`}
              leadingIcon="email"
              onPress={() => handleEmailPress(email)}
            />
          )}
          {phoneNumber && (
            <Menu.Item
              title={` Phone: ${phoneNumber}`}
              leadingIcon="phone"
              onPress={() => handlePhonePress(phoneNumber.toString())}
            />
          )}

          <Menu.Item
            onPress={() => setContactVisible(false)}
            leadingIcon="close"
            title="Close"
          />
        </Menu>

        <View style={{flex: 1}} />

        <CustomButton
          mode="contained"
          icon="message-text"
          style={[styles.messageBtn]}
          onPress={onMessageSeller}
          title="Message Seller"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    backgroundColor: '#FFF',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 40,
    flexShrink: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000845',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#000845',
    marginRight: 4,
  },
  orderClosed: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  description: {
    fontSize: 14,
    color: '#6B6B6B',
    marginBottom: 24,
  },
  actionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    columnGap: 12,
    rowGap: 12,
  },
  shareGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 8,
  },
  roundedIcon: {
    backgroundColor: '#F5F5F5',
    borderRadius: 24,
  },
  shareLabel: {
    fontSize: 16,
    color: '#000845',
    maxWidth: 180,
  },
  roundedButton: {
    borderRadius: 24,
  },
  bulkButton: {
    paddingHorizontal: 24,
    alignSelf: 'flex-start',
  },
  contactBtn: {
    paddingHorizontal: 24,
  },
  messageBtn: {
    backgroundColor: colors.tertiary,
  },
  row: {
    flexDirection: 'row',
  },
});

export default StoreProfileSection;
