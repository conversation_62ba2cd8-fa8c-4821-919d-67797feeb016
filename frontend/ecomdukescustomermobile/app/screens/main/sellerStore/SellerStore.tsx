import React, {useEffect, useMemo} from 'react';
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  FlatList,
  StyleSheet,
} from 'react-native';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';

import {useLazyGetProductVariantsQuery} from '../../../redux/product/productApiSlice';
import {useGetSellerStoreByIdQuery} from '../../../redux/sellerStore/sellerStoreApiSlice';
import PinnedProducts from './components/PinnedProducts';
import StoreProfileSection from './components/StoreProfileScreen';
import StoreReviews from './components/StoreReview';
import ProductCard from '../Products/Components/ProductCard';
import {
  OnboardStackParamList,
  SellerStoreNavigationProp,
} from '../../../navigations/types';
import StoreNavTabs from './components/StoreNavTabs';
import {SCREEN_NAME} from '../../../constants/screenNames';

interface IFilter {
  include?: {
    relation: string;
    scope?: {
      where: {
        sellerId: string;
      };
    };
  }[];
}

export default function SellerStorePage() {
  const navigation = useNavigation<SellerStoreNavigationProp>();

  const route = useRoute<RouteProp<OnboardStackParamList, 'sellerStore'>>();
  const {sellerId} = route.params;

  const calculateDiscount = (
    mrp?: number,
    price?: number,
  ): string | undefined => {
    if (
      typeof mrp !== 'number' ||
      typeof price !== 'number' ||
      mrp <= price ||
      mrp === 0
    ) {
      return undefined;
    }

    const discountPercent = ((mrp - price) / mrp) * 100;
    return `${Math.round(discountPercent)}%`;
  };
  const {data: storeData, isLoading: storeLoading} = useGetSellerStoreByIdQuery(
    sellerId ?? '',
  );

  const [
    triggerGetProductVariants,
    {
      data: productVariants,
      isLoading: productVariantsLoading,
      isError: productVariantsError,
    },
  ] = useLazyGetProductVariantsQuery();

  const filter: IFilter = useMemo(() => {
    if (!sellerId) {
      return {include: []};
    }
    return {
      include: [
        {
          relation: 'product',
          scope: {
            where: {
              sellerId: sellerId as string,
            },
            include: [{relation: 'collection'}],
          },
          required: true,
        },

        {relation: 'productVariantPrice'},
        {relation: 'reviews'},
        {relation: 'featuredAsset'},
      ],
    };
  }, [sellerId]);

  useEffect(() => {
    if (sellerId) {
      triggerGetProductVariants(filter);
    }
  }, [sellerId, triggerGetProductVariants, filter]);

  if (storeLoading) {
    return (
      <View style={sellerStoreStyles.loadingContainer}>
        {' '}
        <ActivityIndicator size="large" />
        <Text style={{marginTop: 8}}>Loading store...</Text>
      </View>
    );
  }
  const collectionMap = new Map<string, string>();

  productVariants?.forEach(variant => {
    const collectionId = variant.product?.collectionId;
    const collectionName = variant.product?.collection?.name;
    if (collectionId && collectionName && !collectionMap.has(collectionId)) {
      collectionMap.set(collectionId, collectionName);
    }
  });

  const categories = ['All Products', ...Array.from(collectionMap.values())];
  const handleProductClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };

  const renderProductCard = ({item}: any) => (
    <ProductCard
      onPress={() => handleProductClick(item.id)}
      id={item.id}
      image={item.featuredAsset?.previewUrl || ''}
      name={item.name}
      price={item.productVariantPrice?.price?.toString()}
      originalPrice={item.productVariantPrice?.mrp?.toString()}
      discount={calculateDiscount(
        Number(item.productVariantPrice?.mrp),
        Number(item.productVariantPrice?.price),
      )}
      rating={item.reviews?.[0]?.rating || 4}
      shortDescription={item.product?.description}
      isWishlisted={false}
      onWishlistToggle={() => {
        handleProductClick(item.id);
      }}
      onAddToCart={() => {
        handleProductClick(item.id);
      }}
      onGoToCart={() => {
        handleProductClick(item.id);
      }}
      isloading={false}
      isAddedToCart={false}
    />
  );
  const handleMessageSeller = async () => {
    navigation.navigate(SCREEN_NAME.MESSAGE, {sellerId: sellerId ?? ''});
  };

  return (
    <ScrollView contentContainerStyle={{paddingBottom: 80}}>
      <StoreNavTabs
        storeData={{
          banner:
            typeof storeData?.banner === 'string'
              ? storeData.banner
              : storeData?.banner?.path || '',
          logo:
            typeof storeData?.logo === 'string'
              ? storeData.logo
              : storeData?.logo?.path || '',
          dp:
            typeof storeData?.dp === 'string'
              ? storeData.dp
              : storeData?.dp?.path || '',
          storeName: storeData?.storeName,
          description: storeData?.description,
          allowCategorisation: storeData?.allowCategorisation,
        }}
        categories={categories}
      />

      <StoreProfileSection
        description={storeData?.description ? storeData.description : ''}
        allowBulkOrder={storeData?.allowBulkOrder}
        sellerId={sellerId ? sellerId : ''}
        email={storeData?.email}
        phoneNumber={storeData?.phoneNumber}
        onMessageSeller={handleMessageSeller}
        rating={5}
      />

      <PinnedProducts
        handleProductPress={handleProductClick}
        sellerId={sellerId}
      />

      <View style={sellerStoreStyles.allProductsContainer}>
        <Text style={{fontSize: 22, fontWeight: '700', marginBottom: 12}}>
          All Products
        </Text>

        {!productVariants?.length ? (
          <Text>No Products are there</Text>
        ) : (
          <FlatList
            data={productVariants}
            keyExtractor={item => item.id}
            renderItem={renderProductCard}
            numColumns={2}
            columnWrapperStyle={sellerStoreStyles.columnWrapper}
            contentContainerStyle={{rowGap: 16}}
            scrollEnabled={false}
          />
        )}
      </View>

      <StoreReviews
        productVariants={(productVariants || []).map(variant => ({
          ...variant,
          reviews: (variant.reviews || [])
            .filter(review => review.id !== undefined)
            .map(review => ({
              id: review.id as string,
              rating: review.rating,
              review: review.review || '',
              reviewAssets: review.reviewAssets || [],
              previewAssets: review.previewAssets || [],
              createdOn: review?.createdOn,
            })),
        }))}
        isLoading={productVariantsLoading}
        isError={productVariantsError}
      />
    </ScrollView>
  );
}
const sellerStoreStyles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  allProductsContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#000',
    marginBottom: 12,
  },
  columnWrapper: {
    justifyContent: 'space-between',
  },
  noProductWrapper: {
    marginVertical: 32,
    alignItems: 'center',
  },
});
