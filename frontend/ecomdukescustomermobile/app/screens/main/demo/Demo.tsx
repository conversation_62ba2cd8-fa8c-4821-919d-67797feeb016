import React, {useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {Card, Searchbar} from 'react-native-paper';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {OnboardStackParamList} from '../../../navigations/types';

const Demo = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const navigation =
    useNavigation<NativeStackNavigationProp<OnboardStackParamList>>();

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSubmitSearch = (searchQuery: string) => {
    if (searchQuery.trim() !== '') {
      navigation.navigate(SCREEN_NAME.PRODUCT_DETAILS, {
        searchQuery,
      });
      setSearchQuery('');
    }
  };
  return (
    <View style={styles.container}>
      <Card.Content style={styles.content}>
        <Searchbar
          placeholder="Search products..."
          onChangeText={setSearchQuery}
          onSubmitEditing={() => onSubmitSearch(searchQuery)}
          value={searchQuery}
          style={styles.searchbar}
          placeholderTextColor={colors.gray.medium}
        />
      </Card.Content>
    </View>
  );
};

export default Demo;

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  searchbar: {
    flex: 1,
    height: 60,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: customColors.textBlack,
    backgroundColor: customColors.white,
  },
});
