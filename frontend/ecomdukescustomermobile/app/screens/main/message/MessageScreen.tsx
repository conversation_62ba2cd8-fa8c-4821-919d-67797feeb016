import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {KeyboardAvoidingView, Platform, StyleSheet, View} from 'react-native';
import {
  Bubble,
  BubbleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
} from 'react-native-gifted-chat';
import {Icon} from 'react-native-paper';
import {SafeAreaView} from 'react-native-safe-area-context';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {
  MessageNavigationProp,
  OnboardStackParamList,
} from '../../../navigations/types';
import {ChatHeader} from './components/ChatHeader';
import {RouteProp, useRoute} from '@react-navigation/native';
import {
  useCreateChatMutation,
  useGetChatMessagesQuery,
  useGetUserChatsQuery,
  useSendChatMessageMutation,
} from '../../../redux/chat/chatApiSlice';
import {ChatDto, ChatMessage} from '../../../types/chat';
import {useTypedSelector} from '../../../redux/store';
import FullScreenLoader from '../../../components/FullScreenLoader';
interface ScreenProps {
  navigation: MessageNavigationProp;
  chat: ChatDto;
}

const MessageScreen: React.FC<ScreenProps> = ({navigation, chat}) => {
  const route = useRoute<RouteProp<OnboardStackParamList, 'message'>>();
  const {sellerId} = route.params;
  const [chatId, setChatId] = useState<string | null>(chat?.id || null);

  const [search] = useState('');

  const {
    data: userChats = [],
    isLoading: userChatLoading,
    refetch: refetchChats,
  } = useGetUserChatsQuery({
    search,
  });

  const [createChat, {isLoading: createChatLoading}] = useCreateChatMutation();
  useEffect(() => {
    if (!sellerId || !userChats.length) return;

    const existingChat = userChats.find(
      (chat: ChatDto) => chat.sellerId === sellerId,
    );

    if (existingChat?.id && chatId !== existingChat.id) {
      setChatId(existingChat.id);
    } else if (!existingChat && !chatId) {
      createChat({sellerId})
        .unwrap()
        .then(newChat => {
          setChatId(newChat.id);
          refetchChats();
        })
        .catch(err => console.error('Failed to create chat', err));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sellerId, userChats.length]);
  const {
    data: messageDtos = [],
    refetch: refetchMessages,
    isLoading: chatMessageLoading,
  } = useGetChatMessagesQuery(chatId ?? '', {
    skip: !chatId,
    pollingInterval: 5000,
  });
  const [sendChatMessage] = useSendChatMessageMutation();

  const user = userChats[0]?.userData?.userTenant?.user;

  const fullName = [user?.firstName, user?.lastName].filter(Boolean).join(' ');
  const avatarUrl = user?.photoUrl || '';

  const customerId = useTypedSelector(s => s?.auth.userDetails?.id) ?? 'me';
  const giftedMessages: IMessage[] = useMemo(() => {
    return messageDtos
      .map(dto => mapToGifted(dto, customerId))
      .sort((a, b) => a.createdAt.valueOf() - b.createdAt.valueOf()); // oldest first
  }, [messageDtos, customerId]);

  const [localMsgs, setLocalMsgs] = useState<IMessage[]>([]);
  useEffect(() => setLocalMsgs([]), [chatId]);
  const mergedMsgs = GiftedChat.append(giftedMessages, localMsgs);
  const onSend = useCallback(
    async (newMessages: IMessage[] = []) => {
      if (!chatId || !newMessages.length) return;

      const message = newMessages[0];
      const text = message.text;

      setLocalMsgs(prev => GiftedChat.append(prev, [message]));

      await sendChatMessage({
        chatId,
        message: {message: text},
      }).unwrap();
    },
    [chatId, sendChatMessage],
  );
  //
  const isAnyLoading =
    chatMessageLoading || userChatLoading || createChatLoading;
  return (
    <SafeAreaView style={styles.safeArea}>
      {isAnyLoading && <FullScreenLoader />}
      <ChatHeader
        onBackPress={() => navigation.goBack()}
        user={{
          name: fullName,
          avatar: avatarUrl,
          online: false,
        }}
      />

      <GiftedChat
        messages={mergedMsgs}
        onSend={onSend}
        user={{_id: customerId}}
        renderBubble={renderBubble}
        renderSend={renderSend}
        renderInputToolbar={renderInputToolbar}
        renderAvatar={null}
        placeholder="Type your message..."
        alwaysShowSend
        inverted={false}
        bottomOffset={Platform.OS === 'ios' ? 20 : 0}
      />
      {Platform.OS === 'android' ? null : (
        <KeyboardAvoidingView behavior="padding" keyboardVerticalOffset={80} />
      )}
    </SafeAreaView>
  );
};
export default MessageScreen;
const mapToGifted = (dto: ChatMessage, customerId: string): IMessage => ({
  _id: dto.id,
  text: dto.message,
  createdAt: new Date(dto.createdOn),
  user: {
    _id: dto.senderId === customerId ? customerId : dto.senderId,
    name: dto.sender?.firstName ?? '',
    avatar: dto.sender?.presignedPhotoUrl ?? '',
  },
});
const renderBubble = (
  props: React.JSX.IntrinsicAttributes & BubbleProps<IMessage>,
) => (
  <Bubble
    {...props}
    wrapperStyle={{
      left: {backgroundColor: colors.gray.backGround, marginBottom: 8},
      right: {backgroundColor: colors.tertiary, marginBottom: 8},
    }}
    textStyle={{
      right: {color: customColors.white},
      left: {color: customColors.textBlack},
    }}
  />
);

const renderSend = (
  props: React.JSX.IntrinsicAttributes & SendProps<IMessage>,
) => (
  <Send {...props}>
    <View style={styles.sendButton}>
      <Icon source="send" size={25} color={colors.tertiary} />
    </View>
  </Send>
);

const renderInputToolbar = (
  props: React.JSX.IntrinsicAttributes & InputToolbarProps<IMessage>,
) => <InputToolbar {...props} containerStyle={styles.inputToolBar} />;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  sendButton: {marginRight: 20, marginBottom: 5},
  inputToolBar: {
    borderTopColor: colors.gray.dark,
    paddingVertical: 6,
    marginLeft: 20,
  },
});
