import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  KeyboardAvoidingView,
  StyleSheet,
} from 'react-native';
import {ActivityIndicator, Icon, Searchbar} from 'react-native-paper';
import {OnboardStackParamList} from '../../../navigations/types';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useLazyGetSearchSuggestionsQuery} from '../../../redux/search/searchApiSlice';

const SearchSuggestionScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<OnboardStackParamList>>();
  const route = useRoute<RouteProp<OnboardStackParamList, 'suggestion'>>();
  const passedSearchQuery = route.params?.searchQuery ?? '';
  const [searchQuery, setSearchQuery] = useState(passedSearchQuery ?? '');
  const [
    getSearchSuggestions,
    {data: suggestionResults = [], isLoading: suggestionLoading},
  ] = useLazyGetSearchSuggestionsQuery();

  const getFirstWords = (name: string, wordLimit: number) => {
    const words = name.split(' ');
    return words.slice(0, wordLimit).join(' ');
  };
  const combinedData = [
    ...(searchQuery.length > 0
      ? suggestionResults.map(item => ({
          type: 'suggestion',
          text: getFirstWords(item.name, 3),
        }))
      : []),
  ];

  const loadSearchSuggestions = useCallback(() => {
    if (searchQuery.trim() !== '') {
      getSearchSuggestions(searchQuery);
    }
  }, [searchQuery, getSearchSuggestions]);

  useEffect(() => {
    const debounceTimeout = setTimeout(loadSearchSuggestions, 500);
    return () => clearTimeout(debounceTimeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSelectSuggestion = (searchQuery: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCT_DETAILS, {
      searchQuery,
    });
  };

  const onSuggestionClick = (suggestedText: string) => {
    setSearchQuery(suggestedText);
    onSelectSuggestion(suggestedText);
  };

  return (
    <KeyboardAvoidingView style={styles.mainView} behavior="padding">
      <Searchbar
        placeholder="Search products..."
        value={searchQuery}
        style={styles.searchbar}
        autoFocus
        onSubmitEditing={e => onSelectSuggestion(e.nativeEvent.text)}
        onChangeText={text => setSearchQuery(text)}
        placeholderTextColor={colors.gray.medium}
      />
      {suggestionLoading && (
        <View style={styles.loadingWrapper}>
          <ActivityIndicator size="large" color={colors.gray.dark} />
        </View>
      )}
      <FlatList
        data={combinedData}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        renderItem={({item}) => {
          if (item.type === 'header') {
            return <Text style={styles.sectionTitle}>{item.text ?? ''}</Text>;
          }

          return (
            <View style={styles.itemRow}>
              <Icon source="magnify" size={20} color={colors.gray.dark} />
              <TouchableOpacity
                style={styles.itemTextWrapper}
                onPress={() => onSuggestionClick(item.text ?? '')}>
                <Text style={styles.itemText}>{item.text}</Text>
              </TouchableOpacity>
            </View>
          );
        }}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  mainView: {flex: 1, backgroundColor: customColors.white},
  searchbar: {
    marginHorizontal: 10,
    borderRadius: 25,
    marginTop: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
    height: 50,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 8,
  },
  itemTextWrapper: {
    flex: 1,
    marginLeft: 10,
  },
  itemText: {
    fontSize: 16,
    color: customColors.textBlack,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginHorizontal: 10,
    marginTop: 20,
    marginBottom: 5,
    color: customColors.textBlack,
  },
  askSection: {
    marginTop: 20,
    paddingHorizontal: 10,
  },
  askItem: {
    backgroundColor: customColors.borderGrey,
    padding: 10,
    borderRadius: 10,
    marginVertical: 5,
  },
  askText: {
    fontSize: 14,
    color: customColors.white,
  },
  loadingWrapper: {
    marginVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default SearchSuggestionScreen;
