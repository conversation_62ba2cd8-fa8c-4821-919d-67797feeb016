import {
  <PERSON><PERSON>ist,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, Text} from 'react-native-paper';
import styleConstants from '../../../theme/styleConstants';
import {colors} from '../../../theme/colors';
import FAQAccordion from '../../../components/accordion/FAQAccordion';
import customColors from '../../../theme/customColors';
import draftToHtml from 'draftjs-to-html';
import {useFocusEffect} from '@react-navigation/native';
import {useGetFaqsQuery} from '../../../redux/faq/faqApiSlice';
import {FAQScreenNavigationProp} from '../../../navigations/types';
interface ScreenProps {
  navigation: FAQScreenNavigationProp;
}

// // Footer
// const ListFooter = () => (
//   <View style={styles.footerContainer}>
//     <View style={styles.footerItem}>
//       <Text variant="titleMedium" style={styles.footerLabel}>
//         Mail Us:
//       </Text>
//       <Text variant="titleMedium" style={styles.footerValue}>
//         <EMAIL>
//       </Text>
//     </View>

//     <View style={styles.footerItem}>
//       <Text variant="titleMedium" style={styles.footerLabel}>
//         Contact Through:
//       </Text>
//       <Text variant="titleMedium" style={styles.footerValue}>
//         988766557
//       </Text>
//     </View>
//   </View>
// );

const FAQScreen: React.FC<ScreenProps> = () => {
  const {data: faqs = [], isLoading, refetch} = useGetFaqsQuery();

  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = useMemo(() => {
    const unique = Array.from(new Set(faqs.map(item => item.category)));
    return ['All', ...unique];
  }, [faqs]);

  const filteredFaqs = useMemo(() => {
    const sorted = [...faqs].sort((a, b) => b.priority - a.priority);
    return selectedCategory === 'All'
      ? sorted
      : sorted.filter(item => item.category === selectedCategory);
  }, [faqs, selectedCategory]);

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );

  return (
    <SafeAreaView style={styles.mainContainer}>
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={styles.loader}
        />
      ) : (
        <FlatList
          data={filteredFaqs}
          keyExtractor={(item, index) => index.toString()}
          refreshing={isLoading}
          onRefresh={refetch}
          ListHeaderComponent={
            <>
              <View style={styles.headerWrapper}>
                <Text style={styles.headerText}>FAQs</Text>
                <Text style={styles.subHeaderText}>
                  Select a category to view related questions.
                </Text>
              </View>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.categoryScroll}>
                {categories.map(cat => (
                  <TouchableOpacity
                    key={cat}
                    style={[
                      styles.categoryChip,
                      selectedCategory === cat && styles.categoryChipSelected,
                    ]}
                    onPress={() => setSelectedCategory(cat)}>
                    <Text
                      style={[
                        styles.categoryText,
                        selectedCategory === cat && styles.categoryTextSelected,
                      ]}>
                      {cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </>
          }
          renderItem={({item}) => {
            const parsedAnswer = JSON.parse(item.answer);
            const formattedAnswer = draftToHtml(parsedAnswer);

            return (
              <View>
                <FAQAccordion
                  question={item.question}
                  answer={formattedAnswer}
                />
              </View>
            );
          }}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </SafeAreaView>
  );
};

export default FAQScreen;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  emailText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  headerWrapper: {
    paddingHorizontal: 20,
    paddingBottom: 12,
  },
  headerText: {
    fontSize: 22,
    fontWeight: '700',
    color: customColors.textBlack,
  },
  subHeaderText: {
    fontSize: 14,
    color: colors.gray.dark,
    marginTop: 4,
  },
  loader: {
    marginTop: 40,
  },
  footerContainer: {
    backgroundColor: colors.background,
    borderColor: colors.gray.light,
    borderTopWidth: 1,
    marginTop: styleConstants.spacing.x20,
    paddingHorizontal: styleConstants.spacing.x20,
    paddingVertical: styleConstants.spacing.x20,
  },

  footerItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: styleConstants.spacing.s10,
  },
  footerLabel: {
    color: customColors.textBlack,
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerText: {
    color: customColors.textBlack,
    fontSize: 16,
    marginBottom: 5,
  },
  footerValue: {
    color: colors.primary,
    fontSize: 16,
    marginTop: 2,
  },

  listContainer: {
    backgroundColor: colors.background,
    flexGrow: 1,
    padding: styleConstants.spacing.x20,
  },
  phoneText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  categoryScroll: {
    paddingHorizontal: 12,
    paddingBottom: 10,
  },
  categoryChip: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: colors.gray.card,
    marginRight: 10,
  },
  categoryChipSelected: {
    backgroundColor: colors.tertiary,
  },
  categoryText: {
    fontSize: 14,
    color: colors.gray.dark,
  },
  categoryTextSelected: {
    color: customColors.white,
    fontWeight: '600',
  },
});
