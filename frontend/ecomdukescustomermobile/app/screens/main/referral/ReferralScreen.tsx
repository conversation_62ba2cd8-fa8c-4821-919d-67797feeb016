import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import {IconButton} from 'react-native-paper';
import {Images} from '../../../assets/images';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import CustomButton from '../../../components/CustomButton/CustomButton';

const ReferralScreen = () => {
  return (
    <View style={styles.container}>
      <Image
        source={Images.referral}
        style={styles.banner}
        resizeMode="contain"
      />

      <View style={styles.card}>
        <Text style={styles.label}>
          Referral Code : <Text style={styles.code}>6589XT</Text>
        </Text>

        <View style={styles.descriptionBox}>
          <Text style={styles.descriptionText}>
            In publishing and graphic design, Lorem ipsum is a placeholder text
            used to demonstrate layout and content.
          </Text>
        </View>

        <View style={styles.shareRow}>
          <Text style={styles.label}>Share :</Text>
          <IconButton icon="share-variant" size={20} onPress={() => {}} />
        </View>

        <CustomButton
          style={styles.dukesCoinBox}
          title={' Total DukesCoin :250'}
        />
        <Text style={styles.noteText}>
          50% discount is available on Purchase using DukesCoin
        </Text>

        <View style={styles.coinStats}>
          <View style={styles.statBox}>
            <Text style={styles.statText}>Used : 150</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={styles.statText}>Balance : 100</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default ReferralScreen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.gray.backGround, padding: 10},
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  headerTitle: {fontSize: 18, fontWeight: 'bold'},
  banner: {
    width: '100%',
    height: '45%',
    borderRadius: 16,
    marginVertical: 8,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 16,
    marginLeft: 12,
    marginRight: 12,
    padding: 16,
  },
  label: {
    fontWeight: '600',
    fontSize: 14,
    marginBottom: 6,
  },
  code: {
    fontWeight: 'bold',
  },
  descriptionBox: {
    backgroundColor: colors.gray.card,
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
  },
  descriptionText: {
    fontSize: 13,
    color: colors.gray.dark,
  },
  shareRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  dukesCoinBox: {
    backgroundColor: colors.tertiary,
  },
  dukesCoinText: {
    color: customColors.white,
    fontWeight: 'bold',
  },
  coinAmount: {
    color: customColors.white,
  },
  noteText: {
    textAlign: 'center',
    fontSize: 12,
    marginVertical: 8,
    color: colors.gray.dark,
  },
  coinStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  statBox: {
    backgroundColor: colors.gray.medium,
    padding: 10,
    borderRadius: 10,
    width: '48%',
    alignItems: 'center',
  },
  statText: {
    color: customColors.white,
    fontWeight: 'bold',
  },
});
