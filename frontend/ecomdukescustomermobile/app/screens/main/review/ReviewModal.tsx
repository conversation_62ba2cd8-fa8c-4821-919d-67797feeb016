import React, {useEffect, useState} from 'react';
import {
  Modal,
  Portal,
  TextInput,
  Button,
  Text,
  Icon,
  IconButton,
} from 'react-native-paper';
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Platform,
} from 'react-native';
import * as ImagePicker from 'react-native-image-picker';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {
  useCreateReviewMutation,
  useDeleteReviewMutation,
  useGetReviewByIdQuery,
  useUpdateReviewMutation,
} from '../../../redux/review/reviewApiSlice';
import Toast from 'react-native-toast-message';
import {useUploadFileMutation} from '../../../redux/fileApiSlice';
import {OrderLineItem} from '../../../redux/order/order';
import {useDispatch} from 'react-redux';
import {apiSlice} from '../../../redux/apiSlice';
import {ApiTags} from '../../../redux/types';

interface ReviewModalProps {
  visible: boolean;
  onDismiss: () => void;
  initialReviewText?: string;
  initialImage?: string;
  isEditing?: boolean;
  reviewId: string;
  productVariantId: string;
  orderLineItemId: string;
  customerId: string;
  onDeleteSuccess?: () => void;
  refetch?: () => void;
  orderLineItems: OrderLineItem[];
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  visible,
  onDismiss,
  reviewId,
  productVariantId,
  orderLineItemId,
  customerId,
  onDeleteSuccess,
  refetch,
}) => {
  const [reviewText, setReviewText] = useState('');
  const [rating, setRating] = useState(0);
  const [imageUris, setImageUris] = useState<string[]>([]);
  const [existingKeys, setExistingKeys] = useState<string[]>([]);
  const [localFiles, setLocalFiles] = useState<ImagePicker.Asset[]>([]);
  const [uploadFile] = useUploadFileMutation();
  const dispatch = useDispatch();

  const isEditing = !!reviewId;
  const {data: existingReview, refetch: refetchReview} = useGetReviewByIdQuery(
    {id: reviewId!},
    {skip: !reviewId},
  );
  useEffect(() => {
    dispatch(apiSlice.util.invalidateTags([ApiTags.PRODUCT_REVIEWS]));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isEditing && existingReview?.rating) {
      setRating(existingReview.rating);
    }
  }, [isEditing, existingReview?.rating]);

  useEffect(() => {
    const urls = localFiles.map(file => file.uri || '');

    setImageUris([...(existingReview?.previewAssets ?? []), ...urls]);

    return () => {};
  }, [localFiles, existingReview?.previewAssets]);

  useEffect(() => {
    if (visible) {
      if (isEditing && existingReview) {
        setReviewText(existingReview.review || '');
        setRating(existingReview.rating || 0);
        setExistingKeys(existingReview.reviewAssets ?? []);
        setLocalFiles([]);
      } else {
        setReviewText('');
        setRating(0);
        setImageUris([]);
        setExistingKeys([]);
        setLocalFiles([]);
      }
    }
  }, [visible, isEditing, existingReview]);

  const handleImagePick = () => {
    ImagePicker.launchImageLibrary(
      {mediaType: 'photo', selectionLimit: 5},
      async response => {
        if (!response.didCancel && response.assets) {
          const assets = response.assets.filter(
            asset => asset.uri && asset.fileName && asset.type,
          );

          const uploadPromises = assets.map(async asset => {
            const formData = new FormData();
            formData.append('file', {
              uri:
                Platform.OS === 'ios'
                  ? asset.uri
                  : asset.uri?.replace('file://', '') ?? '',
              name: asset.fileName!,
              type: asset.type!,
            });

            return uploadFile(formData)
              .unwrap()
              .then(result => (result?.key ? result.key : ''));
          });

          const uploadedKeys = await Promise.all(uploadPromises);
          const validKeys = uploadedKeys.filter(Boolean);

          setExistingKeys(prev => [...prev, ...validKeys]);
          setLocalFiles(prev => [...prev, ...assets]);
        }
      },
    );
  };

  const [createReview] = useCreateReviewMutation();
  const [updateReview] = useUpdateReviewMutation();
  const [deleteReview] = useDeleteReviewMutation();

  const handleSubmit = async () => {
    const payload = {
      rating: rating ?? 0,
      review: reviewText ?? '',
      reviewAssets: existingKeys,
    };

    if (reviewId) {
      await updateReview({id: reviewId, review: payload}).unwrap();
      Toast.show({text1: 'Review Edited', type: 'success'});
      onDismiss();
    } else {
      await createReview({
        ...payload,
        productVariantId,
        orderLineItemId,
        customerId,
        deleted: false,
        createdOn: undefined,
      }).unwrap();
      Toast.show({text1: 'Review Created', type: 'success'});
      onDismiss();
    }

    if (refetch) {
      refetch();
    }
    await refetchReview();
    onDismiss();
  };

  const handleDelete = async () => {
    if (!reviewId) {
      return;
    }
    await deleteReview(reviewId).unwrap();
    setImageUris([]);
    Toast.show({text1: 'Review Deleted', type: 'success'});

    if (onDeleteSuccess) {
      onDeleteSuccess();
    }
    if (refetch) {
      refetch();
    }
    onDismiss();
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.container}>
        <Text style={styles.heading}>
          {isEditing ? 'Edit Your Review' : 'Write a Review'}
        </Text>

        <View style={styles.starRow}>
          {[1, 2, 3, 4, 5].map(index => (
            <IconButton
              key={index}
              icon={index <= rating ? 'star' : 'star-outline'}
              size={36}
              onPress={() => setRating(index)}
              iconColor={index <= rating ? colors.tertiary : colors.gray.light}
            />
          ))}
        </View>

        <TextInput
          label="Write your review"
          value={reviewText}
          onChangeText={setReviewText}
          multiline
          numberOfLines={3}
          mode="outlined"
          style={styles.textInput}
          activeOutlineColor={colors.tertiary}
        />

        <TouchableOpacity onPress={handleImagePick} style={styles.uploadBox}>
          <Text style={styles.centerAlign}>
            {imageUris.length ? 'Add More Images' : 'Tap to upload image'}
          </Text>
        </TouchableOpacity>

        <View style={styles.imageGrid}>
          {imageUris.map((uri, index) => (
            <View key={index.toString()} style={styles.imageWrapper}>
              <Image
                source={{uri}}
                style={styles.thumbnail}
                resizeMode="cover"
              />
              <TouchableOpacity
                onPress={() => {
                  const newUris = imageUris.filter((_, i) => i !== index);
                  const newLocalFiles = localFiles.filter(
                    (_, i) => i !== index,
                  );
                  setImageUris(newUris);
                  setLocalFiles(newLocalFiles);
                }}
                style={styles.deleteIcon}>
                <Icon size={15} source={'close'} color="white" />
              </TouchableOpacity>
            </View>
          ))}
        </View>

        <View style={styles.buttons}>
          {isEditing && (
            <Button
              onPress={handleDelete}
              mode="outlined"
              style={styles.deleteReview}
              textColor={customColors.primaryContainer}>
              Delete Review
            </Button>
          )}

          <Button
            mode="contained"
            onPress={handleSubmit}
            disabled={rating === 0}>
            {isEditing ? 'Update Review' : 'Submit Review'}
          </Button>
        </View>
      </Modal>
    </Portal>
  );
};

export default ReviewModal;

const styles = StyleSheet.create({
  container: {
    backgroundColor: customColors.white,
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  textInput: {
    marginBottom: 18,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 16,
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  uploadBox: {
    backgroundColor: colors.gray.light,
    padding: 12,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 12,
  },
  imageWrapper: {
    position: 'relative',
    width: 100,
    height: 100,
    marginBottom: 16,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    objectFit: 'cover',
  },
  deleteIcon: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: customColors.textBlack,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
  },
  starRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 8,
  },
  deleteReview: {marginRight: 8},
  heading: {fontWeight: 'bold', marginLeft: 10, fontSize: 18},
  centerAlign: {textAlign: 'center'},
});
