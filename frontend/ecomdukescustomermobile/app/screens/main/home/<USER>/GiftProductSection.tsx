// src/sections/GiftProductsSection.tsx
import React, {useEffect, useMemo} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import {ProductList} from './ProductList';
import {SectionType} from '../../../../enums/page-section-enum';
import {User} from '../../../../types/seller';
import {useLazyGetProductVariantsQuery} from '../../../../redux/product/productApiSlice';
import CustomButton from '../../../../components/CustomButton/CustomButton';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
import {useNavigation} from '@react-navigation/native';
import {GiftProductNavigationProp} from '../../../../navigations/types';
import {SCREEN_NAME} from '../../../../constants/screenNames';
export interface HomeSection {
  id: string | number;
  type: SectionType;
  title: string;
  sectionItems?: Array<{
    id: string | number;
    productVariants?: any[];
  }>;
}
type Props = {
  section: HomeSection;
  isLoggedIn: boolean;
  user?: User;
};

const GiftProductsSection: React.FC<Props> = ({section, isLoggedIn, user}) => {
  const navigation = useNavigation<GiftProductNavigationProp>();

  const staticVariants = useMemo(
    () =>
      (section.sectionItems ?? []).flatMap(item => item.productVariants || []),
    [section.sectionItems],
  );
  const [fetchVariants, {data = [], isLoading}] =
    useLazyGetProductVariantsQuery();

  useEffect(() => {
    if (staticVariants.length === 0) {
      fetchVariants({
        include: [
          {
            relation: 'featuredAsset',
            scope: {fields: {preview: true, id: true}},
          },
          {
            relation: 'product',
            required: true,
            scope: {
              fields: {description: true, id: true},
              where: {isGiftWrapAvailable: true},
            },
          },
          {
            relation: 'productVariantPrice',
            scope: {fields: {price: true, mrp: true, currencyCode: true}},
          },
          ...(isLoggedIn
            ? [
                {
                  relation: 'wishlist',
                  scope: {
                    where: {deleted: false, customerId: user?.id},
                    fields: {id: true},
                  },
                },
              ]
            : []),
          {
            relation: 'reviews',
            scope: {
              fields: {rating: true},
              where: {status: 'APPROVED'},
            },
          },
        ],
        fields: {name: true, id: true, featuredAssetId: true, productId: true},
        limit: 10,
      });
    }
  }, [fetchVariants, staticVariants.length, isLoggedIn, user?.id]);

  const variants = staticVariants.length ? staticVariants : data;

  const renderItem = ({item}: any) => (
    <View style={styles.cardWrapper}>
      <ProductList variants={[item]} />
    </View>
  );

  return (
    <View key={section.id} style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={styles.sectionTitle}>{section.title}</Text>
        <CustomButton
          mode={'outlined'}
          title={'View more'}
          labelStyle={styles.categoryLabel}
          onPress={() =>
            navigation.navigate(SCREEN_NAME.VIEW_ALL_PRODUCT, {
              type: SectionType.GIFT_PRODUCTS,
              title: section.title,
              //   variants: data,
            })
          }
        />
      </View>

      {isLoading && variants.length === 0 ? (
        <ActivityIndicator style={styles.loader} />
      ) : (
        <FlatList
          data={variants}
          keyExtractor={item => String(item.id)}
          renderItem={renderItem}
          horizontal
          showsHorizontalScrollIndicator={false}
        />
      )}
    </View>
  );
};

export default GiftProductsSection;

const styles = StyleSheet.create({
  container: {},
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {fontSize: 20, fontWeight: '700'},
  outlinedBtn: {
    borderWidth: 1,
    borderColor: '#5847F9',
    borderRadius: 4,
  },
  btnText: {color: '#5847F9', fontWeight: '600'},
  loader: {height: 140},
  cardWrapper: {width: 195},
  categoryLabel: {color: customColors.textBlack},
  sectionTitle: {
    marginLeft: 15,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
});
