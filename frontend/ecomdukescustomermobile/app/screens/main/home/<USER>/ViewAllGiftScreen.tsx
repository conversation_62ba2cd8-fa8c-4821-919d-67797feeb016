import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
  Pressable,
} from 'react-native';
import {useTypedSelector} from '../../../../redux/store';
import {User} from '../../../../redux/auth/user.model';
import {useLazyGetProductVariantsQuery} from '../../../../redux/product/productApiSlice';
import {ReviewStatus} from '../../../../redux/product/product';
import {ProductList} from './ProductList';
import {useRoute, RouteProp} from '@react-navigation/native';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
import {SectionType} from '../../../../enums/page-section-enum';

const CARD_GUTTER = 12;
const SCREEN_WIDTH = Dimensions.get('window').width;
const CARD_WIDTH = (SCREEN_WIDTH - CARD_GUTTER * 3) / 2;

const ViewAllGiftsScreen: React.FC = () => {
  type RouteParams = {
    type: string;
    title: string;
    variants?: any[];
    allCollections?: {id: string; name: string}[];
  };

  const route = useRoute<RouteProp<{params: RouteParams}, 'params'>>();
  const {type, title, allCollections} = route.params;
  const variants = route.params?.variants ?? [];
  const isLoggedIn = useTypedSelector(s => s.auth.isLoggedIn);
  const user: User = useTypedSelector(s => s.auth.userDetails)!;
  const [listData, setListData] = useState<any[]>(variants);
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(allCollections?.[0]?.id ?? null);

  const [fetchVariants, {data = [], isLoading}] =
    useLazyGetProductVariantsQuery();

  useEffect(() => {
    let loaderTimeout: NodeJS.Timeout;
    if (type === SectionType.GIFT_PRODUCTS) {
      setCategoryLoading(true);
      fetchVariants({
        include: [
          {
            relation: 'product',
            required: true,
            scope: {
              where: {isGiftWrapAvailable: true},
              fields: {id: true, description: true},
            },
          },
          {relation: 'productVariantPrice'},
          {relation: 'featuredAsset'},
          ...(isLoggedIn
            ? [
                {
                  relation: 'wishlist',
                  scope: {
                    where: {deleted: false, customerId: user?.profileId},
                    fields: {id: true},
                  },
                },
              ]
            : []),
          {
            relation: 'reviews',
            scope: {
              where: {status: ReviewStatus.APPROVED},
              fields: {rating: true},
            },
          },
        ],
        fields: {
          id: true,
          name: true,
          featuredAssetId: true,
          productId: true,
        },
        limit: 100, // or any page size you like
      })
        .unwrap()
        .then(full => setListData(full))
        .finally(() => setCategoryLoading(false));
      return; // skip rest of effect
    }
    if (type === 'all-categories' && selectedCollectionId) {
      setCategoryLoading(true);

      loaderTimeout = setTimeout(() => {
        fetchVariants({
          include: [
            {
              relation: 'product',
              required: true,
              scope: {
                where: {collectionId: selectedCollectionId},
                fields: {id: true, description: true},
              },
            },
            {relation: 'productVariantPrice'},
            {relation: 'featuredAsset'},
            ...(isLoggedIn
              ? [
                  {
                    relation: 'wishlist',
                    scope: {
                      where: {deleted: false, customerId: user?.profileId},
                      fields: {id: true},
                    },
                  },
                ]
              : []),
            {
              relation: 'reviews',
              scope: {
                where: {status: ReviewStatus.APPROVED},
                fields: {rating: true},
              },
            },
          ],
          fields: {
            id: true,
            name: true,
            featuredAssetId: true,
            productId: true,
          },
          limit: 50,
        }).finally(() => {
          setCategoryLoading(false);
        });
      }, 200);
    }

    return () => clearTimeout(loaderTimeout); // cleanup on unmount
  }, [type, selectedCollectionId, fetchVariants, isLoggedIn, user?.profileId]);

  const renderItem = ({item}: any) => (
    <View style={[styles.cardWrapper, {width: CARD_WIDTH}]}>
      <ProductList variants={[item]} />
    </View>
  );
  const renderCollectionChip = ({item}: any) => {
    const isSelected = selectedCollectionId === item.id;
    return (
      <Pressable
        style={[
          styles.collectionBtn,
          isSelected && styles.collectionBtnSelected,
        ]}
        android_ripple={{color: '#DDD'}}
        onPress={() => setSelectedCollectionId(item.id)}>
        <Text style={[styles.collectionText, isSelected && {color: '#FFF'}]}>
          {item.name}
        </Text>
      </Pressable>
    );
  };
  const [categoryLoading, setCategoryLoading] = useState(false);

  return (
    <View style={styles.container}>
      <Text style={styles.pageTitle}>{title}</Text>
      {allCollections && (
        <FlatList
          data={allCollections}
          keyExtractor={item => item.id}
          renderItem={renderCollectionChip}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.chipContainer}
        />
      )}
      {categoryLoading || (isLoading && data.length === 0) ? (
        <ActivityIndicator size="large" style={{marginTop: 40}} />
      ) : (
        <FlatList
          data={listData}
          keyExtractor={item => String(item.id)}
          renderItem={renderItem}
          numColumns={2}
          columnWrapperStyle={{gap: CARD_GUTTER}}
          contentContainerStyle={{paddingHorizontal: CARD_GUTTER}}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <Text style={{textAlign: 'center', marginTop: 40}}>
              No products found.
            </Text>
          }
        />
      )}
    </View>
  );
};

export default ViewAllGiftsScreen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: '#FFF'},
  pageTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginVertical: 16,
    marginLeft: 16,
  },
  cardWrapper: {
    marginBottom: CARD_GUTTER,
  },
  collectionBtn: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    height: 40,
    borderRadius: 20,
    backgroundColor: customColors.white,
    borderWidth: 1,
    borderColor: colors.gray.light,
    justifyContent: 'center',
  },
  collectionBtnSelected: {
    backgroundColor: colors.primary,
  },
  collectionText: {
    fontSize: 14,
    color: customColors.textBlack,
  },
  chipContainer: {
    paddingHorizontal: 16,
    marginBottom: 10,
    gap: 6,
  },
});
