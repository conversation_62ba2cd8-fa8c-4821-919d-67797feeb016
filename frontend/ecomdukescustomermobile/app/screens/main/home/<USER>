import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  FlatList,
} from 'react-native';
import {Searchbar} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {HomeScreenNavigationProp} from '../../../navigations/types';
import {useNavigation} from '@react-navigation/native';
import SubCategoryList from '../Products/Components/SubCategoryList';
import {Images} from '../../../assets/images';
import CustomButton from '../../../components/CustomButton/CustomButton';
import {categories, subCategories} from '../../../constants/productCategory';
import VersionChecker from '../../../components/versionChecker/VersionChecker';
import ProductCard from '../Products/Components/ProductCard';
import OccasionCard from '../Products/Components/OccasionCard';
import OfferCard from '../Products/Components/OfferCard';

const HomeScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSubmitSearch = (searchQuery: string) => {
    if (searchQuery.trim() !== '') {
      navigation.navigate(SCREEN_NAME.SUGGESTION, {
        searchQuery,
      });
      setSearchQuery('');
    }
  };
  const handleFocus = () => {
    navigation.navigate(SCREEN_NAME.SUGGESTION, {searchQuery: ''});
    setSearchQuery('');
  };
  const sampleData = [
    {
      id: '1',
      title: 'Him',
      description: 'Perfect for daily use or travel.',
      image: Images.men,
    },
    {
      id: '2',
      title: 'Her',
      description: 'Compact, lightweight, and durable.',
      image: Images.men,
    },
    {
      id: '3',
      title: 'Kids',
      description: 'Comfortable and built for speed.',
      image: Images.men,
    },
    {
      id: '4',
      title: 'Couples',
      description: 'Comfortable and built for speed.',
      image: Images.men,
    },
  ];
  const OfferData = [
    {
      id: '1',
      amount: '$400',
      image: Images.dress,
    },
    {
      id: '2',
      amount: '$1000',
      image: Images.dress,
    },
    {
      id: '3',
      amount: '$2000',

      image: Images.dress,
    },
  ];
  const mostViewedProducts = [
    {
      id: 1,
      image: Images.cream,
      name: 'Cream',
      price: 999,
      originalPrice: 1299,
      discount: '20%',
      rating: 4,
      shortDescription: 'Moisturizing cream',
    },
    {
      id: 2,
      image: Images.serum,
      name: 'Serum',
      price: 899,
      originalPrice: 1099,
      discount: '18%',
      rating: 4.2,
      shortDescription: 'Brightening serum',
    },
  ];
  const topSellingProducts = [
    {
      id: 3,
      image: Images.cosmetics,
      name: 'Cosmetics Kit',
      price: 1299,
      originalPrice: 1699,
      discount: '24%',
      rating: 4.5,
      shortDescription: 'Complete beauty kit',
    },
    {
      id: 4,
      image: Images.jwellery,
      name: 'Jewellery Set',
      price: 1599,
      originalPrice: 1999,
      discount: '20%',
      rating: 4.3,
      shortDescription: 'Elegant set for special occasions',
    },
  ];
  const handleLogin = () => {
    navigation.navigate(SCREEN_NAME.HOME);
  };
  return (
    <ScrollView style={styles.container}>
      <VersionChecker />

      <Searchbar
        placeholder="Search products..."
        onChangeText={text => {
          setSearchQuery(text);
          onSubmitSearch(text);
        }}
        onFocus={() => handleFocus()}
        onSubmitEditing={() => onSubmitSearch(searchQuery)}
        onIconPress={() => onSubmitSearch(searchQuery)}
        value={searchQuery}
        style={styles.searchbar}
        placeholderTextColor={colors.gray.medium}
      />

      <View style={styles.subCategory}>
        <SubCategoryList
          data={categories}
          imageStyle={styles.categoryImage}
          imageContainerStyle={styles.categoryContainer}
        />
      </View>

      <View style={styles.bannerContainer}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View>

      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Shop our Top Categories</Text>
        <CustomButton
          onPress={() => handleLogin}
          mode={'outlined'}
          title={'View more'}
          labelStyle={styles.categoryLabel}
        />
      </View>
      <View style={styles.subCategory}>
        <SubCategoryList
          overlayNames={true}
          data={subCategories}
          showNames={false}
          imageStyle={styles.shopCategory}
          imageContainerStyle={styles.shopCategoryContainer}
        />
      </View>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Recently Viewed</Text>
        <CustomButton
          mode={'outlined'}
          title={'View more'}
          labelStyle={styles.categoryLabel}
        />
      </View>
      <View style={styles.subCategory}>
        <SubCategoryList
          data={categories}
          showNames={false}
          imageContainerStyle={styles.subCategoryContainer}
        />
      </View>

      <CustomButton
        title={'Explore all products'}
        mode={'outlined'}
        style={styles.exlporeButton}
        labelStyle={{color: customColors.textBlack}}
      />
      <View style={styles.similarProducts}>
        <Text style={styles.sectionTitle}>Similar Products</Text>
        <SubCategoryList
          data={categories}
          showNames={false}
          imageContainerStyle={styles.subCategoryContainer}
        />
      </View>
      <View style={styles.bannerContainer2}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View>
      <View style={styles.mostViewdContainer}>
        <Text style={styles.mostViewd}>Most Viewed Products</Text>

        <FlatList
          data={mostViewedProducts}
          horizontal
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.flatListContainer}
          showsHorizontalScrollIndicator={false}
          renderItem={({item}) => (
            <View style={styles.productCardWidth}>
              <ProductCard
                onPress={() => {}}
                id={item.id}
                image={item.image}
                name={item.name}
                price={item.price.toString()}
                originalPrice={item.originalPrice.toString()}
                discount={item.discount}
                rating={item.rating}
                onWishlistToggle={() => {}}
                shortDescription={item.shortDescription}
                isloading={false}
              />
            </View>
          )}
        />
      </View>
      <View style={styles.topSellingProduct}>
        <Text style={styles.mostViewd}>Top Selling Products</Text>

        <FlatList
          data={topSellingProducts}
          horizontal
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.flatListContainer}
          showsHorizontalScrollIndicator={false}
          renderItem={({item}) => (
            <View style={styles.productCardWidth}>
              <ProductCard
                onPress={() => {}}
                id={item.id}
                image={item.image}
                name={item.name}
                price={item.price.toString()}
                originalPrice={item.originalPrice.toString()}
                discount={item.discount}
                rating={item.rating}
                onWishlistToggle={() => {}}
                shortDescription={item.shortDescription}
                isloading={false}
              />
            </View>
          )}
        />
      </View>
      <View style={styles.subCategory}>
        <SubCategoryList
          overlayNames={true}
          data={subCategories}
          showNames={false}
          imageStyle={styles.occasionCategory}
          imageContainerStyle={styles.OccationCategoryContainer}
        />
      </View>

      <FlatList
        data={sampleData}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        renderItem={({item}) => (
          <OccasionCard
            title={item.title}
            description={item.description}
            image={item.image}
          />
        )}
      />
      <View style={styles.bannerContainer2}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View>
      <View style={styles.subCategory}>
        <SubCategoryList
          data={categories}
          showNames={false}
          imageContainerStyle={styles.subCategoryContainer}
        />
      </View>
      <FlatList
        data={mostViewedProducts}
        horizontal
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.flatListContainer}
        showsHorizontalScrollIndicator={false}
        renderItem={({item}) => (
          <View style={styles.productCardWidth}>
            <ProductCard
              onPress={() => {}}
              id={item.id}
              image={item.image}
              name={item.name}
              price={item.price.toString()}
              originalPrice={item.originalPrice.toString()}
              discount={item.discount}
              rating={item.rating}
              onWishlistToggle={() => {}}
              shortDescription={item.shortDescription}
              isloading={false}
            />
          </View>
        )}
      />

      <View style={styles.bannerContainer2}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View>
      <FlatList
        data={OfferData}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        renderItem={({item}) => (
          <OfferCard amount={item.amount} image={item.image} />
        )}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {padding: 10, backgroundColor: customColors.white},
  flatListContent: {
    paddingHorizontal: 10,
    marginTop: 10,
  },
  flatListContainer: {gap: 10, paddingVertical: 10},

  productCardWidth: {width: 185},
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
  },
  sectionTitle: {
    marginTop: 15,
    marginLeft: 15,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
  mostViewdContainer: {marginTop: 30, padding: 20},
  mostViewd: {
    marginLeft: 4,
    marginBottom: 20,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
  viewMore: {
    color: customColors.appBlue,
    fontSize: 14,
    height: 30,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.gray.medium,
    fontWeight: '500',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 1,
  },
  banner: {
    width: '100%',
    height: 150,
    marginBottom: 0,
    paddingBottom: 0,
  },
  bannerText: {
    color: customColors.textBlack,
  },
  exploreButton: {
    marginTop: 24,
    borderRadius: 25,
    paddingVertical: 8,
  },
  subCategory: {marginBottom: 0, paddingBottom: 0},
  similarProducts: {
    marginTop: 20,
  },

  imageContainerStyle: {
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
  },
  categoryContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    marginTop: 10,
  },
  categoryImage: {
    borderRadius: 50,
    backgroundColor: customColors.white,
    height: 50,
    width: 50,
  },
  subCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  shopCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  OccationCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
    marginLeft: 10,
  },

  exlporeButton: {
    width: 300,
    marginTop: 20,
    alignSelf: 'center',
    borderColor: colors.tertiary,
  },
  categoryLabel: {color: customColors.textBlack},
  shopCategory: {height: 120},
  occasionCategory: {height: 100, width: 120},
  bannerContainer: {marginBottom: 0, paddingBottom: 0},
  bannerContainer2: {marginBottom: 0, paddingBottom: 0, marginTop: 20},
  topSellingProduct: {padding: 20},
});

export default HomeScreen;
