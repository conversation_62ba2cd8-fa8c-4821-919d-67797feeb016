import React, {useMemo} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {useGetProductVariantsQuery} from '../../../../redux/product/productApiSlice';
import {Images} from '../../../../assets/images';
import {colors} from '../../../../theme/colors';
import {ExploreAllNavigationProp} from '../../../../navigations/types';
import {useNavigation} from '@react-navigation/native';
import {SCREEN_NAME} from '../../../../constants/screenNames';

const numColumns = 3;
const screenWidth = Dimensions.get('window').width;
const itemSize = (screenWidth - 40 - (numColumns - 1) * 10) / numColumns;

const productVariantFilter = {
  include: [
    {relation: 'product', required: true},
    {relation: 'productVariantPrice'},
    {relation: 'featuredAsset'},
  ],
};

const ExploreAllProducts = () => {
  const navigation = useNavigation<ExploreAllNavigationProp>();

  const {
    data: productVariants = [],
    isLoading,
    isError,
  } = useGetProductVariantsQuery(productVariantFilter);

  const listData = useMemo(
    () =>
      productVariants.map(v => ({
        id: v.id.toString(),
        image: v.featuredAsset?.previewUrl ?? Images.backpack,
      })),
    [productVariants],
  );

  if (isError) {
    return (
      <View style={styles.center}>
        <Text>Couldn't load products, please try again.</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  const renderItem = ({item}: {item: {id: string; image: string}}) => (
    <View style={styles.itemContainer}>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate(SCREEN_NAME.PRODUCTS, {
            productId: item.id,
          });
        }}>
        <Image source={{uri: item.image}} style={styles.image} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Explore All Products</Text>

      <FlatList
        data={listData}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        numColumns={numColumns}
        columnWrapperStyle={styles.columnWrapper}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default ExploreAllProducts;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  heading: {
    fontSize: 16,
    marginBottom: 20,
    fontWeight: 'bold',
    color: colors.tertiary,
  },
  subheading: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  listContainer: {paddingBottom: 20},
  columnWrapper: {
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  itemContainer: {
    width: itemSize,
    height: itemSize,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  center: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
