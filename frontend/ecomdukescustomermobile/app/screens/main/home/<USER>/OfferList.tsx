import React from 'react';
import {FlatList, ViewStyle, StyleProp, StyleSheet} from 'react-native';
import OfferCard from '../../Products/Components/OfferCard';

type OfferItem = {
  id: string;
  title?: string;
  subtitle?: string;
  image: string;
  facetValueIds?: string[];
};

type OfferListProps = {
  data: OfferItem[];
  containerStyle?: StyleProp<ViewStyle>;
  onPress?: (item: OfferItem) => void;
};

const OfferList: React.FC<OfferListProps> = ({
  data,
  containerStyle,
  onPress,
}) => {
  return (
    <FlatList
      data={data}
      keyExtractor={item => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[styles.flatListContent, containerStyle]}
      renderItem={({item}) => {
        return (
          <OfferCard
            subtitle={item.subtitle}
            image={item.image}
            onPress={() => onPress?.(item)}
          />
        );
      }}
    />
  );
};

export default OfferList;
export const styles = StyleSheet.create({
  flatListContent: {
    paddingVertical: 8,
  },
});
