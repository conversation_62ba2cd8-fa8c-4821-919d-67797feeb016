import React, {useCallback, useEffect, useState, memo} from 'react';
import {ScrollView, StyleSheet, useWindowDimensions, View} from 'react-native';
import {ActivityIndicator, Text} from 'react-native-paper';
import {useFocusEffect, useRoute} from '@react-navigation/native';
import RenderHtml from 'react-native-render-html';
import draftToHtml from 'draftjs-to-html';
import {useDispatch} from 'react-redux';

import type {RouteProp} from '@react-navigation/native';
import {OnboardStackParamList} from '../../../navigations/types';
import {
  useGetGuestTokenMutation,
  useGetTermsAndConditionQuery,
} from '../../../redux/terms/termsApiSlice';
import {getItem} from '../../../redux/mmkvStorage';
import {setGuestToken} from '../../../redux/auth/authSlice';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';

const LegalScreen: React.FC = () => {
  const {
    params: {title, type},
  } = useRoute<RouteProp<OnboardStackParamList, 'legal'>>();

  const dispatch = useDispatch();
  const [getToken] = useGetGuestTokenMutation();
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [guestCode, setGuestCode] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      const stored = await getItem('accessToken');
      setAccessToken(stored?.toString() || null);
    })();
  }, []);

  useEffect(() => {
    if (!accessToken) {
      (async () => {
        const result = await getToken().unwrap();
        if (result?.accessToken) {
          dispatch(setGuestToken(result));
          setGuestCode(result.accessToken);
        }
      })();
    }
  }, [accessToken, getToken, dispatch]);

  const tokenToUse = accessToken || guestCode;

  const {
    data: document,
    isLoading,
    refetch,
  } = useGetTermsAndConditionQuery(tokenToUse as string, {
    skip: !tokenToUse,
    selectFromResult: ({data, ...rest}) => ({
      ...rest,
      data: data?.find(item => item.type === type),
    }),
  });

  useFocusEffect(
    useCallback(() => {
      if (tokenToUse) refetch();
    }, [tokenToUse, refetch]),
  );

  const {width} = useWindowDimensions();

  const formattedHTML =
    document?.data && isJson(document.data)
      ? draftToHtml(JSON.parse(document.data))
      : '<p>No content available</p>';

  if (isLoading || !tokenToUse) {
    return <ActivityIndicator style={styles.activityIndicator} />;
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.contentWrapper}>
        <Text style={styles.heading}>{title}</Text>
        <RenderHtml
          contentWidth={width - 40}
          source={{html: formattedHTML}}
          tagsStyles={htmlStyles}
        />
      </View>
    </ScrollView>
  );
};

export default memo(LegalScreen);

const isJson = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 24,
    paddingHorizontal: 20,
    backgroundColor: colors.gray.backGround,
  },
  contentWrapper: {
    backgroundColor: customColors.white,
    borderRadius: 12,
    padding: 25,
    shadowColor: customColors.textBlack,
    shadowOpacity: 0.08,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    elevation: 3,
  },
  heading: {
    fontSize: 24,
    fontWeight: '600',
    color: customColors.textBlack,
    textAlign: 'left',
  },
  activityIndicator: {marginTop: 20},
});

const htmlStyles = {
  h1: {
    fontSize: 22,
    fontWeight: '700',
    color: customColors.primaryContainer,
    marginBottom: 12,
    marginTop: 20,
    padding: 8,
    borderRadius: 6,
  },
  h2: {
    fontSize: 20,
    fontWeight: '700',
    color: customColors.primaryContainer,
    marginBottom: 10,
    marginTop: 16,
    padding: 6,
    borderRadius: 4,
  },
  h3: {
    fontSize: 18,
    fontWeight: '600',
    color: customColors.primaryContainer,
    marginBottom: 8,
    marginTop: 12,
    padding: 4,
    borderRadius: 2,
  },
  p: {
    fontSize: 16,
    color: customColors.textBlack,
    lineHeight: 24,
    marginBottom: 12,
  },
  li: {
    fontSize: 16,
    color: customColors.textBlack,
    marginBottom: 8,
  },
} as const;
