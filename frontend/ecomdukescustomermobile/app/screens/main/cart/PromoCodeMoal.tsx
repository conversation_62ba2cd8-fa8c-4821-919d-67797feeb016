import React from 'react';
import {
  Modal,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {Button} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';

export type PromoCode = {
  id: string;
  code: string;
  discount: number;
  value: number;
  validFrom: string;
  validTill: string;
};

type CouponModalProps = {
  visible: boolean;
  onClose: () => void;
  onSelect: (coupon: PromoCode) => void;
  promoCodes: PromoCode[];
  loading: boolean;
  refetch: () => void;
};

const CouponModal: React.FC<CouponModalProps> = ({
  visible,
  onClose,
  onSelect,
  promoCodes,
  loading,
  refetch,
}) => {
  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Available Coupons</Text>

          {loading ? (
            <ActivityIndicator size="large" />
          ) : (
            <FlatList
              data={promoCodes}
              keyExtractor={item => item.code}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.promoItem}
                  onPress={() => {
                    onSelect(item);
                    refetch();
                    onClose();
                  }}>
                  <Text style={styles.promoText}>
                    {item.code} - ₹{item.value} OFF
                  </Text>
                </TouchableOpacity>
              )}
            />
          )}

          <Button mode="text" onPress={onClose}>
            Close
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default CouponModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: colors.transparent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '70%',
    backgroundColor: customColors.white,
    borderRadius: 10,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  promoItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: colors.gray.light,
  },
  promoText: {
    fontSize: 16,
  },
});
