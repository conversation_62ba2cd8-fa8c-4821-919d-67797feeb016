import React from 'react';
import {View, Text, StyleSheet, FlatList} from 'react-native';
import CustomTextInput from '../../../../components/InputFields/CustomTextInput';
import CustomButton from '../../../../components/CustomButton/CustomButton';
import {colors} from '../../../../theme/colors';

interface Customization {
  title: string;
  value: string;
}

interface OrderCardOptionsProps {
  customizations: Customization[];
  onChange: (index: number, value: string) => void;
  onSave: () => void;
  onRemove: () => void;
}

const OrderCardOptions: React.FC<OrderCardOptionsProps> = ({
  customizations,
  onChange,
  onSave,
  onRemove,
}) => {
  return (
    <>
      <View style={styles.section}>
        <View style={styles.rowAlign}>
          <Text style={styles.sectionTitle}>Customization</Text>
        </View>

        <FlatList
          data={customizations}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item, index}) => (
            <CustomTextInput
              title={item.title}
              value={item.value}
              onChangeText={text => onChange(index, text)}
            />
          )}
        />
      </View>

      <View style={styles.actionRow}>
        <CustomButton
          style={styles.saveActionButton}
          buttonColor={colors.tertiary}
          title={'Save for Later'}
          onPress={onSave}
        />

        <CustomButton
          title={'Remove'}
          style={styles.removeActionButton}
          buttonColor={colors.gray.medium}
          onPress={onRemove}
        />
      </View>
    </>
  );
};

export default OrderCardOptions;

const styles = StyleSheet.create({
  container: {marginBottom: 10, marginTop: 10},
  section: {
    marginVertical: 10,
  },
  input: {
    minHeight: 100,
  },
  giftMsgContainer: {
    fontWeight: 'bold',
    color: colors.tertiary,
  },
  sectionTitle: {
    marginTop: 0,
    fontWeight: 'bold',
    fontSize: 15,
    marginBottom: 8,
    color: colors.tertiary,
  },
  couponRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    height: 45,
  },
  couponText: {
    marginLeft: 8,
    fontSize: 14,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  saveActionButton: {
    flex: 1,
    marginRight: 8,
  },
  removeActionButton: {
    flex: 1,
    marginLeft: 8,
  },
  rowAlign: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  giftRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    columnGap: 6,
    marginTop: 10,
  },
});
