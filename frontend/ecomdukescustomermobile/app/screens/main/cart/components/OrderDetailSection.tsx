import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {Icon} from 'react-native-paper';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';
import {useGetUserQuery} from '../../../../redux/auth/authApiSlice';
import {useGetAddressesQuery} from '../../../../redux/customer/customerApiSlice';
import {FlatList} from 'react-native-gesture-handler';
import {AddressDto} from '../../../../types/customerApi';
type Props = {
  hanldePress: () => void;
  handleEditAddress: (address: AddressDto) => void;
  onAddressSelect: (addressId: string) => void;
};
const OrderDetailsSection: React.FC<Props> = ({
  hanldePress,
  handleEditAddress,
  onAddressSelect,
}) => {
  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;

  const {data: addresses = []} = useGetAddressesQuery(customerId ?? '', {
    skip: !customerId,
  });
  console.log('addresses', addresses);

  const [selectedBillingAddressId, setBillingAddressId] = useState<
    string | null
  >(null);
  const [selectShippingAddressId, setShippingAddressId] = useState<
    string | null
  >(null);

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Text style={styles.blockTitle}>Shipping Address</Text>
        <Text style={styles.text}>
          {addresses.length} {addresses.length === 1 ? 'address' : 'addresses'}
        </Text>
      </View>

      <FlatList
        data={addresses}
        keyExtractor={item => item.id ?? ''}
        renderItem={({item}) => {
          const isSelected = selectShippingAddressId === item.id;

          return (
            <TouchableOpacity
              onPress={() => {
                const selectedId = item.id ?? null;
                setShippingAddressId(selectedId);
                if (selectedId) {
                  onAddressSelect(selectedId);
                }
              }}
              activeOpacity={0.9}
              style={[styles.block, isSelected && styles.selectedBlock]}>
              <View style={styles.rowBetween}>
                <View style={styles.addressDetails}>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    {item.name}
                  </Text>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    {item.addressLine1}, {item.addressLine2}
                  </Text>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    {item.city}
                  </Text>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    Phone: {item.phoneNumber}
                  </Text>
                </View>

                <TouchableOpacity
                  onPress={() => handleEditAddress(item)}
                  hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                  <Icon
                    source="pencil-box-multiple"
                    size={20}
                    color={isSelected ? customColors.white : colors.tertiary}
                  />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          );
        }}
      />

      <TouchableOpacity onPress={hanldePress}>
        <View style={styles.addAddress}>
          <Icon
            size={20}
            color={colors.gray.dark}
            source={'plus-circle-outline'}
          />
          <Text style={styles.text}>Add Address</Text>
        </View>
      </TouchableOpacity>
      <View style={styles.estimatedDeliveryView}>
        <Text style={styles.text}>Estimated delivery : 25 May 2025</Text>
        <Text style={styles.text}>Dispatch from Mumbai</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.blockTitle}>Billing Address</Text>
        <Text style={styles.text}>2 address</Text>
      </View>

      <FlatList
        data={addresses}
        keyExtractor={item => item.id ?? ''}
        renderItem={({item}) => {
          const isSelected = selectedBillingAddressId === item.id;

          return (
            <TouchableOpacity
              onPress={() => {
                const selectedId = item.id ?? null;
                setBillingAddressId(selectedId);
                if (selectedId) {
                  onAddressSelect(selectedId);
                }
              }}
              activeOpacity={0.9}
              style={[styles.block, isSelected && styles.selectedBlock]}>
              <View style={styles.rowBetween}>
                <View style={styles.addressDetails}>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    {item.name}
                  </Text>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    {item.addressLine1}, {item.addressLine2}
                  </Text>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    {item.city}
                  </Text>
                  <Text
                    style={[styles.text, isSelected && styles.selectedText]}>
                    Phone: {item.phoneNumber}
                  </Text>
                </View>

                <TouchableOpacity
                  onPress={() => handleEditAddress(item)}
                  hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                  <Icon
                    source="pencil-box-multiple"
                    size={20}
                    color={isSelected ? customColors.white : colors.tertiary}
                  />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          );
        }}
      />
      <TouchableOpacity onPress={hanldePress}>
        <View style={styles.addAddress}>
          <Icon
            size={20}
            color={colors.gray.dark}
            source={'plus-circle-outline'}
          />
          <Text style={styles.text}>Add Address</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default OrderDetailsSection;

const styles = StyleSheet.create({
  container: {
    marginBottom: 1,
  },
  block: {
    alignItems: 'flex-start',
    backgroundColor: customColors.white,
    padding: 12,
    borderRadius: 15,
    marginBottom: 12,
    elevation: 1,
    borderWidth: 1,
    borderColor: colors.gray.medium,
  },
  addAddress: {
    backgroundColor: customColors.white,
    padding: 18,
    borderRadius: 15,
    marginBottom: 12,
    elevation: 1,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
    borderColor: colors.gray.medium,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  blockTitle: {
    color: colors.tertiary,
    fontWeight: 'bold',
    fontSize: 14,
  },
  text: {
    color: customColors.textBlack,
    marginVertical: 2,
    fontSize: 14,
  },

  selectedOption: {
    backgroundColor: colors.gray.backGround,
  },
  couponRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  couponText: {
    fontWeight: 'bold',
    color: colors.primary,
  },

  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6,
  },
  totalText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  estimatedDeliveryView: {
    flexDirection: 'column',
    marginBottom: 15,
    marginLeft: 15,
  },
  selectedBlock: {
    backgroundColor: colors.tertiary,
    borderColor: colors.tertiary,
  },
  selectedText: {
    color: customColors.white,
  },
  addressDetails: {
    flex: 1,
    paddingRight: 8,
  },
});
