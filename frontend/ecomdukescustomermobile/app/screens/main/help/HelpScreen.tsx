import {<PERSON><PERSON>ist, <PERSON><PERSON><PERSON><PERSON>ontrol, SafeAreaView, StyleSheet} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, Button, Text} from 'react-native-paper';
import styleConstants from '../../../theme/styleConstants';
import {colors} from '../../../theme/colors';
import FAQAccordion from '../../../components/accordion/FAQAccordion';
import customColors from '../../../theme/customColors';
import draftToHtml from 'draftjs-to-html';
import {useFocusEffect} from '@react-navigation/native';
import {HelpNavigationProp} from '../../../navigations/types';
import SupportTicketModal from './components/SupportTicketModal';
import {useGetHelpQuery} from '../../../redux/help/helpApiSlice';
import {HelpItem} from '../../../types/help';
interface ScreenProps {
  navigation: HelpNavigationProp;
}

// Footer
const HelpScreenFooter = () => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Text style={styles.sectionTitle}>Facing a different issue?</Text>{' '}
      <Button
        mode="contained"
        onPress={() => setVisible(true)}
        style={styles.supportButton}>
        + Create Support Ticket
      </Button>
      <SupportTicketModal visible={visible} onClose={() => setVisible(false)} />
    </>
  );
};

const HelpScreen: React.FC<ScreenProps> = () => {
  const {
    data: helpItems = [],
    isLoading: isHelpLoading,
    refetch: refetchHelp,
    isFetching,
  } = useGetHelpQuery();

  useFocusEffect(
    useCallback(() => {
      refetchHelp();
    }, [refetchHelp]),
  );
  const renderItem = ({item}: {item: HelpItem}) => {
    let html = '';
    try {
      const maybeJson = JSON.parse(item.answer);
      html = draftToHtml(maybeJson);
    } catch {
      html = item.answer; // already HTML or plain text
    }
    return <FAQAccordion question={item.question} answer={html} />;
  };
  const listData = useMemo(() => {
    return [...helpItems].sort((a, b) => a.category.localeCompare(b.category));
  }, [helpItems]);
  const loading = isHelpLoading;
  return (
    <SafeAreaView style={styles.mainContainer}>
      {loading ? (
        <ActivityIndicator size="small" color={colors.primary} />
      ) : (
        <FlatList
          refreshing={loading}
          onRefresh={() => {}}
          data={listData}
          keyExtractor={item => item.question}
          renderItem={renderItem}
          ListHeaderComponent={
            <Text style={styles.headerText}>
              What are the issues you are facing?
            </Text>
          }
          ListFooterComponent={<HelpScreenFooter />}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={isFetching} onRefresh={refetchHelp} />
          }
        />
      )}
    </SafeAreaView>
  );
};

export default HelpScreen;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  emailText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  footerContainer: {
    backgroundColor: colors.background,
    borderColor: colors.gray.light,
    borderTopWidth: 1,
    marginTop: styleConstants.spacing.x20,
    paddingHorizontal: styleConstants.spacing.x20,
    paddingVertical: styleConstants.spacing.x20,
  },

  footerItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: styleConstants.spacing.s10,
  },
  footerLabel: {
    color: customColors.textBlack,
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerText: {
    color: customColors.textBlack,
    fontSize: 16,
    marginBottom: 5,
  },
  footerValue: {
    color: colors.primary,
    fontSize: 16,
    marginTop: 2,
  },
  headerText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: styleConstants.spacing.s10,
  },
  headerText1: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    // marginBottom: styleConstants.spacing.s10,
  },
  listContainer: {
    backgroundColor: colors.background,
    flexGrow: 1,
    padding: styleConstants.spacing.x20,
  },
  phoneText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  supportButton: {
    marginVertical: 20,
    marginHorizontal: 16,
    backgroundColor: colors.tertiary,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: customColors.textBlack,
    marginBottom: 12,
    marginTop: 24,
  },
});
