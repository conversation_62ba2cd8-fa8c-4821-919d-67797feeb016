import React, {useCallback, useEffect, useState} from 'react';
import {ScrollView, StyleSheet, useWindowDimensions, View} from 'react-native';
import {ActivityIndicator, Text} from 'react-native-paper';
import RenderHtml from 'react-native-render-html';
import {useFocusEffect} from '@react-navigation/native';
import draftToHtml from 'draftjs-to-html';
import {useDispatch} from 'react-redux';
import {useGetGuestTokenMutation} from '../../../redux/terms/termsApiSlice';
import {setGuestToken} from '../../../redux/auth/authSlice';
import {RefunPolicyNavigationProp} from '../../../navigations/types';
import {getItem} from '../../../redux/mmkvStorage';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {useGetPrivacyAndPolicyQuery} from '../../../redux/privacy/privacyApiSlice';

interface ScreenProps {
  navigation: RefunPolicyNavigationProp;
}

const RefundPolicyScreen: React.FC<ScreenProps> = () => {
  const [guestCode, setGuestCode] = useState<string | null>(null);
  const dispatch = useDispatch();
  const [getToken] = useGetGuestTokenMutation();
  const [accessToken, setAccessToken] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      const token = await getItem('accessToken');
      setAccessToken(token?.toString() || null);
    })();
  }, []);

  useEffect(() => {
    if (!accessToken) {
      const handleGuestLogin = async () => {
        const result = await getToken().unwrap();
        if (result?.accessToken) {
          dispatch(setGuestToken(result));
          setGuestCode(result.accessToken);
        }
      };

      handleGuestLogin();
    }
  }, [accessToken, getToken, dispatch]);

  const tokenToUse = accessToken || guestCode;
  const {width} = useWindowDimensions();

  const {
    data: termData,
    isLoading,
    refetch,
  } = useGetPrivacyAndPolicyQuery(tokenToUse as string, {
    skip: !tokenToUse,
  });

  const formattedHTML =
    termData?.[0]?.data && isJson(termData[0].data)
      ? draftToHtml(JSON.parse(termData[0].data))
      : '<p>No content available</p>';

  useFocusEffect(
    useCallback(() => {
      if (tokenToUse) {
        refetch();
      }
    }, [tokenToUse, refetch]),
  );

  if (isLoading || !tokenToUse) {
    return <ActivityIndicator style={styles.activityIndicator} />;
  }

  return (
    <>
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.contentWrapper}>
          <Text style={styles.heading}>Return & Refund policy</Text>
          <RenderHtml
            contentWidth={width - 40}
            source={{html: formattedHTML}}
            tagsStyles={htmlStyles}
          />
        </View>
      </ScrollView>
    </>
  );
};

export default RefundPolicyScreen;

const styles = StyleSheet.create({
  container: {
    paddingVertical: 24,
    paddingHorizontal: 20,
    backgroundColor: colors.gray.backGround,
  },
  contentWrapper: {
    backgroundColor: customColors.white,
    borderRadius: 12,
    padding: 25,
    shadowColor: customColors.textBlack,
    shadowOpacity: 0.08,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    elevation: 3,
  },
  heading: {
    fontSize: 24,
    fontWeight: '600',
    color: customColors.textBlack,
    textAlign: 'left',
  },
  activityIndicator: {marginTop: 20},
});

const isJson = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};
const htmlStyles = {
  h1: {
    fontSize: 22,
    fontWeight: 700,
    color: customColors.primaryContainer,
    marginBottom: 12,
    marginTop: 20,
    padding: 8,
    borderRadius: 6,
  },
  h2: {
    fontSize: 20,
    fontWeight: 700,
    color: customColors.primaryContainer,
    marginBottom: 10,
    marginTop: 16,
    padding: 6,
    borderRadius: 4,
  },
  h3: {
    fontSize: 18,
    fontWeight: 600,
    color: customColors.primaryContainer,
    marginBottom: 8,
    marginTop: 12,

    padding: 4,
    borderRadius: 2,
  },
  p: {
    fontSize: 16,
    color: customColors.textBlack,
    lineHeight: 24,
    marginBottom: 12,
  },
  li: {
    fontSize: 16,
    color: customColors.textBlack,
    marginBottom: 8,
  },
};
