import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Alert,
  TouchableWithoutFeedback,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AppDispatch} from '../../../redux/store';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {unsetCredentials, unsetGuestToken} from '../../../redux/auth/authSlice';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';

type Account = {
  email: string;
  token: string;
};

type Props = {
  visible?: boolean;
  onClose: () => void;
  onAddAccount: () => void;
  onAccountSwitch: (account: Account) => void;
};
const SwitchAccountModal = ({visible, onClose, onAccountSwitch}: Props) => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [currentEmail, setCurrentEmail] = useState<string | null>(null);
  const dispatch = useDispatch();

  useEffect(() => {
    if (visible) {
      fetchAccounts();
      fetchCurrentEmail();
    }
  }, [visible]);

  const fetchAccounts = async () => {
    const stored = await AsyncStorage.getItem('accounts');

    if (stored) setAccounts(JSON.parse(stored));
  };

  const fetchCurrentEmail = async () => {
    const storedEmail = await AsyncStorage.getItem('currentEmail');
    setCurrentEmail(storedEmail);
  };
  useEffect(() => {
    if (visible) {
      fetchAccounts();
      fetchCurrentEmail();
    }
  }, [visible]);
  useEffect(() => {
    if (visible) {
      fetchAccounts();
      fetchCurrentEmail();
    }
  }, [visible]);

  const handleSwitch = async (account: Account) => {
    await AsyncStorage.setItem('currentEmail', account.email);
    await AsyncStorage.setItem('currentToken', account.token);
    setCurrentEmail(account.email);
    onAccountSwitch(account);
    onClose();
  };

  const handleSignOut = async (email: string) => {
    const updated = accounts.filter(a => a.email !== email);
    setAccounts(updated);
    await AsyncStorage.setItem('accounts', JSON.stringify(updated));
    const logout = () => (dispatch: AppDispatch) => {
      dispatch(unsetCredentials());
      dispatch(unsetGuestToken());
    };
    if (email === currentEmail) {
      await AsyncStorage.removeItem('currentEmail');
      await AsyncStorage.removeItem('currentToken');
      logout()(dispatch);
      setCurrentEmail(null);
    }
  };
  const navigation = useNavigation<any>();
  const renderItem = ({item}: {item: Account}) => {
    const isCurrent = item.email === currentEmail;
    return (
      <TouchableOpacity
        style={[styles.accountRow, isCurrent && styles.activeAccountRow]}
        onPress={() => handleSwitch(item)}
        activeOpacity={0.9}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>{item.email[0].toUpperCase()}</Text>
        </View>
        <Text style={[styles.emailText, isCurrent && styles.activeText]}>
          {item.email} {isCurrent ? '(Active)' : ''}
        </Text>
        <TouchableOpacity
          onPress={() =>
            Alert.alert('Confirm', 'Sign out this account?', [
              {text: 'Cancel'},
              {
                text: 'Sign Out',
                onPress: () => handleSignOut(item.email),
                style: 'destructive',
              },
            ])
          }>
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalBackdrop}>
          <View style={styles.modalContent}>
            <Text style={styles.title}>Switch accounts</Text>

            {currentEmail ? (
              <Text style={styles.emailText}>Logged in as: {currentEmail}</Text>
            ) : (
              <Text style={styles.emailText}>No active user</Text>
            )}

            <FlatList
              data={accounts}
              keyExtractor={item => item.email}
              renderItem={renderItem}
              contentContainerStyle={{paddingBottom: 24}}
            />
            <TouchableOpacity
              style={styles.addAccountButton}
              onPress={() => {
                console.log('Add Account button clicked');
                onClose();
                navigation.navigate('AuthStack', {
                  screen: 'login',
                  params: {
                    onLoginSuccess: (account: {email: any; token?: string}) => {
                      console.log('Login success callback triggered', account);
                      if (account.token) {
                        const validAccount: Account = {
                          email: account.email,
                          token: account.token,
                        };
                        setAccounts(prev => [...prev, validAccount]);
                        setCurrentEmail(validAccount.email);
                        onAccountSwitch(validAccount);
                      } else {
                        console.error('Account token is missing');
                      }
                    },
                  },
                });
              }}>
              <Text style={styles.addAccountText}>＋ Add New Account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default SwitchAccountModal;
const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
    padding: 20,
  },
  modalContent: {
    backgroundColor: customColors.white,
    borderRadius: 16,
    padding: 20,
    maxHeight: '80%',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 16,
    color: customColors.textBlack,
  },
  accountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eef3fc',
    padding: 12,
    borderRadius: 12,
    marginBottom: 10,
  },
  activeAccountRow: {
    backgroundColor: colors.gray.backGround,
  },
  activeText: {
    color: colors.tertiary,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  avatar: {
    backgroundColor: colors.tertiary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: customColors.white,
  },
  emailText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#222',
  },
  signOutText: {
    fontSize: 14,
    color: customColors.primaryContainer,
    fontWeight: '600',
  },
  addAccountButton: {
    alignSelf: 'center',
    marginTop: 12,
  },
  addAccountText: {
    fontSize: 16,
    fontWeight: '600',
    color: customColors.primaryContainer,
  },
});
