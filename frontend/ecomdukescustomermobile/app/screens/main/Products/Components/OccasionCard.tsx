import React from 'react';
import {View, Text, Image, StyleSheet, TouchableOpacity} from 'react-native';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

type CardProps = {
  title: string;
  description: string;
  image: any;
  onPress?: () => void;
};

const OccasionCard = ({title, description, image, onPress}: CardProps) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.textContainer}>
        <Text style={styles.forText}>For</Text>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description} numberOfLines={2}>
          {description}
        </Text>
      </View>
      <Image source={image} style={styles.image} resizeMode="cover" />
    </TouchableOpacity>
  );
};

export default OccasionCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: customColors.white,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    marginBottom: 16,
    marginHorizontal: 12,
    width: 130,
    borderColor: colors.gray.light,
    borderWidth: 1,
  },
  image: {
    width: '100%',
    height: 120,
  },
  textContainer: {
    padding: 12,
    marginBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    color: colors.tertiary,
  },
  description: {
    fontSize: 14,
    color: colors.tertiary,
  },
  forText: {
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
});
