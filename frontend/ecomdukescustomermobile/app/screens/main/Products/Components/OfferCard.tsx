import React from 'react';
import {View, Text, Image, StyleSheet, TouchableOpacity} from 'react-native';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

type CardProps = {
  amount: string;
  image: any;
  onPress?: () => void;
};

const OfferCard = ({amount, image, onPress}: CardProps) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <Image source={image} style={styles.image} resizeMode="cover" />

      <View style={styles.textContainer}>
        <Text style={styles.forText}>Product Under</Text>
        <Text style={styles.title}>{amount}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default OfferCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: customColors.white,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 3,
    marginBottom: 16,
    marginHorizontal: 12,
    width: 130,
    borderColor: colors.gray.light,
    borderWidth: 1,
  },
  image: {
    borderWidth: 1,
    borderBottomColor: 'white',
    width: '100%',
    height: 140,
  },
  textContainer: {
    padding: 15,
    paddingTop: 20,
    height: 100,
    alignItems: 'flex-start',
    paddingBottom: 20,

    backgroundColor: colors.gray.backGround,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    color: colors.tertiary,
  },

  forText: {
    color: colors.tertiary,
  },
});
