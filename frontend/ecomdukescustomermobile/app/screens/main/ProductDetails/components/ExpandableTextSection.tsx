import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';

type ExpandableTextProps = {
  title: string;
  content?: string;
  fallback: string;
  previewCharLimit?: number;
  numberOfLines?: number;
};

const isJsonString = (str: string) => {
  return (
    typeof str === 'string' &&
    str.trim().startsWith('{') &&
    str.trim().endsWith('}')
  );
};

const extractPlainText = (raw: string): string => {
  if (!raw) {
    return '';
  }

  if (!isJsonString(raw)) {
    if (raw.includes('<') && raw.includes('>')) {
      return raw.replace(/<[^>]*>/g, '').trim();
    }
    return raw;
  }

  try {
    const parsed = JSON.parse(raw);
    const blocks = parsed.blocks || [];
    return blocks
      .map((block: any) => block.text)
      .join('\n')
      .trim();
  } catch (e) {
    return raw;
  }
};

const ExpandableText: React.FC<ExpandableTextProps> = ({
  title,
  content,
  fallback,
  previewCharLimit = 100,
  numberOfLines = 3,
}) => {
  const [expanded, setExpanded] = useState(false);

  const fullText = content ? extractPlainText(content) : fallback;
  const shouldShowToggle = fullText.length > previewCharLimit;
  const hasContent = !!content;

  return (
    <View style={styles.container}>
      {title ?? <Text style={styles.heading}>{title}</Text>}
      <Text
        style={[
          styles.paragraph,
          {color: hasContent ? customColors.textBlack : colors.gray.dark},
          // eslint-disable-next-line react-native/no-inline-styles
          {textAlign: 'left', fontSize: hasContent ? 15 : 13},
        ]}
        numberOfLines={
          expanded || !shouldShowToggle ? undefined : numberOfLines
        }>
        {fullText}
      </Text>
      {shouldShowToggle && (
        <TouchableOpacity onPress={() => setExpanded(!expanded)}>
          <Text style={styles.seeMoreView}>
            {expanded ? 'See Less' : 'See More'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ExpandableText;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: customColors.white,
    borderRadius: 12,
    marginBottom: 16,
  },
  heading: {
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.tertiary,
    marginBottom: 4,
  },
  paragraph: {
    textAlign: 'left',
    lineHeight: 20,
  },

  seeMoreView: {
    color: customColors.primaryContainer,
    marginTop: 4,
    fontSize: 13,
  },
});
