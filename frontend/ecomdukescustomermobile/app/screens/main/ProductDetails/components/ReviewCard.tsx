import React from 'react';
import {View, Text, FlatList, Image, StyleSheet} from 'react-native';
import {Icon} from 'react-native-paper';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';

interface Review {
  id?: string;
  name: string;
  avatar: string;
  isVerified: boolean;
  rating: number;
  review?: string;
  images?: string[];
}

interface ReviewCardProps {
  title?: string;
  reviews: Review[];
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  title = 'Reviews',
  reviews,
}) => {
  const getInitials = (fullName?: string) => {
    if (typeof fullName !== 'string') {
      return '';
    }

    const names = fullName.trim().split(' ');
    const first = names[0]?.[0] || '';
    const last = names[names.length - 1]?.[0] || '';
    return (first + last).toUpperCase();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title}</Text>

      <FlatList
        data={reviews}
        keyExtractor={(item, index) => item.id || index.toString()}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No reviews found</Text>
          </View>
        }
        renderItem={({item}) => (
          <View style={styles.card}>
            <Text style={styles.reviewText}>{item.review}</Text>

            {item.images && item.images.length > 0 && (
              <View style={styles.imageRow}>
                {item.images.map((img, index) => (
                  <Image
                    key={index}
                    source={typeof img === 'string' ? {uri: img} : img}
                    style={styles.reviewImage}
                  />
                ))}
              </View>
            )}

            <View style={styles.row}>
              <View style={styles.initialsCircle}>
                <Text style={styles.initialsText}>
                  {getInitials(item.name)}
                </Text>
              </View>
              <Text style={styles.nameText}>{item.name}</Text>
              {item.isVerified && (
                <Icon source="check-decagram" size={16} color="#9C27B0" />
              )}
              <View style={styles.flex} />
            </View>

            <View style={styles.ratingRow}>
              {[1, 2, 3, 4, 5].map(i => (
                <Icon
                  key={i}
                  source="star"
                  size={18}
                  color={i <= item.rating ? colors.tertiary : '#ccc'}
                />
              ))}
            </View>

            <View style={styles.divider} />
          </View>
        )}
      />
    </View>
  );
};

export default ReviewCard;
const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  flex: {flex: 1},
  heading: {
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.tertiary,
    marginBottom: 8,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderColor: colors.gray.light,
    borderWidth: 1,
  },
  reviewText: {
    color: colors.gray.dark,
    fontSize: 14,
    marginBottom: 8,
  },
  imageRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  reviewImage: {
    width: 60,
    height: 60,
    marginRight: 8,
    borderRadius: 6,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  nameText: {
    marginLeft: 8,
    fontWeight: 'bold',
    fontSize: 14,
  },
  dateText: {
    fontSize: 12,
    color: colors.gray.medium,
  },
  ratingRow: {
    flexDirection: 'row',
    marginTop: 6,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray.medium,
    marginTop: 10,
  },

  initialsCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: customColors.white,
    fontWeight: 'bold',
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray.dark,
  },
});
