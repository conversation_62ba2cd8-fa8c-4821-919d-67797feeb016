import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {colors} from '../../../../theme/colors';

const TABS = ['Specifications', 'Details', 'Box Content', 'More Info'];

type Props = {
  tabContent: {[key: string]: React.ReactNode};
};

const ProductInfoTabs: React.FC<Props> = ({tabContent}) => {
  const [activeTab, setActiveTab] = useState(TABS[0]);

  return (
    <View style={styles.container}>
      <View style={styles.tabRow}>
        {TABS.map(tab => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab)}
            style={styles.tabItem}>
            <Text
              style={[
                styles.tabText,
                activeTab === tab && styles.activeTabText,
              ]}>
              {tab}
            </Text>
            {activeTab === tab && <View style={styles.underline} />}
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.contentContainer}>
        {tabContent[activeTab] || <Text>No content available</Text>}
      </View>
    </View>
  );
};

export default ProductInfoTabs;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.gray.card,
    borderRadius: 16,
    padding: 15,
  },
  tabRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  tabItem: {
    alignItems: 'center',
    paddingBottom: 4,
  },
  tabText: {
    color: colors.gray.dark,
    fontWeight: '600',
  },
  activeTabText: {
    color: colors.tertiary,
  },
  underline: {
    marginTop: 4,
    height: 2,
    width: 20,
    backgroundColor: colors.tertiary,
    borderRadius: 2,
  },
  contentContainer: {
    marginTop: 10,
  },
});
