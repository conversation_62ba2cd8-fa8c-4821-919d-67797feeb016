import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

export interface ProductSpecification {
  id?: string;
  name: string;
  value: string;
}

interface DetailSectionProps {
  details: ProductSpecification[];
  title?: string;
}

const DetailSection: React.FC<DetailSectionProps> = ({
  details,
  title = undefined,
}) => {
  if (!details?.length) {
    return (
      <View style={styles.noDataView}>
        <Text style={styles.noDataText}>No data found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>

      {details.map(spec => (
        <View key={spec.id} style={styles.item}>
          <Text style={styles.name}>{spec.name}:</Text>
          <Text style={styles.value}>{spec.value}</Text>
        </View>
      ))}
    </View>
  );
};

export default DetailSection;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: customColors.white,
    borderRadius: 12,
    marginBottom: 16,
  },
  title: {
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.tertiary,
    marginBottom: 12,
    marginLeft: 10,
  },
  item: {
    flexDirection: 'row',
    marginRight: 20,
    marginLeft: 20,
    marginBottom: 8,
    justifyContent: 'space-between',
  },
  name: {
    fontWeight: '500',
    color: customColors.textBlack,
    marginRight: 6,
  },
  value: {
    color: colors.gray.dark,
    flexShrink: 1,
  },
  noDataText: {
    color: colors.gray.dark,
    fontSize: 13,
  },
  noDataView: {
    padding: 16,
    backgroundColor: customColors.white,
    borderRadius: 12,
    marginBottom: 16,
  },
});
