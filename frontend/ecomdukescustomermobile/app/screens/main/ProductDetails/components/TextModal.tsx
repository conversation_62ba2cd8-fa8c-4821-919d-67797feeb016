import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
} from 'react-native';
import ExpandableText from './ExpandableTextSection';
import customColors from '../../../../theme/customColors';
import {Icon, Modal, Portal} from 'react-native-paper';
import {colors} from '../../../../theme/colors';

type Props = {
  visible: boolean;
  onClose: () => void;
  title: string;
  content?: string;
  fallback: string;
  previewCharLimit?: number;
};

const TextModal: React.FC<Props> = ({
  visible,
  onClose,
  title,
  content,
  fallback,
  previewCharLimit = 300,
}) => {
  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onClose}
        contentContainerStyle={styles.modal}>
        <View style={styles.modalheight}>
          <View style={styles.header}>
            <Text style={styles.title}>{title}</Text>
            <TouchableOpacity onPress={onClose}>
              <Icon size={20} source={'close'} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.content}>
            <ExpandableText
              title=""
              content={content}
              fallback={fallback}
              previewCharLimit={previewCharLimit}
              numberOfLines={10}
            />
          </ScrollView>
        </View>
      </Modal>
    </Portal>
  );
};

export default TextModal;

const styles = StyleSheet.create({
  modal: {
    backgroundColor: customColors.white,
    marginHorizontal: 20,
    borderRadius: 12,
    maxHeight: '100%',
    paddingBottom: 16,
  },
  modalheight: {height: 300},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderColor: colors.gray.light,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    flex: 1,
    color: colors.tertiary,
  },

  content: {
    paddingHorizontal: 16,
  },
});
