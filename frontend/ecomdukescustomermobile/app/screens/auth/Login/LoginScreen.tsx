import React, {useCallback, useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {Divider, Text, TextInput} from 'react-native-paper';
import {FormikHelpers, useFormik} from 'formik';
import customColors from '../../../theme/customColors';
import styleConstants from '../../../theme/styleConstants';
import {STRINGS} from '../../../constants/strings';
import {
  useExchangeTokenMutation,
  useGoogleAuthCallbackMutation,
  useLazyGetUserQuery,
  useLoginMutation,
} from '../../../redux/auth/authApiSlice';
import {useDispatch} from 'react-redux';
import {
  AuthResData,
  setCredentials,
  setUserDetails,
} from '../../../redux/auth/authSlice';
import {loginValidationSchema} from '../../../validations/login';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {ILoginForm} from './types';
import {
  GoogleSignin,
  isErrorWithCode,
  isSuccessResponse,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {useFocusEffect} from '@react-navigation/native';
import CustomTextInput from '../../../components/InputFields/CustomTextInput';
import CustomButton from '../../../components/CustomButton/CustomButton';
import {commonStyles, scrollViewProps} from '../../../constants/commonStyles';
import Screenwrapper from '../../../components/ScreenWrapper/ScreenWrapper';
import {LoginScreenNavigationProp} from '../../../navigations/types';
import {
  Account,
  saveAccount,
  saveTokenForUser,
  setCurrentUser,
  StoredToken,
} from '../../../utils/AccountStorage';
import AsyncStorage from '@react-native-async-storage/async-storage';
interface ScreenProps {
  navigation: LoginScreenNavigationProp;
}
const LoginScreen: React.FC<ScreenProps> = ({navigation}) => {
  const dispatch = useDispatch();
  const [getUserDetails] = useLazyGetUserQuery();
  const [loginApi] = useLoginMutation();
  const [exchangeToken] = useExchangeTokenMutation();
  const [googleLoginApi] = useGoogleAuthCallbackMutation();
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);

  const handleLogin = async (
    values: ILoginForm,
    actions: FormikHelpers<ILoginForm>,
  ) => {
    const saveAccount = async (email: string, token: string) => {
      const stored = await AsyncStorage.getItem('accounts');
      let accounts = stored ? JSON.parse(stored) : [];

      const alreadyExists = accounts.find((acc: any) => acc.email === email);
      if (!alreadyExists) {
        accounts.push({email, token});
        await AsyncStorage.setItem('accounts', JSON.stringify(accounts));
      }

      await AsyncStorage.setItem('currentEmail', email);
      await AsyncStorage.setItem('currentToken', token);
    };

    const {resetForm} = actions;

    const response = await loginApi(values).unwrap();

    const {code} = response;

    const tokenResponse = await exchangeToken({code}).unwrap();

    if (tokenResponse) {
      dispatch(setCredentials(tokenResponse));
      const storedToken: StoredToken = {
        accessToken: tokenResponse.accessToken,
        refreshToken: tokenResponse.refreshToken,
        expires: Date.now() + 60 * 60 * 1000,
      };

      const account: Account = {
        id: Date.now(),
        email: values.username,
        token: storedToken,
      };

      await saveTokenForUser(account.email, storedToken);
      await saveAccount(values.username, tokenResponse.accessToken);
      const saved = await AsyncStorage.getItem('accounts');
      console.log('Saved accounts:', saved);

      await setCurrentUser(account.email);
      const userDetails = await getUserDetails().unwrap();

      dispatch(setUserDetails(userDetails));
      resetForm();
      navigation.navigate(SCREEN_NAME.HOME);
    }
  };
  const signInWithGoogle = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      if (isSuccessResponse(response)) {
        const tokens = await GoogleSignin.getTokens();
        const loginResponse = await googleLoginApi({code: tokens.accessToken});
        const token = loginResponse.data;

        if (token) {
          dispatch(setCredentials(token as AuthResData));
          const storedToken: StoredToken = {
            accessToken: token.accessToken,
            refreshToken: token.refreshToken,
            expires: Date.now() + 60 * 60 * 1000,
          };
          const account: Account = {
            id: Date.now(),
            email: response.data.user.email,
            token: storedToken,
          };
          await saveTokenForUser(account.email, storedToken);
          await saveAccount(account);
          await setCurrentUser(account.email);
        }

        const userDetails = await getUserDetails().unwrap();
        dispatch(setUserDetails(userDetails));
      }
    } catch (error) {
      console.log('signInWithGoogle response', error);

      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.IN_PROGRESS:
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            break;
          default:
        }
      } else {
      }
    }
  };
  const formik = useFormik<ILoginForm>({
    initialValues: {
      // username: '<EMAIL>',
      // password: 'Sujithra@2025',
      username: '',
      password: '',
    },
    validationSchema: loginValidationSchema,
    enableReinitialize: true,
    onSubmit: (values, actions) => {
      handleLogin(values, actions);
    },
  });
  useFocusEffect(
    useCallback(() => {
      setTimeout(() => {
        formik.resetForm();
        setPasswordVisible(false);
      }, 0);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []),
  );
  return (
    <Screenwrapper>
      <ScrollView {...scrollViewProps} style={styles.scrollView}>
        <View style={commonStyles.mainContainer}>
          <Text variant="titleMedium" style={styles.titleText}>
            {STRINGS.logintoyourAccount}
          </Text>
          <CustomTextInput
            title={STRINGS.emailId}
            value={formik.values.username}
            onChangeText={formik.handleChange('username')}
            onBlur={formik.handleBlur('username')}
            touched={formik.touched.username}
            errors={formik.errors.username}
          />

          <CustomTextInput
            title={STRINGS.password}
            value={formik.values.password}
            onChangeText={formik.handleChange('password')}
            onBlur={formik.handleBlur('password')}
            secureTextEntry={!passwordVisible}
            touched={formik.touched.password}
            errors={formik.errors.password}
            right={
              <TextInput.Icon
                icon={!passwordVisible ? 'eye-off' : 'eye'}
                onPress={() => setPasswordVisible(!passwordVisible)}
              />
            }
          />
          <CustomButton
            style={styles.forgotPassword}
            onPress={() => {
              formik.resetForm();
              navigation.push(SCREEN_NAME.FORGOT_PASSWORD);
            }}
            title={STRINGS.forgotPassword}
            mode="text"
            textColor={customColors.textLightGrey}
          />

          <CustomButton
            title={STRINGS.submit}
            mode="contained"
            onPress={() => formik.handleSubmit()}
            style={styles.submitButton}
            contentStyle={styles.submitButtonContent}
            disabled={formik.isSubmitting}
            loading={formik.isSubmitting}
          />
          <Text style={styles.orText} variant="bodySmall">
            {STRINGS.or}
          </Text>
          <CustomButton
            title={STRINGS.continuewithGoogle}
            mode="outlined"
            icon="google"
            onPress={() => {
              signInWithGoogle();
            }}
            textColor={customColors.outlineButtonTextGrey}
          />
          <CustomButton
            title={STRINGS.continuewithFacebook}
            mode="outlined"
            icon="facebook"
            onPress={() => {}}
            textColor={customColors.outlineButtonTextGrey}
          />
          <Divider style={styles.divider} />

          <CustomButton
            title={STRINGS.dontHaveAnAccount}
            textColor={customColors.textButtonBlue}
            onPress={() => {
              navigation.push(SCREEN_NAME.REGISTER);
            }}
            style={styles.cantLogin}
            mode="text"
          />
        </View>
      </ScrollView>
    </Screenwrapper>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  mainContainer: {
    padding: styleConstants.spacing.x20,
  },
  scrollView: {backgroundColor: customColors.white},
  titleText: {
    textAlign: 'center',
    marginBottom: styleConstants.spacing.x36,
  },
  forgotPassword: {
    alignSelf: 'flex-start',
    marginTop: styleConstants.spacing.x10,
  },
  submitButton: {
    alignSelf: 'center',
    marginTop: styleConstants.spacing.x36,
  },
  submitButtonContent: {
    paddingHorizontal: styleConstants.spacing.x20,
  },
  cantLogin: {
    marginTop: styleConstants.spacing.s10,
  },
  orText: {alignSelf: 'center', marginTop: styleConstants.spacing.x20},
  divider: {
    marginTop: styleConstants.spacing.x20,
    width: '80%',
    alignSelf: 'center',
    height: 2,
  },
});
