import * as Yup from 'yup';

export const registerValidation = Yup.object().shape({
  firstName: Yup.string()
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed')
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('First Name is required'),
  lastName: Yup.string()
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed')
    .max(255)
    .required('Last Name is required'),
  phoneNumber: Yup.string()
    .min(10, 'Please enter a valid phone number')
    .max(10, 'Please enter a valid phone number')
    .required('Phone number is required'),
  email: Yup.string().email('Must be a valid email').max(255).required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters long')
    .max(255)
    .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/\d/, 'Password must contain at least one number')
    .matches(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Confirm Password is required'),
  referralCode: Yup.string()
});
