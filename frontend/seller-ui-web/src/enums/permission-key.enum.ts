export enum PermissionKeys {
  CreateWarehouse = 'CreateWarehouse',
  ViewWarehouse = 'ViewWarehouse',
  UpdateWarehouse = 'UpdateWarehouse',
  DeleteWarehouse = 'DeleteWarehouse',
  CreateProduct = 'CreateProduct',
  ViewProduct = 'ViewProduct',
  UpdateProduct = 'UpdateProduct',
  DeleteProduct = 'DeleteProduct',
  CreateInventory = 'CreateInventory',
  ViewInventory = 'ViewInventory',
  UpdateInventory = 'UpdateInventory',
  DeleteInventory = 'DeleteInventory',
  // PromoCode Permissions
  CreatePromoCode = 'CreatePromoCode',
  ViewPromoCode = 'ViewPromoCode',
  UpdatePromoCode = 'UpdatePromoCode',
  DeletePromoCode = 'DeletePromoCode',
  // order Permissions
  CreateOrder = 'CreateOrder',
  UpdateOrder = 'UpdateOrder',
  ViewOrder = 'ViewOrder',
  DeleteOrder = 'DeleteOrder',

  ViewServiceRequest = 'ViewServiceRequest',
  CreateServiceRequest = 'CreateServiceRequest',
  UpdateServiceRequest = 'UpdateServiceRequest',
  DeleteServiceRequest = 'DeleteServiceRequest'
}
