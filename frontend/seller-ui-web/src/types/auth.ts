import { ReactNode } from 'react';
import { User } from './user-profile';
import { Dayjs } from 'dayjs';

// third-party

// ==============================|| TYPES - AUTH  ||============================== //

export type GuardProps = {
  children: ReactNode;
};

export type SignupDto = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  referralCode?: string;
  subscribeToNewsletter?: boolean;
  fcmToken?: string | null;
};

export type LoginResponse = {
  code: string;
  error?: { message: { message: string } };
};

export type SignupResponse = {
  email: string;
  password: string;
};

export interface AuthProps {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: User | null;
  token?: string | null;
}

export interface AuthActionProps {
  type: string;
  payload?: AuthProps;
}

export interface JWTDataProps {
  userId: string;
}

export type JWTContextType = {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: User | null | undefined;
  logout: () => void;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: VoidFunction;
};

export interface TokenResponse {
  code: string;
  accessToken: string;
  refreshToken: string;
  expires: number;
  pubnubToken: string;
  message?: string;
}

export enum Roles {
  GUEST = 'Guest',
  SELLER = 'Seller'
}

export interface UserFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  referralCode?: string;
  confirmPassword: string;
  countryCode?: string;
  subscribeToNewsletter?: boolean;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  submit?: any;
}

export interface ISellerStore {
  id?: string;
  storeName: string;
  legalName: string;
  website?: string;
  description?: string;
  fbId?: string;
  instaId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  dp?: File | string;
  banner?: File | string;
  logo?: File | string;
  sellerId?: string;
  signature?: File | string;
  workingHours?: number;
  hideWorkingHours?: boolean;
  allowBulkOrder?: boolean;
  allowCategorisation?: boolean;
  unavailabilityStartDate?: Date | Dayjs | null;
  unavailabilityEndDate?: Date | Dayjs | null;
}
export interface ISellerStorePayload {
  id?: string;
  storeName: string;
  legalName: string;
  website?: string;
  description?: string;
  fbId?: string;
  instaId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  dp?: File | string;
  banner?: File | string;
  logo?: File | string;
  sellerId?: string;
  signature?: File | string;
  workingHours?: number;
  hideWorkingHours?: boolean;
  allowBulkOrder?: boolean;
  allowCategorisation?: boolean;
  unavailabilityStartDate?: string | null;
  unavailabilityEndDate?: string | null;
}

export interface FirmDocument {
  id?: string;
  documentName: string;
  documentValue: string;
}
export interface FirmDocumentDto {
  documentName: string;
  documentValue: File;
}
export interface IFirmDetails {
  id?: string;
  gstNumber?: string;
  gstName: string;
  website?: string;
  panNumber?: string;
  typeOfFirm?: string;
  enrollmentNumber?: string;
  firmDocuments?: { documentName: string; documentValue: string }[];
  firmDocumentsRelation?: FirmDocument[];
}
export interface SampleProduct {
  id: string;
  name: string;
  thumbnail: string[];
  sellerId?: string;
}

export interface ISeller {
  id: string;
  sellerId: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  verificationCode: string;
  status: string;
  rejectionReason?: string;
  userTenantId: string;
  sampleProductImages: SampleProduct[];
}
export interface Faq {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string;
  createdBy: string | null;
  modifiedBy: string | null;
  id: string;
  question: string;
  answer: string;
  category: string;
  status: number;
  priority: number;
  visibility: number;
}

export enum Gender {
  Male = 'M',
  Female = 'F',
  Other = 'O'
}

export interface PlanPricing {
  id: string;
  planId: string;
  minSalesThreshold: number;
  maxSalesThreshold: number;
  price: string; // You can change this to `number` if you plan to treat it numerically
  modifiedBy: string | null;
  modifiedOn: string; // ISO date string
}
export interface Plan {
  id: string;
  name: string;
  key: string;
  status: string;
  currency: string;
  amount: number;
  createdAt: string;
  modifiedAt: string;
  planFeatureValues: PlanFeatureValue[];
  planPricings: PlanPricing[];
}

interface PlanFeatureValue {
  id: string;
  planId: string;
  featureValueId: string;
  createdAt: string;
  modifiedAt: string;
  featureValue: FeatureValue;
}

interface FeatureValue {
  id: string;
  value: string;
  key: string;
  valueType: string;
  featureId: string;
  createdAt: string;
  modifiedAt: string;
  feature: Feature;
}

export interface Feature {
  id: string;
  name: string;
  key: string;
  createdAt: string;
  modifiedAt: string;
}
export interface Subscription {
  id?: string;
  subscriberId: string;
  startDate: string;
  endDate?: string;
  status?: SubscriptionStatus;
  planId: string;
}
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}
export interface ChangePasswordRequest {
  username: string;
  password: string;
  oldPassword: string;
  refreshToken: string;
}
export interface BankDetailsType {
  accountNumber: string;
  accountHolder: string;
  ifsc: string;
  uidai?: string;
  gst?: string;
  pan?: string;
  accountType?: string;
  businessType?: string;
}

export interface UnavailabilityRange {
  startDate: Date | null;
  endDate: Date | null;
}
