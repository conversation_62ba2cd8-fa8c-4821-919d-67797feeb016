export enum ServiceRequestStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export interface EcomdukeserviceRequest {
  id?: string;
  sellerId: string;
  ecomdukeserviceId: string;
  status: string;
  paymentReference?: number;
  paidAmount?: number | string; // Since API sometimes returns string
  paidOn?: string;
  notes?: string;
  createdOn?: string;
  updatedOn?: string;
  createdBy?: string;
  updatedBy?: string;

  // Related entities
  seller?: {
    id: string;
    sellerId: string;
    userTenant?: {
      id: string;
      user?: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        photoUrl?: string;
      };
    };
  };

  ecomdukeservice?: {
    id: string;
    name: string;
    description?: string;
    price: number | string;
    currency: string;
  };
}

export interface EcomdukeService {
  id?: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  taxCategoryId: string;
  isActive: boolean;
  createdOn?: string;
  updatedOn?: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface CashfreePaymentLinkResponse {
  cf_link_id: string;
  link_id: string;
  link_status: string;
  link_currency: string;
  link_amount: number;
  link_amount_paid: number;
  link_partial_payments: boolean;
  link_minimum_partial_amount: number;
  link_purpose: string;
  link_created_at: string;
  customer_details: {
    customer_name: string;
    customer_phone: string;
    customer_email: string;
  };
  link_meta: {
    notify_url?: string;
    upi_intent?: boolean;
    return_url?: string;
  };
  link_url: string;
  link_expiry_time: string;
  link_notes?: Record<string, string>;
  link_auto_reminders?: boolean;
  link_qrcode?: string;
  link_notify?: {
    send_sms?: boolean;
    send_email?: boolean;
  };
  order_splits?: Array<{
    vendor_id: string;
    percentage: number;
    tags?: Record<string, string>;
  }>;
}
