export interface ShippingCharge {
  id?: string;
  shippingProfileId?: string;
  countryCode: string;
  stateCode?: string;
  baseCharge: number;
  additionalItemPrice?: number;
  estimatedDaysMin: number;
  estimatedDaysMax: number;
}

export interface WeightBasedRule {
  id?: string;
  shippingProfileId?: string;
  minWeight: number;
  maxWeight: number;
  charge: number;
}

export interface ProductShippingCharge {
  id?: string;
  shippingProfileId?: string;
  productId: string;
  baseCharge: number;
}

export enum ShippingMethodType {
  ECOMDUKES = 'ECOMDUKES',
  SELF_SHIPPING = 'SELF_SHIPPING'
}

export interface ShippingMethod {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  type: ShippingMethodType;
}

export interface ShippingProfile {
  id?: string;
  name: string;
  description?: string;
  isDefault?: boolean;
  isActive?: boolean;
  sellerId: string;
  shippingMethodId: string;
  shippingMethod?: ShippingMethod;
  shippingCharges?: ShippingCharge[];
  weightBasedRules?: WeightBasedRule[];
  productShippingCharges?: ProductShippingCharge[];
}
