import { MouseEvent, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import { useAppSelector } from 'redux/hooks';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { Logout } from 'iconsax-react';
import { ListItemIcon } from '@mui/material';

// ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //
const protectedLinks = [
  // Core business
  { link: '/orders', name: 'Orders' },
  { link: '/returns', name: 'Returns & Refunds' },
  { link: '/payments', name: 'Payments' },

  // Product & inventory management
  { link: '/products', name: 'Products' },
  { link: '/inventory', name: 'Inventory' },
  { link: '/warehouse', name: 'Warehouse' },

  // Marketing & promotion
  { link: '/promo-code', name: 'Promo Code' },

  // Account & store settings
  { link: '/account/profile', name: 'My Profile' },
  { link: '/account/bank-details', name: 'Bank Details' },

  // Communication tools
  { link: '/chat', name: 'Chat' }
];
const openLinks = [
  { link: '/faq', name: 'FAQ' },
  { link: '/support', name: 'Support' }
];

interface Props {
  handleLogout: () => void;
  handleClose: (event: MouseEvent | TouchEvent) => void;
}

export default function ProfileTab({ handleLogout, handleClose }: Props) {
  const router = useRouter();
  const isLoggedIn = useAppSelector((state) => state.auth.isLoggedIn);
  const { data: user } = useGetUserQuery(undefined, {
    skip: !isLoggedIn
  });

  const path = usePathname();

  const handleListItemClick = (event: MouseEvent<HTMLDivElement>, index: number, route?: string) => {
    if (route) {
      router.push(route);
      handleClose(event);
    }
  };

  const navLinks = useMemo(() => {
    let links = [...openLinks];
    if (user?.onBoardComplete) {
      links = [...protectedLinks, ...openLinks];
    }
    return links;
  }, [user]);

  return (
    <List component="nav" sx={{ p: 0 }}>
      {navLinks.map((item, index) => (
        <ListItemButton key={item.name} selected={path === item.link} onClick={(e) => handleListItemClick(e, index, item.link)}>
          <ListItemText primary={item.name} />
        </ListItemButton>
      ))}
      <ListItemButton onClick={handleLogout}>
        <ListItemIcon>
          <Logout variant="Bulk" />
        </ListItemIcon>
        <ListItemText primary="Logout" />
      </ListItemButton>
    </List>
  );
}
