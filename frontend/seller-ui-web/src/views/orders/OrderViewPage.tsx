'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, Typography, Box, Grid, Button, CardMedia, Dialog, DialogContent, DialogActions } from '@mui/material';
import { useGetOrderDetailsByIdQuery, useUpdateOrderMutation } from 'redux/app/order/orderApiSlice';
import Loader from 'components/Loader';
import { OrderItemStatus } from 'enums/orderStatus';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { TextField } from '@mui/material';
import { useRouter } from 'next/navigation';

interface OrderViewPageProps {
  orderId: string;
  refetch: () => Promise<void> | any;
}

function OrderViewPage({ orderId, refetch: refreshOrderList }: OrderViewPageProps) {
  const [open, setOpen] = React.useState(false);
  const [updateSellerStatus, { error: updateError, reset: updateReset }] = useUpdateOrderMutation();
  const handleError = useApiErrorHandler();
  const router = useRouter();

  const [rejectionReason, setRejectionReason] = React.useState('');

  const filter = {
    include: [
      {
        relation: 'order',
        required: true,
        scope: {
          include: [
            {
              relation: 'promoCode'
            },
            { relation: 'shippingAddress' }
          ]
        }
      },
      {
        relation: 'productVariant',
        required: true,
        scope: {
          include: [
            {
              relation: 'product'
            },
            {
              relation: 'featuredAsset'
            }
          ]
        }
      },
      {
        relation: 'customizationValues',
        scope: {
          include: [
            {
              relation: 'customizationField'
            }
          ]
        }
      }
    ]
  };
  const { data, isLoading, error, refetch } = useGetOrderDetailsByIdQuery({
    id: orderId,
    filter
  });
  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateError]);
  const handleClose = () => {
    setOpen(false);
    setRejectionReason('');
  };

  const handleUpdateStatus = async (status: OrderItemStatus, reason?: string) => {
    if (!data?.id) return;
    await updateSellerStatus({
      id: data.id,
      rejectionReason: reason ?? '',
      data: { status }
    }).unwrap();
    openSnackbar({
      open: true,
      message: 'Seller status updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push('/orders');

    refetch();
    refreshOrderList();
    handleClose();
  };

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching products</Typography>;

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12} key={data.id}>
        <Card sx={{ boxShadow: 3 }}>
          <CardContent>
            <Grid container spacing={0.5}>
              <Grid item xs={12} sm={3} display="flex" justifyContent="center" alignItems="flex-start">
                <CardMedia
                  component="img"
                  sx={{
                    width: { xs: '100%', sm: 100 },
                    height: 100,
                    borderRadius: 2,
                    objectFit: 'contain',
                    cursor: 'pointer'
                  }}
                  image={data.productVariant?.featuredAsset?.previewUrl ?? ''}
                  alt={data?.productVariant?.name}
                />
              </Grid>
              <Grid item xs={6} sm={9}>
                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h5" sx={{ textAlign: 'left' }}>
                        Product ID
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <Typography variant="h5" sx={{ textAlign: 'left' }}>
                        {' '}
                        {data.productVariant?.product?.productId ?? '---'}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Product price :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <Typography variant="h5">{data.unitPrice}</Typography>
                    </Grid>
                  </Grid>
                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Delivery Details :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      {data?.order?.shippingAddress ? (
                        <>
                          <Typography variant="h5">
                            {data.order?.shippingAddress?.addressLine1} {data.order?.shippingAddress?.addressLine2},{' '}
                          </Typography>
                          <Typography variant="h5">
                            {data.order?.shippingAddress?.locality},{data.order?.shippingAddress?.city},{' '}
                            {data.order?.shippingAddress?.state} - {data.order?.shippingAddress?.zipCode},{' '}
                            {data.order?.shippingAddress?.country}
                          </Typography>
                        </>
                      ) : (
                        <Typography variant="h5">Address not available</Typography>
                      )}
                    </Grid>
                  </Grid>

                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Offers Applied :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <Typography variant="h5"> {data.order?.promoCode?.code ?? '---'}</Typography>
                    </Grid>
                  </Grid>
                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Customize :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      {data.customizationValues?.length ? (
                        data.customizationValues.map((item, index) => (
                          <Typography variant="h5" key={index}>
                            {item.customizationField?.label || item.customizationField?.name}: {item.value}
                          </Typography>
                        ))
                      ) : (
                        <Typography variant="h5">---</Typography>
                      )}
                    </Grid>
                  </Grid>

                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Note :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <Typography variant="h5">Additional information</Typography>
                    </Grid>
                  </Grid>
                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Wrapping :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <Typography variant="h5">
                        {data.productVariant?.product?.isGiftWrapAvailable === true
                          ? 'Yes'
                          : data.productVariant?.product?.isGiftWrapAvailable === false
                            ? 'No'
                            : '---'}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Grid container sx={{ marginBottom: 1 }}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="h6">Payment :</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <Typography variant="h5">{data.totalPrice}</Typography>
                    </Grid>
                  </Grid>
                  {data.status === OrderItemStatus.Rejected && (
                    <Grid container sx={{ marginBottom: 1 }}>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="h6">Reason for Reject :</Typography>
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        <Typography variant="h5">{data.rejectionReason || 'No reason provided'}</Typography>
                      </Grid>
                    </Grid>
                  )}
                  {data.status === OrderItemStatus.Pending && (
                    <Grid container justifyContent="space-between" alignItems="center" sx={{ mt: 2 }}>
                      <Grid item>
                        <Box display="flex" gap={2} mt={2}>
                          <Button variant="contained" color="primary" onClick={() => handleUpdateStatus(OrderItemStatus.Accepted)}>
                            Accept Order
                          </Button>

                          <Button variant="contained" color="error" onClick={() => setOpen(true)}>
                            Reject Order
                          </Button>

                          <Dialog open={open} onClose={handleClose}>
                            <form
                              onSubmit={(event: React.FormEvent<HTMLFormElement>) => {
                                event.preventDefault();
                                const formData = new FormData(event.currentTarget);
                                const formJson = Object.fromEntries(formData.entries());
                                const rejectionReason = formJson.reason as string;
                                handleUpdateStatus(OrderItemStatus.Rejected, rejectionReason);
                              }}
                            >
                              <DialogContent>
                                <Typography variant="h6">Reject Order</Typography>
                                <Typography variant="body2" gutterBottom>
                                  Please provide a reason for rejection.
                                </Typography>
                                <TextField
                                  autoFocus
                                  required
                                  fullWidth
                                  margin="dense"
                                  id="reason"
                                  name="reason"
                                  label="Rejection Reason"
                                  variant="standard"
                                  value={rejectionReason}
                                  onChange={(e) => setRejectionReason(e.target.value)}
                                />
                              </DialogContent>
                              <DialogActions>
                                <Button onClick={handleClose}>Cancel</Button>
                                <Button type="submit" color="error">
                                  Submit
                                </Button>
                              </DialogActions>
                            </form>
                          </Dialog>
                        </Box>
                      </Grid>
                    </Grid>
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

export default OrderViewPage;
