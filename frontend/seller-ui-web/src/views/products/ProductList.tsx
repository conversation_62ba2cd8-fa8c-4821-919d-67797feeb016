'use client';

import { useState, useEffect, useMemo, MouseEvent, useCallback } from 'react';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import moment from 'moment-timezone';

import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';

import { Add, ArrowLeft, Edit, Eye, Trash } from 'iconsax-react';

import IconButton from '../../components/@extended/IconButton';
import IndeterminateCheckbox from '../../components/third-party/react-table/IndeterminateCheckbox';
import { ProductTable } from './ProductTable';
import { PermissionKeys } from 'enums/permission-key.enum';

import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';

import { useAuth } from 'contexts/AuthContext';
import AlertProductDelete from './AlertProductDelete';
import withPermission from 'hoc/withPermission';

import {
  useCreatePinnedProductMutation,
  useDeletePinnedProductMutation,
  useGetPinnedProductsQuery,
  useGetproductCountQuery,
  useGetproductsQuery
} from 'redux/app/products/productApiSlice';

import { Product } from 'types/product';
import { useRouter } from 'next/navigation';
import { IFilter } from 'redux/app/types/filter';
import Loader from 'components/Loader';
import { ProductVariant } from 'types/product-variant';
import Image from 'next/image';
import { ProductStatus } from 'enums/product-status.enum';
import { Chip } from '@mui/material';

import { DEFAULT_DATE_FORMAT, ThemeMode } from 'config';

const ProductList = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [idTobeDeleted, setIdTobedeleted] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);

  const canCreate = hasPermission(PermissionKeys.CreateProduct);
  const canEdit = hasPermission(PermissionKeys.UpdateProduct);
  const canDelete = hasPermission(PermissionKeys.DeleteProduct);

  const { data: pinnedProducts = [], refetch: refetchPinned } = useGetPinnedProductsQuery({});
  const [createPinnedProduct] = useCreatePinnedProductMutation();
  const [deletePinnedProduct] = useDeletePinnedProductMutation();

  const isPinned = (variantId: string) => pinnedProducts.some((p) => p.productVariantId === variantId);

  const handleClose = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const loopbackFilter = useMemo(
    (): IFilter => ({
      order: convertSortingToLoopbackSort(sorting),
      where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'slug']),
      ...convertPaginationToLoopback(pagination),
      include: [{ relation: 'productAssets' }, { relation: 'featuredAsset' }, { relation: 'productVariants' }]
    }),
    [sorting, columnFilters, globalFilter, pagination]
  );
  const { data: productList, isLoading: productListLoading, refetch } = useGetproductsQuery(loopbackFilter);

  const finalProductList = useMemo(() => {
    if (!productList) return [];

    const pinnedVariantIds = new Set(pinnedProducts.map((p) => p.productVariantId));

    const productIdToVariantId = new Map(
      productList.filter((p) => p.productVariants && p.productVariants.length > 0).map((p) => [p.id, p.productVariants[0].id])
    );

    return [...productList].sort((a, b) => {
      const aPinned = pinnedVariantIds.has(productIdToVariantId.get(a.id) ?? '');
      const bPinned = pinnedVariantIds.has(productIdToVariantId.get(b.id) ?? '');
      return Number(bPinned) - Number(aPinned);
    });
  }, [pinnedProducts, productList]);

  const handleTogglePin = async (variant: ProductVariant) => {
    if (!variant?.id || !variant?.productId) return;

    try {
      const currentPinned = pinnedProducts.find((pinned) => {
        const match = productList?.flatMap((p) => p.productVariants || []).find((v) => v.id === pinned.productVariantId);
        return match?.productId === variant.productId;
      });

      const isSame = currentPinned?.productVariantId === variant.id;

      if (isSame && currentPinned?.id) {
        await deletePinnedProduct(currentPinned.id).unwrap();
      } else {
        if (currentPinned?.id) {
          await deletePinnedProduct(currentPinned.id).unwrap();
          await refetchPinned();
        }
        await createPinnedProduct({ productVariantId: variant.id }).unwrap();
      }

      await refetchPinned();
    } catch (error) {
      console.error('Failed to toggle pin:', error);
    }
  };

  const countWhereFilter = useMemo(() => {
    return convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'slug']);
  }, [columnFilters, globalFilter]);

  const {
    data: productCount,
    isLoading: countLoading,
    refetch: countRefetch
  } = useGetproductCountQuery({ where: countWhereFilter, include: [] });

  useEffect(() => {
    countRefetch();
  }, [countWhereFilter]);

  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  useEffect(() => {
    refetch();
  }, [loopbackFilter]);
  const columns = useMemo<ColumnDef<Product>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Product ID',
        accessorKey: 'featuredAsset',
        cell: ({ row }) => {
          const imageUrl = row.original?.featuredAsset?.previewUrl;
          const productId = row.original?.productId;

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt="Product"
                  style={{
                    width: 40,
                    height: 40,
                    objectFit: 'cover',
                    borderRadius: 4,
                    marginRight: 26
                  }}
                />
              ) : (
                <span>-</span>
              )}
              <span>{productId}</span>
            </div>
          );
        },
        meta: { className: 'cell-center' }
      },
      { header: 'Name', accessorKey: 'name' },
      { header: 'Slug', accessorKey: 'slug' },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ row }) => {
          const status = row.original?.status;
          if (!status) return <span>-</span>;

          const getStatusColor = (status: ProductStatus) => {
            switch (status) {
              case ProductStatus.APPROVED:
                return 'success';
              case ProductStatus.REJECTED:
                return 'error';
              case ProductStatus.PENDING:
              default:
                return 'warning';
            }
          };

          const getStatusLabel = (status: ProductStatus) => {
            switch (status) {
              case ProductStatus.APPROVED:
                return 'Approved';
              case ProductStatus.REJECTED:
                return 'Rejected';
              case ProductStatus.PENDING:
              default:
                return 'Pending';
            }
          };

          return <Chip label={getStatusLabel(status)} color={getStatusColor(status)} size="small" variant="outlined" />;
        },
        meta: { className: 'cell-center' }
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format(DEFAULT_DATE_FORMAT) ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        meta: { className: 'cell-center' },
        enableSorting: false,
        cell: ({ row }) => {
          const product = row.original;
          const pinnedVariant = product.productVariants?.find((v) => isPinned(v.id));
          const pinned = !!pinnedVariant;
          const variant = pinnedVariant ?? product.productVariants?.[0];

          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add style={{ color: theme.palette.error.main, transform: 'rotate(45deg)' }} />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title={pinned ? 'Unpin Product' : 'Pin Product'}>
                <IconButton onClick={() => variant && handleTogglePin(variant)} color="primary" disabled={!variant}>
                  <Image
                    src={pinned ? '/assets/images/icons8-pin-25.png' : '/assets/images/icons8-pin-48.png'}
                    alt={pinned ? 'Pinned' : 'Unpinned'}
                    width={24}
                    height={24}
                  />
                </IconButton>
              </Tooltip>

              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>

              <Tooltip title="Edit">
                <IconButton
                  color="primary"
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/products/edit/${product.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    setOpen(true);
                    setIdTobedeleted(product.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, canDelete, canEdit, router, pinnedProducts]
  );

  return (
    <>
      {selectedProduct ? (
        <>
          <IconButton onClick={() => setSelectedProduct(null)} sx={{ mb: 2 }}>
            <ArrowLeft />
          </IconButton>
          <p>Coming Soon</p>
        </>
      ) : countLoading || productListLoading ? (
        <Loader />
      ) : (
        <ProductTable
          // data={productList || []}
          data={finalProductList || []}
          columns={columns}
          setSorting={setSorting}
          sorting={sorting}
          filters={columnFilters}
          setFilters={setColumnFilters}
          loading={productListLoading || countLoading}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          pagination={pagination}
          setPagination={setPagination}
          totalRows={(productCount?.count as number) ?? 0}
          refetch={refetch}
          canCreate={canCreate}
        />
      )}

      <AlertProductDelete
        refetch={refetch}
        id={idTobeDeleted}
        title={productList?.find((product) => product.id === idTobeDeleted)?.name ?? idTobeDeleted}
        open={open}
        handleClose={handleClose}
      />
    </>
  );
};

export default withPermission(PermissionKeys.ViewProduct)(ProductList);
