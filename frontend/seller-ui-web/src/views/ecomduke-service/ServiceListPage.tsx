'use client';

import { useMemo, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import IconButton from 'components/@extended/IconButton';
import { Add, Eye } from 'iconsax-react';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import { useAuth } from 'contexts/AuthContext';
import Loader from 'components/Loader';
import withPermission from 'hoc/withPermission';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import { useGetServiceRequestCountQuery, useGetServiceRequestsQuery } from 'redux/app/service-request/requestApislice';
import { PermissionKeys } from 'enums/permission-key.enum';
import { EcomdukeserviceRequest } from 'types/service-request';
import ServiceRequestTable from './ServiceTable';

const ServiceRequestListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const canCreate = hasPermission(PermissionKeys.CreateServiceRequest);

  const {
    data: serviceRequests,
    isLoading: isListLoading,
    refetch
  } = useGetServiceRequestsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['status', 'notes']),
    ...convertPaginationToLoopback(pagination),
    include: [{ relation: 'seller' }, { relation: 'ecomdukeservice' }]
  });

  const { data: requestCount, isLoading: isCountLoading } = useGetServiceRequestCountQuery({
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['status'])
  });

  useEffect(() => {
    refetch?.();
  }, [refetch]);

  const columns = useMemo<ColumnDef<EcomdukeserviceRequest>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Seller',
        accessorKey: 'seller',
        cell: ({ row }) => (
          <Typography>
            {' '}
            {row.original.seller?.userTenant?.user
              ? `${row.original.seller.userTenant.user.firstName} ${row.original.seller.userTenant.user.lastName}`
              : '-'}
          </Typography>
        )
      },
      {
        header: 'Service',
        accessorKey: 'service',
        cell: ({ row }) => <Typography>{row.original.ecomdukeservice?.name ?? '-'}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ row }) => <Typography>{row.original.status}</Typography>
      },
      {
        header: 'Paid Amount',
        accessorKey: 'paidAmount',
        cell: ({ row }) => <Typography>{row.original.paidAmount ?? '-'}</Typography>
      },
      {
        header: 'Paid On',
        accessorKey: 'paidOn',
        cell: ({ row }) => <Typography>{row.original.paidOn ? new Date(row.original.paidOn).toLocaleDateString() : '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const expandIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add style={{ color: theme.palette.error.main, transform: 'rotate(45deg)' }} />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" spacing={0}>
              <Tooltip title="View">
                <IconButton onClick={row.getToggleExpandedHandler()}>{expandIcon}</IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router]
  );

  return (
    <>
      {isListLoading || isCountLoading ? (
        <Loader />
      ) : (
        <ServiceRequestTable
          data={serviceRequests || []}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          pagination={pagination}
          setPagination={setPagination}
          totalRows={requestCount?.count ?? 0}
          canCreate={canCreate}
          loading={isListLoading} // ✅ You missed this
          refetch={refetch}
        />
      )}
    </>
  );
};

export default withPermission(PermissionKeys.ViewServiceRequest)(ServiceRequestListPage);
