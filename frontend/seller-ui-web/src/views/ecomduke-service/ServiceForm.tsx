'use client';

import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Grid, MenuItem, TextField, Typography, CircularProgress } from '@mui/material';
import { useFormik } from 'formik';
import {
  useCreateServiceRequestMutation,
  useGetServicesQuery,
  useCreatePaymentLinkMutation
} from 'redux/app/service-request/requestApislice';

export default function EcomDukeserviceRequestCreate() {
  const [createRequest] = useCreateServiceRequestMutation();
  const [createPaymentLink] = useCreatePaymentLinkMutation();

  const { data: services, isLoading } = useGetServicesQuery();

  const formik = useFormik({
    initialValues: {
      ecomdukeserviceId: '',
      notes: ''
    },
    onSubmit: async (values, { resetForm }) => {
      const selectedService = services?.find((s) => s.id === values.ecomdukeserviceId);
      if (!selectedService) {
        return;
      }
      const payload = {
        ecomdukeserviceId: values.ecomdukeserviceId,
        notes: values.notes,
        paidAmount: Number(selectedService?.price),
        status: 'PENDING'
      };
      const request = await createRequest(payload).unwrap();

      if (!request?.id) {
        return;
      }
      const paymentResponse = await createPaymentLink({
        linkAmount: Number(selectedService.price),
        linkNotes: {
          ecomdukeserviceRequestId: request.id
        }
      }).unwrap();
      if (paymentResponse?.link_url) {
        window.open(paymentResponse.link_url, '_blank');
      }
      console.log('🚀 ~ onSubmit: ~ paymentResponse:', paymentResponse);

      resetForm();
    }
  });

  const selectedService = services?.find((s) => s.id === formik.values.ecomdukeserviceId);

  return (
    <Card sx={{ mt: 4, p: 2, mb: 4 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Select a Service
        </Typography>

        {isLoading ? (
          <Box display="flex" justifyContent="center" my={3}>
            <CircularProgress />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  name="ecomdukeserviceId"
                  label="Service"
                  value={formik.values.ecomdukeserviceId}
                  onChange={formik.handleChange}
                >
                  {services?.map((service) => (
                    <MenuItem key={service.id} value={service.id}>
                      {service.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              {selectedService && (
                <>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">
                      <strong>Payable Amount:</strong> ₹{selectedService.price}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      name="notes"
                      label="Notes"
                      value={formik.values.notes}
                      onChange={formik.handleChange}
                    />
                  </Grid>

                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button variant="contained" type="submit">
                      Submit Request
                    </Button>
                  </Grid>
                </>
              )}
            </Grid>
          </form>
        )}
      </CardContent>
    </Card>
  );
}
