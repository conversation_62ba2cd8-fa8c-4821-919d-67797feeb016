'use client';

import React, { useEffect } from 'react';
import { Grid, Card, CardContent, Typography, Box } from '@mui/material';
import Loader from 'components/Loader';
import { useGetServiceRequestByIdQuery, useGetServicesQuery } from 'redux/app/service-request/requestApislice';

type Props = {
  requestId: string;
};

const ViewServiceRequest = ({ requestId }: Props) => {
  const { data, isLoading, error, refetch } = useGetServiceRequestByIdQuery({ id: requestId });
  const { data: allServices = [] } = useGetServicesQuery();

  const service = allServices.find((s: any) => s.id === data?.ecomdukeserviceId);
  const serviceName = service?.name ?? 'N/A';
  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching service request.</Typography>;

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Service Request Details
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Service Name" value={serviceName} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem
                  label="Seller"
                  value={
                    data.seller?.userTenant?.user ? `${data.seller.userTenant.user.firstName} ${data.seller.userTenant.user.lastName}` : '-'
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Status" value={data.status} />
              </Grid>
              {data.paidAmount !== undefined && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid Amount" value={data.paidAmount} />
                </Grid>
              )}
              {data.paymentReference && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Payment Reference" value={data.paymentReference} />
                </Grid>
              )}
              {data.paidOn && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid On" value={new Date(data.paidOn).toLocaleString()} />
                </Grid>
              )}
              {data.notes && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Notes" value={data.notes} isMultiline />
                </Grid>
              )}
              {data.paymentReference && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Payment Reference" value={data.paymentReference} />
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const DisplayItem = ({ label, value, isMultiline = false }: { label: string; value: string | number; isMultiline?: boolean }) => (
  <Box sx={{ mb: 1.5 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary',
        whiteSpace: isMultiline ? 'pre-line' : 'nowrap'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default ViewServiceRequest;
