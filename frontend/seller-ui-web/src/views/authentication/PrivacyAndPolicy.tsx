'use client';
import { Grid } from '@mui/material';
import { useGetPrivacyAndPolicyQuery } from 'redux/app/privacy-and-policy/privacyApiSlice';
import { setGuestToken } from '../../redux/auth/authSlice';
import draftToHtml from 'draftjs-to-html';
import { useGetGuestTokenMutation } from 'redux/app/terms-and-condition/termsApiSlice';
import { useDispatch } from 'react-redux';
import { useEffect, useState } from 'react';
import { Typography } from '@mui/material';
import { CardContent } from '@mui/material';
import { Card } from '@mui/material';
import { Legals } from 'enums/legal-category.enum';

export default function PrivacyAndPolicy() {
  const [guestCode, setGuestCode] = useState<string | null>(null);

  const [getToken] = useGetGuestTokenMutation();
  const dispatch = useDispatch();

  useEffect(() => {
    const handleLogin = async () => {
      const result = await getToken().unwrap();
      if (result?.accessToken) {
        dispatch(setGuestToken(result));
        setGuestCode(result.accessToken);
      }
    };

    handleLogin();
  }, [getToken, dispatch]);

  const { data: privacyData, isLoading } = useGetPrivacyAndPolicyQuery(guestCode!, {
    skip: !guestCode
  });

  const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
  return (
    <Grid container sx={{ p: { xs: 2, sm: 4 } }}>
      <Grid item xs={12}>
        <Card sx={{ borderRadius: 3, p: 3 }}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : privacyData?.length ? (
            privacyData
              .filter((term) => term.type === Legals.PrivacyPolicy)
              .map((privacy, index) => {
                let htmlContent: string;
                try {
                  if (typeof privacy.data === 'string') {
                    htmlContent = isHtml(privacy.data) ? privacy.data : draftToHtml(JSON.parse(privacy.data));
                  } else {
                    htmlContent = draftToHtml(privacy.data);
                  }
                } catch (err) {
                  // eslint-disable-next-line no-console
                  console.error('Failed to convert term data:', err);
                  htmlContent = '<p>Error loading content.</p>';
                }

                return (
                  <CardContent key={index}>
                    <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
                  </CardContent>
                );
              })
          ) : (
            <Typography>No Privacy Policy available.</Typography>
          )}
        </Card>
      </Grid>
    </Grid>
  );
}
