'use client';

import { useState } from 'react';
import { Stack, Typography, Avatar, IconButton, Divider, Collapse, Switch, useTheme } from '@mui/material';

import { ArrowDown2, ArrowRight2, Document, DocumentLike, Image as ImageIcon, Mobile, Sms, Camera, More, Add } from 'iconsax-react';

import MainCard from 'components/MainCard';
import { ThemeMode } from 'config';
import { User } from 'types/user-profile';

type Props = {
  user: User & { presignedPhotoUrl?: string };
  onClose?: () => void;
};

export default function UserDetails({ user, onClose }: Props) {
  const theme = useTheme();
  const [infoExpanded, setInfoExpanded] = useState(true);

  if (!user || Object.keys(user).length === 0) return <Typography>...Loading</Typography>;

  return (
    <MainCard
      content={false}
      sx={{
        bgcolor: theme.palette.mode === ThemeMode.DARK ? 'dark.main' : 'common.white',
        borderRadius: '0 4px 4px 0',
        borderLeft: 'none',
        height: '100%',
        position: 'relative',
        px: 3,
        py: 3
      }}
    >
      {onClose && (
        <IconButton size="small" onClick={onClose} sx={{ position: 'absolute', top: 12, right: 12 }} color="error">
          <Add style={{ transform: 'rotate(45deg)' }} />
        </IconButton>
      )}

      <Stack spacing={2} alignItems="center">
        <Avatar
          src={user.presignedPhotoUrl ?? user.photoUrl ?? ''}
          alt={`${user.firstName} ${user.lastName}`}
          sx={{
            width: 80,
            height: 80,
            border: `2px solid ${theme.palette.primary.main}`
          }}
        />
        <Typography variant="h5">{`${user.firstName} ${user.lastName}`}</Typography>
        <Typography color="text.secondary" variant="body2">
          {user.role ?? 'Customer'}
        </Typography>

        <Stack direction="row" spacing={2} sx={{ mt: 1 }}>
          <IconButton size="medium" color="secondary">
            <Mobile />
          </IconButton>
          <IconButton size="medium" color="secondary">
            <Sms />
          </IconButton>
          <IconButton size="medium" color="secondary">
            <Camera />
          </IconButton>
        </Stack>
      </Stack>

      <Divider sx={{ my: 2 }} />

      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between" onClick={() => setInfoExpanded(!infoExpanded)} sx={{ cursor: 'pointer' }}>
          <Typography variant="h6">Information</Typography>
          <IconButton size="small" color="secondary">
            <ArrowDown2 />
          </IconButton>
        </Stack>

        <Collapse in={infoExpanded}>
          <Stack spacing={1}>
            <InfoRow label="Address" value={user.firstName ?? '-'} />
            <InfoRow label="Email" value={user.email ?? '-'} />
            <InfoRow label="Last visited" value={user.lastLogin ?? '-'} />
          </Stack>
        </Collapse>

        <Divider />

        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Notifications</Typography>
          <Switch defaultChecked />
        </Stack>

        <Divider />

        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">File Types</Typography>
          <IconButton size="small" sx={{ transform: 'rotate(90deg)' }}>
            <More />
          </IconButton>
        </Stack>

        <FileStat
          icon={<DocumentLike />}
          title="Documents"
          subtitle="123 files, 193MB"
          iconColor={theme.palette.success.main}
          iconBg={theme.palette.success.lighter}
        />
        <FileStat
          icon={<ImageIcon />}
          title="Photos"
          subtitle="53 files, 321MB"
          iconColor={theme.palette.warning.main}
          iconBg={theme.palette.warning.lighter}
        />
        <FileStat
          icon={<Document />}
          title="Other"
          subtitle="49 files, 193MB"
          iconColor={theme.palette.primary.main}
          iconBg={theme.palette.primary.lighter}
        />
      </Stack>
    </MainCard>
  );
}

// Helper: Info row
function InfoRow({ label, value }: { label: string; value: string }) {
  return (
    <Stack direction="row" justifyContent="space-between">
      <Typography variant="body2">{label}</Typography>
      <Typography variant="body2" color="text.secondary">
        {value}
      </Typography>
    </Stack>
  );
}

// Helper: File statistic
function FileStat({
  icon,
  title,
  subtitle,
  iconColor,
  iconBg
}: {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  iconColor: string;
  iconBg: string;
}) {
  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center">
      <Stack direction="row" spacing={1.5} alignItems="center">
        <Avatar sx={{ bgcolor: iconBg, color: iconColor, borderRadius: 1 }}>{icon}</Avatar>
        <Stack>
          <Typography variant="body1">{title}</Typography>
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        </Stack>
      </Stack>
      <IconButton size="small" color="secondary">
        <ArrowRight2 />
      </IconButton>
    </Stack>
  );
}
