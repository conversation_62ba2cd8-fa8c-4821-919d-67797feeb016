import { ApiSliceIdentifier } from 'enums/api.enum';
import { LegalVisibility } from 'enums/legal-category.enum';
import { Category } from 'enums/terms.enum';
import { apiSlice } from 'redux/apiSlice';
import { LegalType } from 'types/terms';
export const privacyApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getPrivacyAndPolicy: builder.query<LegalType[], string>({
      query: (code) => ({
        url: '/legals',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              category: { inq: [Category.SELLER] },
              visibility: { inq: [LegalVisibility.SELLER, LegalVisibility.ALL] }
            }
          })
        },
        headers: {
          Authorization: `Bearer ${code}`
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE
      })
    })
  })
});

export const { useGetPrivacyAndPolicyQuery } = privacyApiSlice;
