import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { Count } from 'types/api';
import { Collection } from 'types/collection';

export const collectionApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCollections: builder.query<
      Collection[],
      {
        limit?: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, order, where, fields, include }) => ({
        url: '/collections',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            order,
            where: { status: 'active' },
            fields,
            include
          })
        }
      })
    }),
    createCollections: builder.mutation<void, Partial<Collection>>({
      query: (newCollection) => ({
        url: '/collections',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: newCollection
      })
    }),
    getCollectionCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/collections/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    })
  })
});

export const { useGetCollectionsQuery, useGetCollectionCountQuery, useLazyGetCollectionsQuery, useCreateCollectionsMutation } =
  collectionApiSlice;
