import StoreSetting from 'views/store/StoreSetting';

// ==============================|| PROFILE - ACCOUNT ||============================== //

type Props = {
  params: {
    tab: string;
  };
};

// Multiple versions of this page will be statically generated
// using the `params` returned by `generateStaticParams`
export default function Page({ params }: Props) {
  const { tab } = params;

  return <StoreSetting tab={tab} />;
}

// Return a list of `params` to populate the [slug] dynamic segment
export async function generateStaticParams() {
  const response = [
    'profile',
    'personal',
    'login-security',
    'store-setting',
    'firm-details',
    'social-media',
    'subscription',
    'sample-products',
    'bank-details',
    'shipping-settings'
  ];

  return response.map((tab: string) => ({
    tab: tab
  }));
}
