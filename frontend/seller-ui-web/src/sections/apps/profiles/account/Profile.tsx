// material-ui
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import List from '@mui/material/List';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import { CloseCircle } from 'iconsax-react';

// third-party

// project-imports
import MainCard from 'components/MainCard';
import Avatar from 'components/@extended/Avatar';

// assets
import { CallCalling, Gps, Sms } from 'iconsax-react';
import { useGetUserQuery } from '../../../../redux/auth/authApiSlice';
import { extractCountryCode } from 'utils/countryCodes';
import { MouseEvent, useCallback, useEffect, useState } from 'react';
import { Gender } from 'types/auth';
import { useGetSellerStoreBySellerIdQuery } from 'redux/auth/sellerApiSlice';
import { Button, IconButton, Tooltip } from '@mui/material';
import AlertDeactivateAccount from './AlertDeactivateAccount';
import { Card, CardContent, Snackbar } from '@mui/material';
import { Copy } from 'iconsax-react';
import { useCreateReferralCodeMutation } from 'redux/auth/authApiSlice';
import { MenuItem } from '@mui/material';
import { Menu } from '@mui/material';
import Image from 'next/image';

// ==============================|| ACCOUNT PROFILE - BASIC ||============================== //

export default function Profile() {
  const matchDownMD = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));
  const { data: user } = useGetUserQuery();
  const [referralCode, setReferralCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [isCodeGenerated, setIsCodeGenerated] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const oopen = Boolean(anchorEl);
  const [createReferralCode] = useCreateReferralCodeMutation();
  const [open, setOpen] = useState<boolean>(false);
  const sellerId = user?.profileId;
  const { data: sellerStore, refetch: refetchSellerStore } = useGetSellerStoreBySellerIdQuery(user?.profileId ?? '', {
    skip: !user?.profileId
  });
  const referralLink = `${process.env.NEXT_PUBLIC_CUSTOMER_UI_URL}/register?code=${referralCode}`;
  const message = `Hey!👋 ;I'm inviting you to try Ecomdukes – an awesome shopping app! 	🛍️
Use my referral code to sign up and get a discount: ${referralLink}`;

  const handleGenerateReferral = async () => {
    if (!sellerId) return;

    const result = await createReferralCode({ referrerId: sellerId }).unwrap();
    setReferralCode(result.referralCode);
    setIsCodeGenerated(true);
  };
  const handleClickMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleCopy = () => {
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
  };
  useEffect(() => {
    if (user?.profileId) {
      refetchSellerStore();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.profileId]);

  const { countryCode, phoneNumber } = extractCountryCode(user?.phone ?? '') ?? {};

  const genderLabels: Record<Gender, string> = {
    [Gender.Male]: 'Male',
    [Gender.Female]: 'Female',
    [Gender.Other]: 'Other'
  };

  const handleClose = useCallback(() => {
    setOpen((prev: boolean) => !prev);
  }, []);

  const handleWhatsappShare = () => {
    const encoded = encodeURIComponent(message);
    const url = `https://wa.me/?text=${encoded}`;
    window.open(url, '_blank');
  };
  const handleEmailShare = () => {
    const subject = encodeURIComponent('Join me on Ecomdukes & get a discount! 🛍️');
    const body = encodeURIComponent(
      `Hey! 👋 I'm inviting you to try Ecomdukes – an awesome shopping app! 🛍️\n` +
        `Use my referral code to sign up and get a discount: ${referralLink}`
    );
    const emailUrl = `mailto:?subject=${subject}&body=${body}`;
    window.open(emailUrl, '_blank');
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={5} md={4} xl={3}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MainCard>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Stack spacing={2.5} alignItems="center">
                    <Avatar alt="Avatar 1" size="xl" src={user?.photoUrl ?? ''} />
                    <Stack spacing={0.5} alignItems="center">
                      <Typography variant="h5">
                        {user?.firstName} {user?.lastName}
                      </Typography>
                      <Typography color="secondary">{user?.designation ?? '---'} </Typography>
                    </Stack>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Divider />
                </Grid>

                <Grid item xs={12}>
                  <List component="nav" aria-label="main mailbox folders" sx={{ py: 0, '& .MuiListItem-root': { p: 0, py: 1 } }}>
                    <ListItem>
                      <ListItemIcon>
                        <Sms size={18} />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right">{user?.email} </Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CallCalling size={18} />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right"> {`+${countryCode}${phoneNumber}`}</Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <Gps size={18} />
                      </ListItemIcon>
                      <ListItemSecondaryAction>
                        <Typography align="right"> {sellerStore?.country ?? '---'} </Typography>
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </MainCard>

            <MainCard sx={{ mt: 2 }}>
              <Card>
                <CardContent>
                  <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h5" gutterBottom>
                      Refer a Friend
                    </Typography>
                    {isCodeGenerated ? (
                      <IconButton onClick={handleClickMenu}>
                        <Image src="/assets/images/share.svg" alt="Share" width={24} height={24} />
                      </IconButton>
                    ) : (
                      <IconButton onClick={handleGenerateReferral}>
                        <Image src="/assets/images/share.svg" alt="Share" width={24} height={24} />
                      </IconButton>
                    )}
                  </Stack>

                  <Typography variant="body2" color="text.secondary" mb={3}>
                    Generate your unique referral code and share it with others.
                  </Typography>

                  {isCodeGenerated && (
                    <>
                      <Menu anchorEl={anchorEl} open={oopen} onClose={handleCloseMenu}>
                        <MenuItem onClick={handleEmailShare}>
                          <Sms size="20" style={{ marginRight: 8 }} /> Email
                        </MenuItem>
                        <MenuItem
                          onClick={() => {
                            handleWhatsappShare();
                          }}
                        >
                          <Image
                            src="/assets/images/footer/whatsapp.svg"
                            alt="Facebook"
                            style={{ marginRight: 8 }}
                            width={25}
                            height={25}
                          />{' '}
                          Whatsapp
                        </MenuItem>

                        <Divider />
                        <Tooltip title={referralLink} placement="left" arrow>
                          <MenuItem
                            onClick={() => {
                              handleCopy();
                              handleCloseMenu();
                            }}
                          >
                            <Copy size="20" style={{ marginRight: 8 }} /> Copy Link
                          </MenuItem>
                        </Tooltip>
                        <MenuItem onClick={handleCloseMenu} sx={{ justifyContent: 'center' }}>
                          <CloseCircle size="20" />
                        </MenuItem>
                      </Menu>
                    </>
                  )}
                </CardContent>
              </Card>

              <Snackbar open={copied} autoHideDuration={3000} onClose={() => setCopied(false)} message="Referral link copied!" />
            </MainCard>
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} sm={7} md={8} xl={9}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MainCard title="Personal Details">
              <List sx={{ py: 0 }}>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">First Name</Typography>
                        <Typography>{user?.firstName ?? '---'}</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Last Name</Typography>
                        <Typography>{user?.lastName ?? '---'}</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Phone</Typography>
                        <Typography>{`+${countryCode}${phoneNumber}`}</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Store Name</Typography>
                        <Typography>{sellerStore?.storeName ?? '---'} </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Designation</Typography>
                        <Typography>{user?.designation ?? '---'}</Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Gender</Typography>
                        <Typography>{user?.gender ? genderLabels[user.gender as Gender] : '---'}</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">DOB</Typography>
                        <Typography>{user?.dob?.toString() ?? '---'}</Typography>
                      </Stack>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Email</Typography>
                        <Typography>{user?.email ?? '---'}</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Address</Typography>
                        <Typography>
                          {sellerStore?.addressLine1} {sellerStore?.addressLine2}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">Country</Typography>
                        <Typography>{sellerStore?.country ?? '---'} </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem divider={!matchDownMD}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">State</Typography>
                        <Typography> {sellerStore?.state ?? '---'} </Typography>
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Stack spacing={0.5}>
                        <Typography color="secondary">City</Typography>
                        <Typography>{sellerStore?.city ?? '---'} </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </ListItem>
                <ListItem>
                  <Grid item xs={12} md={6}>
                    <Stack spacing={0.5}>
                      <Typography color="secondary">ZipCode</Typography>
                      <Typography>{sellerStore?.pincode ?? '---'}</Typography>
                    </Stack>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Stack spacing={0}>
                      <Button
                        variant="contained"
                        color="error"
                        size="small"
                        onClick={(e: MouseEvent<HTMLButtonElement>) => {
                          e.stopPropagation();
                          handleClose();
                        }}
                      >
                        Deactivate Account
                      </Button>
                    </Stack>
                  </Grid>
                </ListItem>
              </List>
            </MainCard>
          </Grid>
        </Grid>
      </Grid>
      <AlertDeactivateAccount id={user?.profileId ?? ''} open={open} handleClose={handleClose} />
    </Grid>
  );
}
