import { useState, useEffect } from 'react';
import { useFormik, FieldArray, FormikProvider } from 'formik';

// material-ui
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Tab,
  Tabs,
  TextField,
  Typography,
  IconButton,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project imports
import MainCard from 'components/MainCard';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { shippingProfileValidation } from 'validation/shipping-settings.validation';
import { ShippingProfile, ShippingCharge, WeightBasedRule, ShippingMethodType, ProductShippingCharge } from 'types/shipping';
import {
  useGetShippingProfilesQuery,
  useGetShippingMethodsQuery,
  useCreateShippingProfileMutation,
  useUpdateShippingProfileMutation,
  useDeleteShippingProfileMutation
} from 'redux/ecom/shippingApiSlice';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { Add, Edit2, Trash } from 'iconsax-react';
import { Country, State } from 'country-state-city';
import { currencyCodeSymbolMap } from 'constants/currency';
import { useGetproductsQuery } from 'redux/app/products/productApiSlice';
import { excludeMetaFields } from 'constants/filter';

// ==============================|| SHIPPING SETTINGS ||============================== //

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div role="tabpanel" hidden={value !== index} id={`shipping-tabpanel-${index}`} aria-labelledby={`shipping-tab-${index}`} {...other}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `shipping-tab-${index}`,
    'aria-controls': `shipping-tabpanel-${index}`
  };
}

const emptyShippingCharge: ShippingCharge = {
  countryCode: 'IN',
  stateCode: '',
  baseCharge: 0,
  estimatedDaysMin: 1,
  estimatedDaysMax: 3
};

const emptyWeightRule: WeightBasedRule = {
  minWeight: 0,
  maxWeight: 1,
  charge: 0
};

const emptyProductShippingCharge: ProductShippingCharge = {
  productId: '',
  baseCharge: 0
};

const ShippingSettings = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const { data: user } = useGetUserQuery();
  const { data: shippingMethods, isLoading: isMethodsLoading } = useGetShippingMethodsQuery();
  const {
    data: shippingProfiles,
    isLoading: isProfilesLoading,
    refetch: refetchProfiles
  } = useGetShippingProfilesQuery(
    { sellerId: user?.profileId ?? '', filter: { fields: excludeMetaFields } },
    {
      skip: !user?.profileId
    }
  );

  const [createShippingProfile, { isLoading: isCreating }] = useCreateShippingProfileMutation();
  const [updateShippingProfile, { isLoading: isUpdating }] = useUpdateShippingProfileMutation();
  const [deleteShippingProfile, { isLoading: isDeleting }] = useDeleteShippingProfileMutation();
  const { data: products, isLoading: isProductsLoading } = useGetproductsQuery({ fields: { id: true, name: true } });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const initialValues: ShippingProfile = {
    name: '',
    description: '',
    isDefault: false,
    isActive: true,
    sellerId: user?.profileId ?? '',
    shippingMethodId: '',
    shippingCharges: [],
    weightBasedRules: [],
    productShippingCharges: []
  };

  const handleSave = async (values: ShippingProfile) => {
    if (isEditing && selectedProfile) {
      delete values.shippingMethod;
      delete values.id;
      await updateShippingProfile({
        id: selectedProfile,
        body: values
      }).unwrap();
      openSnackbar({
        open: true,
        message: 'Shipping profile updated successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
    } else {
      await createShippingProfile(values).unwrap();
      openSnackbar({
        open: true,
        message: 'Shipping profile created successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
    }
    await refetchProfiles();
    resetForm();
  };

  const formik = useFormik<ShippingProfile>({
    initialValues,
    validationSchema: shippingProfileValidation,
    onSubmit: async (values) => {
      handleSave(values);
    }
  });

  const resetForm = () => {
    formik.resetForm();
    setIsEditing(false);
    setSelectedProfile(null);
    setTabValue(0);
  };

  const handleEditProfile = (profile: ShippingProfile) => {
    setIsEditing(true);
    setSelectedProfile(profile.id ?? null);
    formik.setValues({
      ...profile,
      shippingCharges: profile.shippingCharges?.length ? profile.shippingCharges : [{ ...emptyShippingCharge }],
      weightBasedRules: profile.weightBasedRules?.length ? profile.weightBasedRules : [{ ...emptyWeightRule }]
    });
    setTabValue(1);
  };

  const handleDeleteProfile = async (id: string) => {
    await deleteShippingProfile(id).unwrap();
    openSnackbar({
      open: true,
      message: 'Shipping profile deleted successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    await refetchProfiles();
    if (selectedProfile === id) {
      resetForm();
    }
  };

  const countries = Country.getAllCountries();

  useEffect(() => {
    if (!isMethodsLoading && shippingMethods?.length) {
      const selfShippingMethod = shippingMethods.find((method) => method.type === ShippingMethodType.SELF_SHIPPING);
      if (selfShippingMethod) {
        formik.setFieldValue('shippingMethodId', selfShippingMethod.id);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shippingMethods, isMethodsLoading]);

  function getChargeValue<T extends ShippingCharge | WeightBasedRule | ProductShippingCharge>(
    index: number,
    field: keyof T,
    charges?: T[]
  ): number | string {
    if (!charges || index < 0 || index >= charges.length) return '';
    return charges[index][field] as number | string;
  }

  const getChargeError = <T extends ShippingCharge | WeightBasedRule | ProductShippingCharge>(
    index: number,
    field: keyof T,
    errors?: Partial<Record<keyof T, string>>[] | undefined
  ): string => {
    if (!errors || index < 0 || index >= errors.length) return '';
    return errors[index]?.[field] ?? '';
  };

  useEffect(() => {
    if (tabValue === 0) {
      resetForm();
    }
  }, [selectedProfile]);

  console.log(formik.errors);
  console.log(formik.values);

  return (
    <MainCard title="Shipping Settings">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="shipping settings tabs">
            <Tab label="Shipping Profiles" {...a11yProps(0)} />
            <Tab label="Create/Edit Profile" {...a11yProps(1)} disabled />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              {isProfilesLoading ? (
                <Grid item xs={12}>
                  <Typography>Loading profiles...</Typography>
                </Grid>
              ) : shippingProfiles?.length ? (
                shippingProfiles.map((profile) => (
                  <Grid item xs={12} md={6} lg={4} key={profile.id}>
                    <Card>
                      <CardContent>
                        <Stack spacing={2}>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography variant="h5">{profile.name}</Typography>
                            <Stack direction="row" spacing={1}>
                              <IconButton color="primary" onClick={() => handleEditProfile(profile)}>
                                <Edit2 />
                              </IconButton>
                              <IconButton color="error" onClick={() => profile.id && handleDeleteProfile(profile.id)}>
                                {isDeleting ? <CircularProgress size={20} /> : <Trash />}
                              </IconButton>
                            </Stack>
                          </Stack>
                          <Typography variant="body2">{profile.description}</Typography>
                          <Divider />
                          <Stack direction="row" justifyContent="space-between">
                            <Typography variant="subtitle2">Method:</Typography>
                            <Typography>{profile.shippingMethod?.name}</Typography>
                          </Stack>
                          <Stack direction="row" justifyContent="space-between">
                            <Typography variant="subtitle2">Status:</Typography>
                            <Typography>{profile.isActive ? 'Active' : 'Inactive'}</Typography>
                          </Stack>
                          <Stack direction="row" justifyContent="space-between">
                            <Typography variant="subtitle2">Default:</Typography>
                            <Typography>{profile.isDefault ? 'Yes' : 'No'}</Typography>
                          </Stack>
                          <Stack direction="row" justifyContent="space-between">
                            <Typography variant="subtitle2">Shipping Charges:</Typography>
                            <Typography>{profile.shippingCharges?.length || 0}</Typography>
                          </Stack>
                          <Stack direction="row" justifyContent="space-between">
                            <Typography variant="subtitle2">Weight Rules:</Typography>
                            <Typography>{profile.weightBasedRules?.length || 0}</Typography>
                          </Stack>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Typography>No shipping profiles found. Create your first profile.</Typography>
                </Grid>
              )}
              {!shippingProfiles?.length && (
                <Grid item xs={12}>
                  <Button variant="contained" onClick={() => setTabValue(1)}>
                    {'Create First Profile'}
                  </Button>
                </Grid>
              )}
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <FormikProvider value={formik}>
              <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={3}>
                  {typeof formik.errors === 'string' && (
                    <Grid item xs={12}>
                      <Alert severity="error" sx={{ mb: 2 }}>
                        {formik.errors}
                      </Alert>
                    </Grid>
                  )}
                  <Grid item xs={12}>
                    <Typography variant="h5">Profile Information</Typography>
                    <Divider sx={{ my: 1.5 }} />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Stack spacing={1}>
                      <InputLabel htmlFor="name">Profile Name*</InputLabel>
                      <TextField
                        fullWidth
                        id="name"
                        name="name"
                        value={formik.values.name}
                        onChange={formik.handleChange}
                        error={formik.touched.name && Boolean(formik.errors.name)}
                        helperText={formik.touched.name && formik.errors.name}
                      />
                    </Stack>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Stack spacing={1}>
                      <InputLabel htmlFor="shippingMethodId">Shipping Method*</InputLabel>
                      <FormControl fullWidth error={formik.touched.shippingMethodId && Boolean(formik.errors.shippingMethodId)}>
                        <Select
                          id="shippingMethodId"
                          name="shippingMethodId"
                          value={formik.values.shippingMethodId}
                          onChange={formik.handleChange}
                          displayEmpty
                          readOnly
                        >
                          <MenuItem disabled value="">
                            <em>Select a shipping method</em>
                          </MenuItem>
                          {shippingMethods?.map((method) => (
                            <MenuItem key={method.id} value={method.id}>
                              {method.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {formik.touched.shippingMethodId && formik.errors.shippingMethodId && (
                          <FormHelperText>{formik.errors.shippingMethodId as string}</FormHelperText>
                        )}
                      </FormControl>
                    </Stack>
                  </Grid>

                  <Grid item xs={12}>
                    <Stack spacing={1}>
                      <InputLabel htmlFor="description">Description</InputLabel>
                      <TextField
                        fullWidth
                        id="description"
                        name="description"
                        multiline
                        rows={3}
                        value={formik.values.description}
                        onChange={formik.handleChange}
                        error={formik.touched.description && Boolean(formik.errors.description)}
                        helperText={formik.touched.description && formik.errors.description}
                      />
                    </Stack>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formik.values.isActive}
                          onChange={(e) => formik.setFieldValue('isActive', e.target.checked)}
                          name="isActive"
                        />
                      }
                      label="Active"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formik.values.isDefault}
                          onChange={(e) => formik.setFieldValue('isDefault', e.target.checked)}
                          name="isDefault"
                        />
                      }
                      label="Set as Default"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h5">Shipping Charges by Location</Typography>
                    <Typography variant="body2">Set shipping prices for different countries</Typography>
                    <Divider sx={{ my: 1.5 }} />
                  </Grid>

                  <Grid item xs={12}>
                    <FieldArray
                      name="shippingCharges"
                      render={(arrayHelpers) => (
                        <Stack spacing={2}>
                          {formik.values.shippingCharges?.map((charge, index) => (
                            <Card key={index} sx={{ p: 2 }}>
                              <Stack spacing={2}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                  <Typography variant="h6">Shipping Charge #{index + 1}</Typography>
                                  <IconButton color="error" onClick={() => arrayHelpers.remove(index)}>
                                    <Trash />
                                  </IconButton>
                                </Stack>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`shippingCharges.${index}.countryCode`}>Country*</InputLabel>
                                      <FormControl
                                        fullWidth
                                        error={Boolean(
                                          getChargeError<ShippingCharge>(
                                            index,
                                            'countryCode' as keyof ShippingCharge,
                                            Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                          )
                                        )}
                                      >
                                        <Select
                                          id={`shippingCharges.${index}.countryCode`}
                                          name={`shippingCharges.${index}.countryCode`}
                                          value={getChargeValue(index, 'countryCode', formik.values.shippingCharges) as string}
                                          onChange={formik.handleChange}
                                          displayEmpty
                                          disabled
                                        >
                                          <MenuItem disabled value="">
                                            <em>Select a country</em>
                                          </MenuItem>
                                          {countries.map((country) => (
                                            <MenuItem key={country.isoCode} value={country.isoCode}>
                                              {country.name}
                                            </MenuItem>
                                          ))}
                                        </Select>
                                        {getChargeError<ShippingCharge>(
                                          index,
                                          'countryCode' as keyof ShippingCharge,
                                          Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                        ) && (
                                          <FormHelperText>
                                            {getChargeError<ShippingCharge>(
                                              index,
                                              'countryCode' as keyof ShippingCharge,
                                              Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                            )}
                                          </FormHelperText>
                                        )}
                                      </FormControl>
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={6}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`shippingCharges.${index}.stateCode`}>State/Province (Optional)</InputLabel>
                                      <FormControl
                                        fullWidth
                                        error={Boolean(
                                          getChargeError<ShippingCharge>(
                                            index,
                                            'stateCode' as keyof ShippingCharge,
                                            Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                          )
                                        )}
                                      >
                                        <Select
                                          id={`shippingCharges.${index}.stateCode`}
                                          name={`shippingCharges.${index}.stateCode`}
                                          value={getChargeValue<ShippingCharge>(index, 'stateCode', formik.values.shippingCharges) || ''}
                                          onChange={formik.handleChange}
                                          displayEmpty
                                          disabled={!getChargeValue<ShippingCharge>(index, 'countryCode', formik.values.shippingCharges)}
                                        >
                                          <MenuItem value="">
                                            <em>All states/provinces</em>
                                          </MenuItem>
                                          {getChargeValue<ShippingCharge>(index, 'countryCode', formik.values.shippingCharges) &&
                                            State.getStatesOfCountry(
                                              getChargeValue<ShippingCharge>(index, 'countryCode', formik.values.shippingCharges) as string
                                            ).map((state) => (
                                              <MenuItem key={state.isoCode} value={state.isoCode}>
                                                {state.name}
                                              </MenuItem>
                                            ))}
                                        </Select>
                                        {getChargeError<ShippingCharge>(
                                          index,
                                          'stateCode' as keyof ShippingCharge,
                                          Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                        ) && (
                                          <FormHelperText>
                                            {getChargeError<ShippingCharge>(
                                              index,
                                              'stateCode' as keyof ShippingCharge,
                                              Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                            )}
                                          </FormHelperText>
                                        )}
                                      </FormControl>
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`shippingCharges.${index}.baseCharge`}>Shipping Price*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`shippingCharges.${index}.baseCharge`}
                                        name={`shippingCharges.${index}.baseCharge`}
                                        type="number"
                                        value={getChargeValue<ShippingCharge>(index, 'baseCharge', formik.values.shippingCharges) as number}
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<ShippingCharge>(
                                            index,
                                            'baseCharge' as keyof ShippingCharge,
                                            Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                          )
                                        )}
                                        helperText={getChargeError<ShippingCharge>(
                                          index,
                                          'baseCharge' as keyof ShippingCharge,
                                          Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                        )}
                                        InputProps={{
                                          startAdornment: <Typography sx={{ mr: 1 }}>{currencyCodeSymbolMap.get('INR')}</Typography>
                                        }}
                                      />
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`shippingCharges.${index}.estimatedDaysMin`}>Min Delivery Days*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`shippingCharges.${index}.estimatedDaysMin`}
                                        name={`shippingCharges.${index}.estimatedDaysMin`}
                                        type="number"
                                        value={
                                          getChargeValue<ShippingCharge>(index, 'estimatedDaysMin', formik.values.shippingCharges) as number
                                        }
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<ShippingCharge>(
                                            index,
                                            'estimatedDaysMin' as keyof ShippingCharge,
                                            Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                          )
                                        )}
                                        helperText={getChargeError<ShippingCharge>(
                                          index,
                                          'estimatedDaysMin' as keyof ShippingCharge,
                                          Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                        )}
                                      />
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`shippingCharges.${index}.estimatedDaysMax`}>Max Delivery Days*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`shippingCharges.${index}.estimatedDaysMax`}
                                        name={`shippingCharges.${index}.estimatedDaysMax`}
                                        type="number"
                                        value={
                                          getChargeValue<ShippingCharge>(index, 'estimatedDaysMax', formik.values.shippingCharges) as number
                                        }
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<ShippingCharge>(
                                            index,
                                            'estimatedDaysMax' as keyof ShippingCharge,
                                            Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                          )
                                        )}
                                        helperText={getChargeError<ShippingCharge>(
                                          index,
                                          'estimatedDaysMax' as keyof ShippingCharge,
                                          Array.isArray(formik.errors.shippingCharges) ? formik.errors.shippingCharges : undefined
                                        )}
                                      />
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Stack>
                            </Card>
                          ))}
                          <Button variant="outlined" startIcon={<Add />} onClick={() => arrayHelpers.push({ ...emptyShippingCharge })}>
                            {formik?.values?.shippingCharges?.length ? 'Add Another Shipping Charge' : 'Add Shipping Charge'}
                          </Button>
                        </Stack>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h5">Weight-Based Shipping Rules</Typography>
                    <Typography variant="body2">Set shipping prices based on product weight</Typography>
                    <Divider sx={{ my: 1.5 }} />
                  </Grid>

                  <Grid item xs={12}>
                    <FieldArray
                      name="weightBasedRules"
                      render={(arrayHelpers) => (
                        <Stack spacing={2}>
                          {formik.values.weightBasedRules?.map((rule, index) => (
                            <Card key={index} sx={{ p: 2 }}>
                              <Stack spacing={2}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                  <Typography variant="h6">Weight Rule #{index + 1}</Typography>
                                  {formik.values.weightBasedRules!.length > 1 && (
                                    <IconButton color="error" onClick={() => arrayHelpers.remove(index)}>
                                      <Trash />
                                    </IconButton>
                                  )}
                                </Stack>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`weightBasedRules.${index}.minWeight`}>Min Weight (kg)*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`weightBasedRules.${index}.minWeight`}
                                        name={`weightBasedRules.${index}.minWeight`}
                                        type="number"
                                        value={
                                          getChargeValue<WeightBasedRule>(index, 'minWeight', formik.values.weightBasedRules) as number
                                        }
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<WeightBasedRule>(
                                            index,
                                            'minWeight' as keyof WeightBasedRule,
                                            Array.isArray(formik.errors.weightBasedRules) ? formik.errors.weightBasedRules : undefined
                                          )
                                        )}
                                        helperText={getChargeError<WeightBasedRule>(
                                          index,
                                          'minWeight' as keyof WeightBasedRule,
                                          Array.isArray(formik.errors.weightBasedRules) ? formik.errors.weightBasedRules : undefined
                                        )}
                                      />
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`weightBasedRules.${index}.maxWeight`}>Max Weight (kg)*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`weightBasedRules.${index}.maxWeight`}
                                        name={`weightBasedRules.${index}.maxWeight`}
                                        type="number"
                                        value={
                                          getChargeValue<WeightBasedRule>(index, 'maxWeight', formik.values.weightBasedRules) as number
                                        }
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<WeightBasedRule>(
                                            index,
                                            'maxWeight' as keyof WeightBasedRule,
                                            Array.isArray(formik.errors.weightBasedRules) ? formik.errors.weightBasedRules : undefined
                                          )
                                        )}
                                        helperText={getChargeError<WeightBasedRule>(
                                          index,
                                          'maxWeight' as keyof WeightBasedRule,
                                          Array.isArray(formik.errors.weightBasedRules) ? formik.errors.weightBasedRules : undefined
                                        )}
                                      />
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`weightBasedRules.${index}.charge`}>Shipping Price*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`weightBasedRules.${index}.charge`}
                                        name={`weightBasedRules.${index}.charge`}
                                        type="number"
                                        value={getChargeValue<WeightBasedRule>(index, 'charge', formik.values.weightBasedRules) as number}
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<WeightBasedRule>(
                                            index,
                                            'charge' as keyof WeightBasedRule,
                                            Array.isArray(formik.errors.weightBasedRules) ? formik.errors.weightBasedRules : undefined
                                          )
                                        )}
                                        helperText={getChargeError<WeightBasedRule>(
                                          index,
                                          'charge' as keyof WeightBasedRule,
                                          Array.isArray(formik.errors.weightBasedRules) ? formik.errors.weightBasedRules : undefined
                                        )}
                                        InputProps={{
                                          startAdornment: <Typography sx={{ mr: 1 }}>{currencyCodeSymbolMap.get('INR')}</Typography>
                                        }}
                                      />
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Stack>
                            </Card>
                          ))}
                          <Button variant="outlined" startIcon={<Add />} onClick={() => arrayHelpers.push({ ...emptyWeightRule })}>
                            {formik.values.weightBasedRules?.length ? 'Add Another Weight Rule' : 'Add Weight Rule'}
                          </Button>
                        </Stack>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h5">Product-Specific Shipping Charges</Typography>
                    <Typography variant="body2">Set custom shipping prices for specific products</Typography>
                    <Divider sx={{ my: 1.5 }} />
                  </Grid>

                  <Grid item xs={12}>
                    <FieldArray
                      name="productShippingCharges"
                      render={(arrayHelpers) => (
                        <Stack spacing={2}>
                          {formik.values.productShippingCharges?.map((charge, index) => (
                            <Card key={index} sx={{ p: 2 }}>
                              <Stack spacing={2}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                  <Typography variant="h6">Product Charge #{index + 1}</Typography>
                                  {formik.values.productShippingCharges!.length > 1 && (
                                    <IconButton color="error" onClick={() => arrayHelpers.remove(index)}>
                                      <Trash />
                                    </IconButton>
                                  )}
                                </Stack>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={8}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`productShippingCharges.${index}.productId`}>Product*</InputLabel>
                                      <FormControl
                                        fullWidth
                                        error={Boolean(
                                          getChargeError<ProductShippingCharge>(
                                            index,
                                            'productId' as keyof ProductShippingCharge,
                                            Array.isArray(formik.errors.productShippingCharges)
                                              ? formik.errors.productShippingCharges
                                              : undefined
                                          )
                                        )}
                                      >
                                        <Select
                                          id={`productShippingCharges.${index}.productId`}
                                          name={`productShippingCharges.${index}.productId`}
                                          value={
                                            getChargeValue<ProductShippingCharge>(
                                              index,
                                              'productId',
                                              formik.values.productShippingCharges
                                            ) as string
                                          }
                                          onChange={formik.handleChange}
                                          displayEmpty
                                        >
                                          <MenuItem disabled value="">
                                            <em>Select a product</em>
                                          </MenuItem>
                                          {!isProductsLoading &&
                                            products?.map((product) => {
                                              const isAlreadySelected = formik.values.productShippingCharges
                                                ?.filter((_, i) => i !== index) // allow current field to be reselected
                                                .some((charge) => charge.productId === product.id);

                                              return (
                                                <MenuItem key={product.id} value={product.id} disabled={isAlreadySelected}>
                                                  {product.name}
                                                </MenuItem>
                                              );
                                            })}
                                        </Select>
                                        {getChargeError<ProductShippingCharge>(
                                          index,
                                          'productId' as keyof ProductShippingCharge,
                                          Array.isArray(formik.errors.productShippingCharges)
                                            ? formik.errors.productShippingCharges
                                            : undefined
                                        ) && (
                                          <FormHelperText>
                                            {getChargeError<ProductShippingCharge>(
                                              index,
                                              'productId' as keyof ProductShippingCharge,
                                              Array.isArray(formik.errors.productShippingCharges)
                                                ? formik.errors.productShippingCharges
                                                : undefined
                                            )}
                                          </FormHelperText>
                                        )}
                                      </FormControl>
                                    </Stack>
                                  </Grid>

                                  <Grid item xs={12} md={4}>
                                    <Stack spacing={1}>
                                      <InputLabel htmlFor={`productShippingCharges.${index}.baseCharge`}>Shipping Price*</InputLabel>
                                      <TextField
                                        fullWidth
                                        id={`productShippingCharges.${index}.baseCharge`}
                                        name={`productShippingCharges.${index}.baseCharge`}
                                        type="number"
                                        value={
                                          getChargeValue<ProductShippingCharge>(
                                            index,
                                            'baseCharge',
                                            formik.values.productShippingCharges
                                          ) as number
                                        }
                                        onChange={formik.handleChange}
                                        error={Boolean(
                                          getChargeError<ProductShippingCharge>(
                                            index,
                                            'baseCharge' as keyof ProductShippingCharge,
                                            Array.isArray(formik.errors.productShippingCharges)
                                              ? formik.errors.productShippingCharges
                                              : undefined
                                          )
                                        )}
                                        helperText={getChargeError<ProductShippingCharge>(
                                          index,
                                          'baseCharge' as keyof ProductShippingCharge,
                                          Array.isArray(formik.errors.productShippingCharges)
                                            ? formik.errors.productShippingCharges
                                            : undefined
                                        )}
                                        InputProps={{
                                          startAdornment: <Typography sx={{ mr: 1 }}>{currencyCodeSymbolMap.get('INR')}</Typography>
                                        }}
                                      />
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Stack>
                            </Card>
                          ))}
                          <Button
                            variant="outlined"
                            startIcon={<Add />}
                            onClick={() => arrayHelpers.push({ ...emptyProductShippingCharge })}
                          >
                            {formik.values.productShippingCharges?.length ? 'Add Another Product Charge' : 'Add Product Charge'}
                          </Button>
                        </Stack>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    {formik.submitCount > 0 && !formik.isValid && (
                      <Grid item xs={12}>
                        <Alert severity="error" sx={{ mb: 2 }}>
                          {typeof formik.errors === 'string' ? formik.errors : 'Please fix the errors above before submitting.'}
                        </Alert>
                      </Grid>
                    )}
                    <Stack direction="row" spacing={2} justifyContent="flex-end">
                      <Button variant="outlined" color="secondary" onClick={resetForm}>
                        Cancel
                      </Button>
                      <LoadingButton loading={isCreating || isUpdating} variant="contained" type="submit" disabled={!formik.dirty}>
                        {isEditing ? 'Update Profile' : 'Create Profile'}
                      </LoadingButton>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </FormikProvider>
          </TabPanel>
        </Grid>
      </Grid>
    </MainCard>
  );
};

export default ShippingSettings;
