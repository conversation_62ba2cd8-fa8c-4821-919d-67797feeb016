import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { openSnackbar } from 'api/snackbar';
import { ApiError } from 'types/api';
import { SnackbarProps } from 'types/snackbar';

export const useApiErrorHandler = () => {
  return (error: unknown) => {
    const errorMessage = getErrorMessage(error);

    openSnackbar({
      message: errorMessage,
      open: true,
      variant: 'alert',
      alert: { color: 'error' }
    } as SnackbarProps);
  };
};

export const getErrorMessage = (error: unknown): string => {
  let errorMessage = 'An unexpected error occurred';

  if (error && typeof error === 'object') {
    if ('status' in error) {
      // Handle RTK Query baseQuery errors
      const baseError = error as FetchBaseQueryError;

      if (baseError?.data && typeof baseError.data === 'object') {
        const apiError = baseError.data as ApiError;
        errorMessage = String(
          typeof apiError.error.message === 'object' ? (apiError.error?.message as { message: string })?.message : apiError.error?.message
        );
      } else {
        errorMessage = `Request failed with status: ${baseError.status}`;
      }
    } else if ('message' in error) {
      // Handle other errors
      errorMessage = String((error as Error).message);
    }
  }
  return errorMessage;
};
