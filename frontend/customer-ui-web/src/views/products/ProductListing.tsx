'use client';
import React from 'react';
import {CircularProgress, Grid} from '@mui/material';
import {ProductVariant} from 'types/product';
import {NoProduct} from './NoProduct';
import ProductCard from './ProductCard';
import {Box} from '@mui/material';

interface ProductListProps {
  products?: ProductVariant[];
  isLoading: boolean;
  isFetching: boolean;
}
const ProductList: React.FC<ProductListProps> = ({
  products = [],
  isFetching,
  isLoading,
}) => {
  if (isLoading && isFetching) {
    return (
      <Box
        sx={{
          height: '50vh',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (products.length === 0) return <NoProduct />;

  return (
    <>
      <Grid
        container
        spacing={1}
        justifyContent="flex-start"
        sx={{paddingX: {xs: 1, sm: 2, md: 3}, paddingY: 2}}
      >
        {products.map(product => (
          <Grid item xs={12} sm={6} md={4} key={product.id}>
            <ProductCard productVariant={product} />
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default ProductList;
