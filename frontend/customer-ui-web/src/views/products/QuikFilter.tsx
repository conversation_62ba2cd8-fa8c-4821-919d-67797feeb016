'use client';

import React, {FC} from 'react';
import {
  Box,
  Typography,
  Chip,
  Select,
  MenuItem,
  FormControl,
  Grid,
  SelectChangeEvent,
} from '@mui/material';
import {FilterValue} from 'types/filter';
interface Props {
  facets: FilterValue[];
  sortOrder: 'featured' | 'lowToHigh' | 'highToLow';
  onSortChange: (value: 'featured' | 'lowToHigh' | 'highToLow') => void;
}

const FilterSortProducts: FC<Props> = ({facets, sortOrder, onSortChange}) => {
  const handleSortChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value as 'featured' | 'lowToHigh' | 'highToLow';
    onSortChange(value);
  };

  return (
    <Grid container spacing={3} sx={{p: 2}}>
      <Grid
        item
        xs={12}
        sm={6}
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <Typography variant="h6" fontWeight="bold" noWrap>
          Laptops{' '}
          <Typography variant="body2" color="text.secondary" component="span">
            (2800 Products)
          </Typography>
        </Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <Box display="flex" justifyContent={{xs: 'flex-start', sm: 'flex-end'}}>
          <FormControl sx={{minWidth: 160}}>
            <Select
              id="sort-by"
              value={sortOrder}
              onChange={handleSortChange}
              sx={{
                borderRadius: '50px',
                px: 1.5,
                height: '36px',
                fontSize: '0.8rem',
                '&.MuiOutlinedInput-root': {
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'inherit',
                  },
                },
                '& .MuiSelect-select': {
                  py: 0,
                  height: '36px',
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              <MenuItem value="featured">
                <strong style={{fontSize: '0.8rem'}}>Relevance</strong>
              </MenuItem>
              <MenuItem value="lowToHigh">
                <strong style={{fontSize: '0.8rem'}}>Price low to high</strong>
              </MenuItem>
              <MenuItem value="highToLow">
                <strong style={{fontSize: '0.8rem'}}>Price high to low</strong>
              </MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Grid>

      <Grid item xs={12}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 1,
          }}
        >
          <Typography variant="body2" sx={{flexShrink: 0}}>
            Quick Filters:
          </Typography>

          {facets?.map(facet => (
            <Chip
              key={facet.value}
              label={facet.label}
              onClick={() => {}}
              clickable
              sx={{
                borderRadius: '15px',
                fontSize: '0.8rem',
                padding: '0.3rem 0.6rem',
                flexShrink: 0,
              }}
            />
          ))}
        </Box>
      </Grid>
    </Grid>
  );
};

export default FilterSortProducts;
