import {useState, SyntheticEvent, useEffect} from 'react';

// material-ui
import List from '@mui/material/List';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import ListItem from '@mui/material/ListItem';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import OutlinedInput from '@mui/material/OutlinedInput';
import FormHelperText from '@mui/material/FormHelperText';
import InputAdornment from '@mui/material/InputAdornment';
import Box from '@mui/material/Box';

// project-imports
import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';
import {openSnackbar} from 'api/snackbar';
import {
  isNumber,
  isLowercase<PERSON>har,
  isUppercase<PERSON>har,
  isSpecial<PERSON>har,
  minLength,
} from 'utils/password-validation';

// third-party
import * as Yup from 'yup';
import {useFormik} from 'formik';

// assets
import {Eye, EyeSlash, Minus, TickCircle} from 'iconsax-react';
import {
  useGetUserQuery,
  useUpdatePasswordMutation,
} from 'redux/auth/authApiSlice';

// types
import {SnackbarProps} from 'types/snackbar';
import {useApiErrorHandler} from 'hooks/useApiErrorHandler';
import {Backdrop, CircularProgress} from '@mui/material';
import {useRouter} from 'next/navigation';
import {unsetCredentials} from 'redux/auth/authSlice';
import {unsetMonitor} from 'redux/apimonitor/apiMonitorSlice';
import {useAppDispatch} from 'redux/hooks';
import {apiSlice} from 'redux/apiSlice';
import {ApiTagTypes} from 'redux/types';

// ==============================|| ACCOUNT PROFILE - PASSWORD CHANGE ||============================== //

export default function ChangePassword() {
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [updatePassword, {isLoading, error: updateError, reset: updateReset}] =
    useUpdatePasswordMutation();
  const router = useRouter();
  const {data: user} = useGetUserQuery();
  const handleError = useApiErrorHandler();
  const dispatch = useAppDispatch();

  const handleLogout = async () => {
    dispatch(unsetCredentials());
    dispatch(unsetMonitor());
    dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
    router.push('/login');
  };
  const handleMouseDownPassword = (event: SyntheticEvent) => {
    event.preventDefault();
  };

  const formik = useFormik({
    initialValues: {
      old: '',
      password: '',
      confirm: '',
    },
    validationSchema: Yup.object()
      .shape({
        old: Yup.string().required('Current Password is required'),
        password: Yup.string()
          .required('New Password is required')
          .matches(
            /^.*(?=.{8,})((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})(?=.*\d)((?=.*[a-z]){1})((?=.*[A-Z]){1}).*$/,
            'Password must contain at least 8 characters, one uppercase, one number and one special case character',
          )
          .test(
            'not-same-as-old',
            'New Password must be different from Current Password',
            function (value) {
              const {old} = this.parent;
              return value !== old;
            },
          ),
        confirm: Yup.string()
          .required('Confirm Password is required')
          .oneOf([Yup.ref('password')], "Passwords don't match."),
      })
      .test(
        'all-passwords-not-same',
        'Old, New, and Confirm Password must not be the same',
        function (values) {
          if (!values) return true;
          const {old, password, confirm} = values;
          return !(
            old &&
            password &&
            confirm &&
            old === password &&
            password === confirm
          );
        },
      ),

    onSubmit: async () => {
      await updatePassword({
        username: user?.username ?? '',
        oldPassword: formik.values.old,
        password: formik.values.password,
      }).unwrap();
      openSnackbar({
        open: true,
        message: `Your password has been successfully updated.`,
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
      handleLogout();
    },
  });
  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);
  return (
    <MainCard title="Change Password">
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          <Grid item container spacing={3} xs={12} sm={6}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="password-old">Current Password</InputLabel>
                <OutlinedInput
                  id="password-old"
                  placeholder="Enter Old Password"
                  type={showOldPassword ? 'text' : 'password'}
                  value={formik.values.old}
                  name="old"
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowOldPassword(!showOldPassword)}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                        size="large"
                        color="secondary"
                      >
                        {showOldPassword ? <Eye /> : <EyeSlash />}
                      </IconButton>
                    </InputAdornment>
                  }
                  autoComplete="password-old"
                />
              </Stack>
              {formik.touched.old && formik.errors.old && (
                <FormHelperText error id="password-old-helper">
                  {formik.errors.old}
                </FormHelperText>
              )}
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="password-password">
                  New Password
                </InputLabel>
                <OutlinedInput
                  id="password-password"
                  placeholder="Enter New Password"
                  type={showNewPassword ? 'text' : 'password'}
                  value={formik.values.password}
                  name="password"
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                        size="large"
                        color="secondary"
                      >
                        {showNewPassword ? <Eye /> : <EyeSlash />}
                      </IconButton>
                    </InputAdornment>
                  }
                  autoComplete="password-password"
                />
              </Stack>
              {formik.touched.password && formik.errors.password && (
                <FormHelperText error id="password-password-helper">
                  {formik.errors.password}
                </FormHelperText>
              )}
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="password-confirm">
                  Confirm Password
                </InputLabel>
                <OutlinedInput
                  id="password-confirm"
                  placeholder="Enter Confirm Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formik.values.confirm}
                  name="confirm"
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                        size="large"
                        color="secondary"
                      >
                        {showConfirmPassword ? <Eye /> : <EyeSlash />}
                      </IconButton>
                    </InputAdornment>
                  }
                  autoComplete="password-confirm"
                />
              </Stack>
              {formik.touched.confirm && formik.errors.confirm && (
                <FormHelperText error id="password-confirm-helper">
                  {formik.errors.confirm}
                </FormHelperText>
              )}
            </Grid>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Box sx={{p: {xs: 0, sm: 2, md: 4, lg: 5}}}>
              <Typography variant="h5">New Password must contain:</Typography>
              <List sx={{p: 0, mt: 1}}>
                <ListItem divider>
                  <ListItemIcon
                    sx={{
                      color: minLength(formik.values.password)
                        ? 'success.main'
                        : 'inherit',
                    }}
                  >
                    {minLength(formik.values.password) ? (
                      <TickCircle />
                    ) : (
                      <Minus />
                    )}
                  </ListItemIcon>
                  <ListItemText primary="At least 8 characters" />
                </ListItem>
                <ListItem divider>
                  <ListItemIcon
                    sx={{
                      color: isLowercaseChar(formik.values.password)
                        ? 'success.main'
                        : 'inherit',
                    }}
                  >
                    {isLowercaseChar(formik.values.password) ? (
                      <TickCircle />
                    ) : (
                      <Minus />
                    )}
                  </ListItemIcon>
                  <ListItemText primary="At least 1 lower letter (a-z)" />
                </ListItem>
                <ListItem divider>
                  <ListItemIcon
                    sx={{
                      color: isUppercaseChar(formik.values.password)
                        ? 'success.main'
                        : 'inherit',
                    }}
                  >
                    {isUppercaseChar(formik.values.password) ? (
                      <TickCircle />
                    ) : (
                      <Minus />
                    )}
                  </ListItemIcon>
                  <ListItemText primary="At least 1 uppercase letter (A-Z)" />
                </ListItem>
                <ListItem divider>
                  <ListItemIcon
                    sx={{
                      color: isNumber(formik.values.password)
                        ? 'success.main'
                        : 'inherit',
                    }}
                  >
                    {isNumber(formik.values.password) ? (
                      <TickCircle />
                    ) : (
                      <Minus />
                    )}
                  </ListItemIcon>
                  <ListItemText primary="At least 1 number (0-9)" />
                </ListItem>
                <ListItem>
                  <ListItemIcon
                    sx={{
                      color: isSpecialChar(formik.values.password)
                        ? 'success.main'
                        : 'inherit',
                    }}
                  >
                    {isSpecialChar(formik.values.password) ? (
                      <TickCircle />
                    ) : (
                      <Minus />
                    )}
                  </ListItemIcon>
                  <ListItemText primary="At least 1 special character" />
                </ListItem>
              </List>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Stack
              direction="row"
              justifyContent="flex-end"
              alignItems="center"
              spacing={2}
            >
              <Button type="submit" variant="contained" disabled={isLoading}>
                Update Profile
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </form>
      <Backdrop
        sx={{color: '#fff', zIndex: theme => theme.zIndex.drawer + 1}}
        open={isLoading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </MainCard>
  );
}
