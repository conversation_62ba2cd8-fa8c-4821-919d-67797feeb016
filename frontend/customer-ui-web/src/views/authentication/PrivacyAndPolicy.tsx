'use client';
import {Grid} from '@mui/material';
import {setGuestToken} from '../../redux/auth/authSlice';
import draftToHtml from 'draftjs-to-html';
import {useDispatch} from 'react-redux';
import {useEffect, useState} from 'react';
import {Typography} from '@mui/material';
import {CardContent} from '@mui/material';
import {Card} from '@mui/material';
import {useGetGuestTokenMutation} from 'redux/terms-and-privacy/termsApiSlice';
import {useGetPrivacyAndPolicyQuery} from 'redux/terms-and-privacy/privacyApiSlice';
import {Legals} from 'types/legal-category.enum';

export default function PrivacyAndPolicy() {
  const [guestCode, setGuestCode] = useState<string | null>(null);

  const [getToken] = useGetGuestTokenMutation();
  const dispatch = useDispatch();

  const accessToken =
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
  useEffect(() => {
    if (!accessToken) {
      const handleGuestLogin = async () => {
        const result = await getToken().unwrap();
        if (result?.accessToken) {
          dispatch(setGuestToken(result));
          setGuestCode(result.accessToken);
        }
      };

      handleGuestLogin();
    }
  }, [accessToken, getToken, dispatch]);
  const tokenToUse = accessToken || guestCode;

  const {data: privacyData, isLoading} = useGetPrivacyAndPolicyQuery(
    tokenToUse!,
    {
      skip: !tokenToUse,
    },
  );

  const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
  return (
    <Grid
      container
      sx={{
        pt: 0,
      }}
    >
      {' '}
      <Grid item xs={12}>
        <Card sx={{borderRadius: 3, p: 2}}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : privacyData?.length ? (
            privacyData
              .filter(term => term.type === Legals.PrivacyPolicy)
              .map((privacy, index) => {
                let htmlContent: string;
                try {
                  if (typeof privacy.data === 'string') {
                    htmlContent = isHtml(privacy.data)
                      ? privacy.data
                      : draftToHtml(JSON.parse(privacy.data));
                  } else {
                    htmlContent = draftToHtml(privacy.data);
                  }
                } catch (err) {
                  // eslint-disable-next-line no-console
                  console.error('Failed to convert term data:', err);
                  htmlContent = '<p>Error loading content.</p>';
                }

                return (
                  <CardContent key={index}>
                    <div dangerouslySetInnerHTML={{__html: htmlContent}} />
                  </CardContent>
                );
              })
          ) : (
            <Typography>No Privacy Policy available.</Typography>
          )}
        </Card>
      </Grid>
    </Grid>
  );
}
