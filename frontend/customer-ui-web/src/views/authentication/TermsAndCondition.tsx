'use client';
import {Card, CardContent, Grid, Typography} from '@mui/material';
import {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';

import {setGuestToken} from '../../redux/auth/authSlice';
import draftToHtml from 'draftjs-to-html';
import {
  useGetGuestTokenMutation,
  useGetTermsAndConditionQuery,
} from 'redux/terms-and-privacy/termsApiSlice';

export default function TermsCondition() {
  const [guestCode, setGuestCode] = useState<string | null>(null);
  const [getToken] = useGetGuestTokenMutation();
  const dispatch = useDispatch();

  const accessToken =
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;

  useEffect(() => {
    if (!accessToken) {
      const handleGuestLogin = async () => {
        const result = await getToken().unwrap();
        if (result?.accessToken) {
          dispatch(setGuestToken(result));
          setGuestCode(result.accessToken);
        }
      };

      handleGuestLogin();
    }
  }, [accessToken, getToken, dispatch]);

  const tokenToUse = accessToken || guestCode;

  const {data: termData, isLoading} = useGetTermsAndConditionQuery(
    tokenToUse!,
    {
      skip: !tokenToUse,
    },
  );

  const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);

  return (
    <Grid
      container
      sx={{
        pt: 0,
        px: {xs: 2, sm: 3},
        pb: {xs: 2, sm: 3},
      }}
    >
      {' '}
      <Grid item xs={12}>
        <Card sx={{borderRadius: 3, p: 2}}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : (
            termData?.map((term, index) => {
              let htmlContent: string;
              try {
                if (typeof term.data === 'string') {
                  htmlContent = isHtml(term.data)
                    ? term.data
                    : draftToHtml(JSON.parse(term.data));
                } else {
                  htmlContent = draftToHtml(term.data);
                }
              } catch (err) {
                // eslint-disable-next-line no-console
                console.error('Failed to convert term data:', err);
                htmlContent = '<p>Error loading content.</p>';
              }

              return (
                <CardContent key={index}>
                  <div dangerouslySetInnerHTML={{__html: htmlContent}} />
                </CardContent>
              );
            })
          )}
        </Card>
      </Grid>
    </Grid>
  );
}
