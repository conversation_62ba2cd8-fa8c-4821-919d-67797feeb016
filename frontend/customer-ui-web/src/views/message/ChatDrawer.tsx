'use client';

import {useState, ChangeEvent, MouseEvent, useMemo, useEffect} from 'react';
import {useTheme} from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import {
  Chip,
  Drawer,
  Grid,
  InputAdornment,
  Menu,
  MenuItem,
  OutlinedInput,
  Stack,
  Typography,
  Box,
} from '@mui/material';

import {
  ArrowRight2,
  Clock,
  MinusCirlce,
  SearchNormal1,
  TickCircle,
} from 'iconsax-react';

import Link from 'next/link';
import IconButton from 'components/@extended/IconButton';
import MainCard from 'components/MainCard';
import SimpleBar from 'components/third-party/SimpleBar';
import {ThemeMode} from 'config';
import {useGetUserChatsQuery} from 'redux/chat/chatApiSlice';
import {ChatDto} from 'types/chat';
import useUser from 'hooks/useUser';
import ChatItem from './ChatItem';

interface CustomerChatDrawerProps {
  openChatDrawer: boolean;
  handleDrawerOpen: () => void;
  selectedChatId: string | null;
  setSelectedChat: (chat: ChatDto) => void;
  chat: ChatDto | null;
}

export default function CustomerChatDrawer({
  openChatDrawer,
  handleDrawerOpen,
  selectedChatId,
  setSelectedChat,
  chat,
}: CustomerChatDrawerProps) {
  const theme = useTheme();
  const matchDownLG = useMediaQuery(theme.breakpoints.down('lg'));
  const drawerBG =
    theme.palette.mode === ThemeMode.DARK ? 'dark.main' : 'white';
  const user = useUser();
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const {data: userChats = [], refetch} = useGetUserChatsQuery({
    search: debouncedSearch,
  });

  const [anchorEl, setAnchorEl] = useState<Element | null>(null);
  const [, setStatus] = useState('available');
  const [search, setSearch] = useState('');

  const handleSearch = (event: ChangeEvent<HTMLInputElement>) =>
    setSearch(event.target.value);

  const handleClickRightMenu = (event: MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);

  const handleCloseRightMenu = () => setAnchorEl(null);

  const handleRightMenuItemClick = (userStatus: string) => () => {
    setStatus(userStatus);
    handleCloseRightMenu();
  };

  const isUserValid = user && typeof user !== 'boolean';
  const filteredChats = useMemo(() => {
    return userChats.filter(chat => {
      const sellerUser = chat.userData?.userTenant?.user;
      const firstNameMatch = sellerUser?.firstName
        ?.toLowerCase()
        .includes(search.toLowerCase());
      const lastNameMatch = sellerUser?.lastName
        ?.toLowerCase()
        .includes(search.toLowerCase());

      return firstNameMatch || lastNameMatch;
    });
  }, [search, userChats]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300);

    return () => clearTimeout(handler);
  }, [search]);

  return (
    <Drawer
      sx={{
        width: 320,
        flexShrink: 0,
        display: {xs: openChatDrawer ? 'block' : 'none', lg: 'block'},
        zIndex: {xs: openChatDrawer ? 1300 : -1, lg: 0},
        '& .MuiDrawer-paper': {
          height: '100%',
          width: 320,
          boxSizing: 'border-box',
          position: matchDownLG ? 'fixed' : 'relative',
          border: 'none',
          ...(!matchDownLG && {borderRadius: '12px 0 0 12px'}),
        },
      }}
      variant={matchDownLG ? 'temporary' : 'persistent'}
      anchor="left"
      open={openChatDrawer}
      ModalProps={{keepMounted: true}}
      onClose={handleDrawerOpen}
    >
      <MainCard
        sx={{
          bgcolor: matchDownLG ? 'transparent' : drawerBG,
          borderRadius: '12px 0 0 12px',
          borderRight: 'none',
          height: '100%',
        }}
        border={!matchDownLG}
        content={false}
      >
        <Box sx={{p: 3, pb: 1}}>
          <Stack spacing={2}>
            <Stack direction="row" spacing={0.5} alignItems="center">
              <Typography variant="h5" color="inherit">
                Messages
              </Typography>
              <Chip
                label={userChats.length}
                color="secondary"
                sx={{width: 20, height: 20, borderRadius: '50%'}}
              />
              <IconButton onClick={refetch} size="small" color="primary">
                🔄
              </IconButton>
            </Stack>

            <OutlinedInput
              fullWidth
              placeholder="Search by seller "
              value={search}
              onChange={handleSearch}
              sx={{'& .MuiOutlinedInput-input': {p: '10.5px 0px 12px'}}}
              startAdornment={
                <InputAdornment position="start">
                  <SearchNormal1 style={{fontSize: 'small'}} />
                </InputAdornment>
              }
            />
          </Stack>
        </Box>

        <SimpleBar
          sx={{
            overflowX: 'hidden',
            height: matchDownLG ? 'calc(100vh - 300px)' : 'calc(100vh - 402px)',
            minHeight: matchDownLG ? 0 : 420,
          }}
        >
          <Box sx={{p: 3, pt: 0}}>
            {filteredChats.length > 0 ? (
              filteredChats.map(chat => (
                <ChatItem
                  key={chat.id}
                  chat={chat}
                  isSelected={chat.id === selectedChatId}
                  onClick={() => setSelectedChat(chat)}
                />
              ))
            ) : chat ? (
              <ChatItem
                key={chat.id}
                chat={chat}
                isSelected={chat.id === selectedChatId}
                onClick={() => setSelectedChat(chat)}
              />
            ) : (
              <Typography>No messages found</Typography>
            )}
          </Box>
        </SimpleBar>

        <Box sx={{p: 3, pt: 1, pl: 5}}>
          <Grid container>
            <Grid item xs={12}>
              <Grid
                container
                spacing={1}
                alignItems="center"
                sx={{flexWrap: 'nowrap'}}
              >
                <Grid item xs zeroMinWidth>
                  <Stack
                    component={Link}
                    href="/profile"
                    sx={{cursor: 'pointer', textDecoration: 'none'}}
                  >
                    <Typography variant="h5" color="text.primary">
                      {isUserValid ? user.name : 'Customer'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {isUserValid ? user.email : ''}
                    </Typography>
                  </Stack>
                </Grid>
                <Grid item>
                  <IconButton
                    onClick={handleClickRightMenu}
                    size="small"
                    color="secondary"
                  >
                    <ArrowRight2 />
                  </IconButton>
                  <Menu
                    anchorEl={anchorEl}
                    keepMounted
                    open={Boolean(anchorEl)}
                    onClose={handleCloseRightMenu}
                    anchorOrigin={{vertical: 'top', horizontal: 'right'}}
                    transformOrigin={{vertical: 'bottom', horizontal: 'right'}}
                    sx={{
                      '& .MuiMenu-list': {p: 0},
                      '& .MuiMenuItem-root': {pl: '6px', py: '3px'},
                    }}
                  >
                    <MenuItem onClick={handleRightMenuItemClick('available')}>
                      <TickCircle
                        variant="Bold"
                        color={theme.palette.success.main}
                      />
                      <Typography sx={{ml: 1}}>Active</Typography>
                    </MenuItem>
                    <MenuItem onClick={handleRightMenuItemClick('offline')}>
                      <Clock
                        variant="Bold"
                        color={theme.palette.warning.main}
                      />
                      <Typography sx={{ml: 1}}>Away</Typography>
                    </MenuItem>
                    <MenuItem
                      onClick={handleRightMenuItemClick('do_not_disturb')}
                    >
                      <MinusCirlce
                        variant="Bold"
                        color={theme.palette.secondary.main}
                      />
                      <Typography sx={{ml: 1}}>Do not disturb</Typography>
                    </MenuItem>
                  </Menu>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
      </MainCard>
    </Drawer>
  );
}
