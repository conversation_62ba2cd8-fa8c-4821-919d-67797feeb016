import {Avatar, Box, Stack, Typography} from '@mui/material';
import {useTheme} from '@mui/material/styles';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {ChatMessage} from 'types/chat';

dayjs.extend(relativeTime);

const ChatHistory: React.FC<{
  messages: ChatMessage[];
  currentUserId: string;
}> = ({messages, currentUserId}) => {
  const theme = useTheme();

  return (
    <Stack spacing={2}>
      {messages.map((msg, idx) => {
        const isSender = msg.senderId === currentUserId;
        const time = dayjs(msg.createdOn || msg.ModifiedOn).format('hh:mm A');

        const sender = msg.sender;
        const initials =
          `${sender?.firstName?.[0] ?? ''}${sender?.lastName?.[0] ?? ''}`.toUpperCase();

        const avatarUrl = sender?.presignedPhotoUrl || sender?.photoUrl || '';

        return (
          <Box
            key={msg.id || idx}
            display="flex"
            justifyContent={isSender ? 'flex-end' : 'flex-start'}
          >
            <Stack
              direction="row"
              spacing={1}
              alignItems="flex-end"
              justifyContent={isSender ? 'flex-end' : 'flex-start'}
              sx={{maxWidth: '75%'}}
            >
              {!isSender && (
                <Avatar
                  src={avatarUrl || undefined}
                  sx={{width: 32, height: 32, fontSize: '0.875rem'}}
                >
                  {!avatarUrl && initials}
                </Avatar>
              )}
              <Stack
                spacing={0.5}
                alignItems={isSender ? 'flex-end' : 'flex-start'}
              >
                <Box
                  sx={{
                    px: 2,
                    py: 1,
                    bgcolor: isSender ? theme.palette.primary.main : 'grey.100',
                    color: isSender ? '#fff' : theme.palette.text.primary,
                    borderRadius: 2,
                    maxWidth: '100%',
                  }}
                >
                  <Typography variant="body2" sx={{whiteSpace: 'pre-wrap'}}>
                    {msg.message}
                  </Typography>
                </Box>
                <Typography variant="caption" color="text.secondary">
                  {time}
                </Typography>
              </Stack>
              {isSender && (
                <Avatar
                  src={avatarUrl || undefined}
                  sx={{width: 32, height: 32, fontSize: '0.875rem'}}
                >
                  {!avatarUrl && initials}
                </Avatar>
              )}
            </Stack>
          </Box>
        );
      })}
    </Stack>
  );
};

export default ChatHistory;
