// material-ui
import Badge from '@mui/material/Badge';

// project-imports
import Avatar from 'components/@extended/Avatar';

// types
import {ChatDto} from 'types/chat';
import AvatarStatus from './AvatarStatus';

// ==============================|| CHAT - USER AVATAR WITH STATUS ICON ||============================== //

interface UserAvatarProps {
  user: ChatDto;
}

export default function UserAvatar({user}: UserAvatarProps) {
  const userInfo = user?.userData?.userTenant?.user;

  const fullName = userInfo
    ? `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim()
    : `User (${user.id})`;

  const avatarSrc = user.userData?.presignedPhotoUrl || undefined;

  return (
    <Badge
      overlap="circular"
      badgeContent={<AvatarStatus status={user.statusText || 'offline'} />}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      sx={{
        '& .MuiBox-root': {width: 6, height: 6},
        padding: 0,
        minWidth: 12,
        '& svg': {background: '#fff', borderRadius: '50%'},
      }}
    >
      <Avatar alt={fullName} src={avatarSrc} />
    </Badge>
  );
}
