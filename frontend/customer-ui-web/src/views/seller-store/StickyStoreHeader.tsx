'use client';

import React from 'react';
import {<PERSON>, Typography, Stack, Button, Avatar, useTheme} from '@mui/material';
import {ProductVariant} from 'types/product';

interface StickyStoreHeaderProps {
  storeData: {
    banner: string;
    logo?: string;
    dp?: string;
    storeName?: string;
    allowCategorisation?: boolean;
  };
  productVariants: ProductVariant[];
}

const StickyStoreHeader: React.FC<StickyStoreHeaderProps> = ({
  storeData,
  productVariants,
}) => {
  const theme = useTheme();

  const collectionMap = new Map<string, string>();
  productVariants.forEach(variant => {
    const collectionId = variant.product?.collectionId;
    const collectionName = variant.product?.collection?.name;
    if (collectionId && collectionName && !collectionMap.has(collectionId)) {
      collectionMap.set(collectionId, collectionName);
    }
  });

  const uniqueCollections = Array.from(collectionMap.entries()).map(
    ([id, name]) => ({id, name}),
  );

  const logoImageUrl =
    storeData?.logo ||
    'https://images.mamaearth.in/catalog/product/v/i/vit_c_glow_sunscreen1_white_bg_1.jpg?format=auto&width=300&height=300';

  return (
    <Box
      sx={{
        position: 'sticky',
        top: 120,
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        gap: 4,
        px: 5,
        py: 2,
        borderBottom: '1px solid #ddd',
        backgroundColor: theme.palette.secondary?.lighter ?? '#f5f5f5',
        justifyContent: 'space-between',
      }}
    >
      {/* Left: Logo + Store Name */}
      <Stack direction="row" spacing={2} alignItems="center">
        <Box
          component="img"
          src={logoImageUrl}
          alt={`${storeData?.storeName || 'Store'} Logo`}
          sx={{
            height: 50,
            width: 50,
            borderRadius: 1,
          }}
        />
        <Typography variant="h5" sx={{fontWeight: 'bold', color: '#0A0A5F'}}>
          {storeData?.storeName || 'Nature Republic'}
        </Typography>
      </Stack>

      {storeData.allowCategorisation && (
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            overflowX: 'auto',
            flex: 1,
            justifyContent: 'center',
            px: 2,
            '&::-webkit-scrollbar': {display: 'none'},
          }}
        >
          <Button
            sx={{
              color: '#444',
              textTransform: 'none',
              fontWeight: 500,
              whiteSpace: 'nowrap',
              '&:hover': {color: '#000845', backgroundColor: 'transparent'},
            }}
          >
            All Products
          </Button>
          {uniqueCollections.map(collection => (
            <Button
              key={collection.id}
              sx={{
                color: '#444',
                textTransform: 'none',
                fontWeight: 500,
                whiteSpace: 'nowrap',
                '&:hover': {color: '#000845', backgroundColor: 'transparent'},
              }}
            >
              {collection.name}
            </Button>
          ))}
        </Box>
      )}

      {/* Right: Avatar */}
      <Avatar
        alt="Store Avatar"
        src={storeData.dp || '/default-avatar.png'}
        sx={{width: 40, height: 40}}
      />
    </Box>
  );
};

export default StickyStoreHeader;
