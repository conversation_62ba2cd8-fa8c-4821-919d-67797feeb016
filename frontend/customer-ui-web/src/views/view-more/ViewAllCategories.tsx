'use client';

import {
  Box,
  Grid,
  Typography,
  Button,
  CircularProgress,
} from '@mui/material';
import {useEffect, useState} from 'react';
import {Collection, ProductVariant} from 'types/product';
import {IFilter} from 'types/api';
import {useGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';
import ProductCard from 'views/products/ProductCard';
import {useGetProductVariantsQuery} from 'redux/ecom/productVariantApiSlice';

export default function ViewAllCategories() {
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);

  const {data: collections, isLoading: isCollectionsLoading} =
    useGetCollectionsQuery({
      fields: {id: true, name: true},
    });

  useEffect(() => {
    if (collections && collections.length > 0 && !selectedCollectionId) {
      setSelectedCollectionId(collections[0].id);
    }
  }, [collections, selectedCollectionId]);

  const productVariantFilter: IFilter | undefined = selectedCollectionId
    ? {
        include: [
          {
            relation: 'product',
            scope: {
              where: {
                collectionId: selectedCollectionId,
              },
            },
            required: true,
          },
          {
            relation: 'productVariantPrice',
          },
          {
            relation: 'featuredAsset',
          },
        ],
        // Removed limit to fetch all
      }
    : undefined;

  const {data: productVariants, isLoading: isVariantsLoading} =
    useGetProductVariantsQuery(productVariantFilter);

  return (
    <Box p={3}>
      <Typography variant="h4" mb={3}>
        All Categories
      </Typography>

      {/* Category Chips */}
      {isCollectionsLoading ? (
        <CircularProgress size={24} />
      ) : (
        <Grid container spacing={2} mb={3}>
          {collections?.map((collection: Collection) => (
            <Grid item key={collection.id}>
              <Button
                variant={
                  selectedCollectionId === collection.id
                    ? 'contained'
                    : 'outlined'
                }
                onClick={() => setSelectedCollectionId(collection.id)}
                sx={{
                  borderRadius: 20,
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  minWidth: 120,
                  backgroundColor:
                    selectedCollectionId === collection.id
                      ? '#9A2D8E'
                      : 'white',
                  color:
                    selectedCollectionId === collection.id ? 'white' : 'black',
                }}
              >
                {collection.name}
              </Button>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Product Grid */}
      {isVariantsLoading ? (
        <CircularProgress />
      ) : (
        <Grid container spacing={3}>
          {productVariants?.map((product: ProductVariant) => (
            <Grid item xs={12} sm={6} md={3} key={product.id}>
              <ProductCard productVariant={product} />
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
}
