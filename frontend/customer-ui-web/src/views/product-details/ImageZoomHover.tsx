import React, {useRef, useState} from 'react';
import {Box} from '@mui/material';

interface ImageZoomProps {
  src: string;
  zoomScale?: number;
  disabled?: boolean;
}

export const ImageZoomHover = ({
  src,
  zoomScale = 2.5,
  disabled = false,
}: ImageZoomProps) => {
  const [hovered, setHovered] = useState(false);
  const [position, setPosition] = useState({x: 0, y: 0});
  const [zoomPosition, setZoomPosition] = useState({left: 0, top: 2});
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasMouseMoved, setHasMouseMoved] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    if (!hasMouseMoved) setHasMouseMoved(true);
    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setPosition({x, y});
    setZoomPosition({left: rect.right + 50, top: rect.top});
  };
  if (disabled) {
    return (
      <Box
        ref={containerRef}
        sx={{
          width: '100%',
          aspectRatio: '4 / 3',
          borderRadius: 4,
          overflow: 'hidden',
        }}
      >
        <img
          src={src}
          alt="Zoom"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            borderRadius: 'inherit',
          }}
        />
      </Box>
    );
  }
  return (
    <>
      <Box
        ref={containerRef}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        onMouseMove={handleMouseMove}
        sx={{
          width: '100%',
          aspectRatio: '4 / 3',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 4,
          cursor: 'zoom-in',
        }}
      >
        <img
          src={src}
          alt="Zoom"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
          }}
        />
      </Box>

      {hovered && hasMouseMoved && (
        <Box
          sx={{
            position: 'fixed',
            top: zoomPosition.top,
            left: zoomPosition.left,
            width: 620,
            height: 500,
            overflow: 'hidden',
            backgroundColor: '#fff',
            backgroundSize: `${zoomScale * 100}%`,
            backgroundPosition: position,
            zIndex: 1300,
          }}
        >
          <img
            src={src}
            alt="Zoomed"
            style={{
              width: `${zoomScale * 100}%`,
              height: `${zoomScale * 100}%`,
              objectFit: 'contain',
              transform: `translate(-${position.x}%, -${position.y}%)`,
              transition: 'transform 0.1s ease',
              pointerEvents: 'none',
            }}
          />
        </Box>
      )}
    </>
  );
};
