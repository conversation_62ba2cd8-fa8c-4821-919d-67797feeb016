'use client';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import MainCard from 'components/MainCard';
import {
  Chip,
  Divider,
  Rating,
  Box,
  Button,
  Tabs,
  Tab,
  Avatar,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
} from '@mui/material';
import {Menu, MenuItem, Tooltip} from '@mui/material';
import {SyntheticEvent, useState} from 'react';
import {
  AttachCircle,
  Crown,
  Gift,
  CloseCircle,
  Designtools,
} from 'iconsax-react';
import {ProductCustomField, ProductVariant} from 'types/product';
import {VariantSelector} from './VariantSelector';
import {Dialog, DialogTitle, DialogContent, IconButton} from '@mui/material';
import draftToHtml from 'draftjs-to-html';
import ProductCustomizationForm from './CustomizationForm';
import Link from 'next/link';
import Image from 'next/image';
import {Copy, Sms} from 'iconsax-react';

interface Props {
  productVariant: ProductVariant;
  otherVariants: ProductVariant[];
  onVariantSelect: (variantId: string) => void;
  productCustomizations?: ProductCustomField[];
}

const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);

// Safe HTML conversion
const getHtmlContent = (content: string | undefined) => {
  if (!content) return '<p>No content available.</p>';
  try {
    return isHtml(content) ? content : draftToHtml(JSON.parse(content));
  } catch (err) {
    console.error('Failed to parse content:', err);
    return '<p>Error rendering content.</p>';
  }
};

export default function ProductAddress({
  productVariant,
  otherVariants,
  onVariantSelect,
  productCustomizations,
}: Props) {
  const [tab, setTab] = useState(0);
  const [openPolicyModal, setOpenPolicyModal] = useState(false);
  const [openTermsModal, setOpenTermsModal] = useState(false);
  const [openDisclaimerModal, setOpenDisclaimerModal] = useState(false);
  const [expanded, setExpanded] = useState<string | false>('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);
  const productLink = `${process.env.NEXT_PUBLIC_CUSTOMER_UI_URL}/product-details/${productVariant.id}`;

  const [, setCopied] = useState(false);
  const handleChange =
    (panel: string) => (event: SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : false);
    };
  const handleClickMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleCopy = () => {
    setCopied(true);
    navigator.clipboard.writeText(productLink);
  };

  const handleWhatsappShare = () => {
    const message = `Hey!  Check out this amazing product: ${productVariant.name} ${productLink}`;
    const encoded = encodeURIComponent(message);
    const url = `https://wa.me/?text=${encoded}`;
    window.open(url, '_blank');
  };

  const handleEmailShare = () => {
    const subject = `Check out this amazing product: ${productVariant.name}`;
    const body = `Hey! Check out this amazing product on Ecomdukes: ${productLink}`;

    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoUrl, '_blank');
  };

  const handleFacebookShare = () => {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
      productLink,
    )}`;
    window.open(url, '_blank');
  };
  const handleTwitterShare = () => {
    const text = `Check out this product: ${productVariant.name}`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(productLink)}`;
    window.open(url, '_blank');
  };

  const handleLinkedlnShare = () => {
    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(productLink)}`;
    window.open(url, '_blank');
  };

  const price = productVariant.productVariantPrice?.price ?? 'N/A';
  const mrp = productVariant.productVariantPrice?.mrp ?? 'N/A';

  const returnPolicyContent =
    (productVariant.productReturnPolicy?.returnPolicy ?? '') ||
    (productVariant.product?.productReturnPolicy?.returnPolicy ?? '') ||
    '';

  const termsAndCondtionContent =
    (productVariant.productTermsAndCondition?.terms ?? '') ||
    (productVariant.product?.productTermsAndCondition?.terms ?? '') ||
    '';

  const productDetailContent =
    (productVariant.productDetail?.details ?? '') ||
    (productVariant.product?.productDetail?.details ?? '') ||
    '';

  const productMoreInfoContent =
    (productVariant.productMoreInfo?.info ?? '') ||
    (productVariant.product?.productMoreInfo?.info ?? '') ||
    '';

  const productDisclaimerContent =
    (productVariant.productDisclaimer?.disclaimer ?? '') ||
    (productVariant.product.productDisclaimer?.disclaimer ?? '') ||
    '';

  const productSuitabilityContent =
    (productVariant.productSuitability?.suitableFor ?? '') ||
    (productVariant.product.productSuitability?.suitableFor ?? '') ||
    '';

  const productUniquenessContent =
    (productVariant.productUniqueness?.uniqueness ?? '') ||
    (productVariant.product.productUniqueness?.uniqueness ?? '') ||
    '';

  const productPersonalWork =
    (productVariant.productPersonalWork?.workLevel ?? '') ||
    (productVariant.product.productPersonalWork?.workLevel ?? '') ||
    '';

  const productSpecificationsContent = productVariant.productSpecifications
    ?.length
    ? productVariant.productSpecifications
    : productVariant.product?.productSpecifications?.length
      ? productVariant.product?.productSpecifications
      : [];

  const productBoxContents = productVariant.productBoxContents?.length
    ? productVariant.productBoxContents
    : productVariant.product?.productBoxContents?.length
      ? productVariant.product?.productBoxContents
      : [];

  return (
    <MainCard
      sx={{
        position: 'relative',
        p: 3,
        maxWidth: 700,
        boxShadow: 3,
        borderRadius: 2,
      }}
    >
      <Stack spacing={2}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Chip
            label="20+ Left"
            size="small"
            sx={{
              backgroundColor: 'transparent',
              color: '#9E3393',
            }}
          />

          <Stack
            direction="row"
            spacing={1}
            alignItems="center"
            sx={{cursor: 'pointer'}}
            onClick={handleClickMenu}
          >
            <Typography variant="h6" color="black">
              Share
            </Typography>
            <Image
              src="/assets/images/share.svg"
              alt="Share"
              width={15}
              height={15}
            />
          </Stack>

          <Menu anchorEl={anchorEl} open={openMenu} onClose={handleCloseMenu}>
            <MenuItem
              onClick={() => {
                handleEmailShare();
                handleCloseMenu();
              }}
            >
              <Sms size="20" style={{marginRight: 8}} /> Email
            </MenuItem>

            <MenuItem
              onClick={() => {
                handleWhatsappShare();
                handleCloseMenu();
              }}
            >
              <Image
                src="/assets/images/footer/whatsapp.svg"
                alt="Whatsapp"
                style={{marginRight: 8}}
                width={25}
                height={25}
              />
              Whatsapp
            </MenuItem>

            <MenuItem
              onClick={() => {
                handleFacebookShare();
                handleCloseMenu();
              }}
            >
              <Image
                src="/assets/images/footer/facebook.svg"
                alt="Facebook"
                style={{marginRight: 8}}
                width={20}
                height={20}
              />
              Facebook
            </MenuItem>
            <MenuItem
              onClick={() => {
                handleTwitterShare();
                handleCloseMenu();
              }}
            >
              <Image
                src="/assets/images/twitter.svg"
                alt="Twitter"
                style={{marginRight: 8}}
                width={20}
                height={20}
              />
              Twitter
            </MenuItem>

            <MenuItem
              onClick={() => {
                handleLinkedlnShare();
                handleCloseMenu();
              }}
            >
              <Image
                src="/assets/images/linkedin.svg"
                alt="LinkedIn"
                style={{marginRight: 8}}
                width={20}
                height={20}
              />
              LinkedIn
            </MenuItem>

            <Divider />
            <Tooltip title={productLink} placement="left" arrow>
              <MenuItem
                onClick={() => {
                  handleCopy();
                  handleCloseMenu();
                }}
              >
                <Copy size="20" style={{marginRight: 8}} /> Copy Link
              </MenuItem>
            </Tooltip>
            <MenuItem onClick={handleCloseMenu} sx={{justifyContent: 'center'}}>
              <CloseCircle size="20" />
            </MenuItem>
          </Menu>
        </Stack>
        <Typography variant="h5" fontWeight="bold">
          {productVariant.name}
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            ₹{price}
          </Typography>
          {mrp &&
            !isNaN(parseFloat(price)) &&
            !isNaN(parseFloat(mrp)) &&
            parseFloat(price) < parseFloat(mrp) && (
              <>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{textDecoration: 'line-through'}}
                >
                  M.R.P.: ₹{mrp}
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: {xs: '14px', sm: '16px', md: '15px'},
                    color: '#9E3393',
                  }}
                >
                  <Box component="span" sx={{fontWeight: 700}}>
                    Deal :
                  </Box>{' '}
                  {Math.round((1 - parseFloat(price) / parseFloat(mrp)) * 100)}%
                </Typography>
              </>
            )}
        </Stack>
        {/* Ratings */}
        {productVariant.reviews && productVariant.reviews.length > 0 ? (
          (() => {
            const totalRatings = productVariant.reviews.length;
            const totalRatingScore = productVariant.reviews.reduce(
              (sum, r) => sum + r.rating,
              0,
            );
            const averageRating = totalRatingScore / totalRatings;
            const totalTextReviews = productVariant.reviews.filter(
              r => r.review && r.review.trim() !== '',
            ).length;

            return (
              <Stack direction="row" spacing={1} alignItems="center">
                <Rating
                  value={parseFloat(averageRating.toFixed(1))}
                  precision={0.5}
                  readOnly
                />
                <Typography variant="body2">
                  ({totalRatings} Ratings
                  {totalTextReviews > 0 ? ` & ${totalTextReviews} Reviews` : ''}
                  )
                </Typography>
              </Stack>
            );
          })()
        ) : (
          <Typography variant="h6" color="text.secondary">
            This product has not yet been rated.
          </Typography>
        )}
        {/* Country of origin */}
        <Box sx={{display: 'flex', alignItems: 'center'}}>
          <Typography
            variant="body2"
            sx={{
              fontSize: {
                xs: '11px',
                sm: '13px',
                md: '15px',
              },
            }}
          >
            Country of Origin :&nbsp;
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: {
                xs: '11px',
                sm: '14px',
                md: '15px',
              },
              fontWeight: 600,
              color: '#515151',
            }}
          >
            India
          </Typography>
        </Box>
        <Divider sx={{my: 1}} />
        <VariantSelector
          variants={otherVariants}
          currentVariantId={productVariant.id}
          onVariantSelect={onVariantSelect}
          productVariantOptions={productVariant.productVariantOptions}
        />
        <Divider sx={{my: 1}} />
        {Array.isArray(productCustomizations) &&
          productCustomizations.length > 0 && (
            <>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                sx={{color: '#00004F'}}
              >
                Personalize
              </Typography>

              <Box
                sx={{
                  backgroundColor: '#f2f3f5',
                  px: 2,
                  py: 2,
                  borderRadius: 3,
                }}
              >
                <ProductCustomizationForm
                  fields={productCustomizations}
                  productVariantId={productVariant.id}
                />
              </Box>
            </>
          )}
        {/* Additional Info */}
        <Box>
          <Typography
            variant="subtitle1"
            fontWeight="bold"
            sx={{color: '#00004F'}}
          >
            Additional Information
          </Typography>
          <Box
            sx={{
              backgroundColor: '#f2f3f5',
              mt: 1,
              borderRadius: 3,
              p: 2,
            }}
          >
            <TextField placeholder="Enter additional info" fullWidth />
          </Box>
        </Box>
        {/* Gift Wrapping */}
        {productVariant.product.isGiftWrapAvailable ? (
          <Box
            sx={{
              backgroundColor: '#f2f3f5',
              px: 2,
              py: 1.5,
              borderRadius: 3,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <Gift style={{color: '#000'}} />
              <Typography fontWeight="bold">Gift Wrapping Available</Typography>
            </Stack>
            <Typography color="text.secondary">₹50 Extra</Typography>
          </Box>
        ) : (
          <Box
            sx={{
              backgroundColor: '#f9f9f9',
              px: 2,
              py: 1.5,
              borderRadius: 3,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <Gift style={{color: '#aaa'}} />
              <Typography color="text.secondary">
                Gift wrapping not available for this product
              </Typography>
            </Stack>
          </Box>
        )}
        {/* Product Notes Box */}
        <Box
          sx={{
            backgroundColor: '#f2f3f5',
            px: 2,
            py: 2,
            borderRadius: 3,
          }}
        >
          <Accordion
            expanded={expanded === 'panel1'}
            onChange={handleChange('panel1')}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
            >
              <Stack direction="row" spacing={1.5} alignItems="center">
                <Designtools size={18} />
                <Typography variant="h6">
                  Handmade/Personalised Work inolved
                </Typography>
              </Stack>
            </AccordionSummary>
            <AccordionDetails>
              <div
                dangerouslySetInnerHTML={{
                  __html: getHtmlContent(productPersonalWork),
                }}
              />
            </AccordionDetails>
          </Accordion>
          <Accordion
            expanded={expanded === 'panel2'}
            onChange={handleChange('panel2')}
          >
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
            >
              <Stack direction="row" spacing={1.5} alignItems="center">
                <Crown size={18} />
                <Typography variant="h6">Uniqueness</Typography>
              </Stack>
            </AccordionSummary>
            <AccordionDetails>
              <div
                dangerouslySetInnerHTML={{
                  __html: getHtmlContent(productUniquenessContent),
                }}
              />
            </AccordionDetails>
          </Accordion>
          <Accordion
            expanded={expanded === 'panel3'}
            onChange={handleChange('panel3')}
          >
            <AccordionSummary
              aria-controls="panel3d-content"
              id="panel3d-header"
            >
              <Stack direction="row" spacing={1.5} alignItems="center">
                <AttachCircle size={18} />
                <Typography variant="h6">Suitability</Typography>
              </Stack>
            </AccordionSummary>
            <AccordionDetails>
              <div
                dangerouslySetInnerHTML={{
                  __html: getHtmlContent(productSuitabilityContent),
                }}
              />
            </AccordionDetails>
          </Accordion>
        </Box>
        <Divider sx={{my: 1}} />
        {/* Tabs Section */}
        <Box sx={{bgcolor: '#f2f3f5', borderRadius: 3, p: 2}}>
          <Tabs
            value={tab}
            onChange={(e, newValue) => setTab(newValue)}
            textColor="primary"
            indicatorColor="primary"
          >
            <Tab label="Specifications" />
            <Tab label="Details" />
            <Tab label="More Info" />
            <Tab label="Box Contents" />
          </Tabs>
          <Box sx={{mt: 2}}>
            {tab === 0 && (
              <Grid container spacing={2}>
                {productSpecificationsContent.length ? (
                  productSpecificationsContent.map((spec, idx) => (
                    <>
                      <Grid item xs={3} key={`label-${idx}`}>
                        <Typography color="text.secondary" noWrap>
                          {spec.name}:
                        </Typography>
                      </Grid>
                      <Grid item xs={9} key={`value-${idx}`}>
                        <Typography>{spec.value}</Typography>
                      </Grid>
                    </>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Typography color="text.secondary">
                      No specifications available.
                    </Typography>
                  </Grid>
                )}
              </Grid>
            )}

            {tab === 1 && (
              <Box>
                <div
                  dangerouslySetInnerHTML={{
                    __html: getHtmlContent(productDetailContent),
                  }}
                />
              </Box>
            )}

            {tab === 2 && (
              <Box>
                <div
                  dangerouslySetInnerHTML={{
                    __html: getHtmlContent(productMoreInfoContent),
                  }}
                />
              </Box>
            )}
            {tab === 3 && (
              <Grid container spacing={2}>
                {productBoxContents.length ? (
                  productBoxContents.map((boxContent, idx) => (
                    <>
                      <Grid item xs={3} key={`label-${idx}`}>
                        <Typography color="text.secondary" noWrap>
                          {boxContent.itemName}:
                        </Typography>
                      </Grid>
                      <Grid item xs={9} key={`value-${idx}`}>
                        <Typography>{boxContent.quantity}</Typography>
                      </Grid>
                    </>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Typography color="text.secondary">
                      No specifications available.
                    </Typography>
                  </Grid>
                )}
              </Grid>
            )}
          </Box>
        </Box>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mt={2}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Avatar src="/dummy-profile.jpg" />
            <Box>
              <Link
                href={`/seller-stores/${productVariant.seller?.id}`}
                passHref
              >
                <Typography
                  fontWeight="bold"
                  sx={{
                    cursor: 'pointer',
                    '&:hover': {textDecoration: 'underline'},
                    color: 'black',
                  }}
                >
                  {productVariant.seller?.userTenant.user.firstName}{' '}
                  {productVariant.seller?.userTenant.user.lastName}
                </Typography>
              </Link>
              {/* <Typography fontWeight="bold">
                {productVariant.seller?.userTenant.user.firstName}{' '}
                {productVariant.seller?.userTenant.user.lastName}
              </Typography> */}
              <Typography variant="body2">Owner</Typography>
              <Typography variant="caption" color="text.secondary">
                Sold 33 Products
              </Typography>
            </Box>
          </Stack>
          <Stack alignItems="center">
            <Button
              variant="contained"
              sx={{
                borderRadius: '20px',
                textTransform: 'none',
                mt: 1,
                bgcolor: '#00004F',
              }}
            >
              Message
            </Button>
          </Stack>
        </Box>{' '}
        {/* Policy Buttons */}
        <Stack
          direction={{xs: 'column', sm: 'row'}}
          spacing={2}
          justifyContent="center"
        >
          <Button
            variant="text"
            onClick={() => setOpenPolicyModal(true)}
            sx={{
              backgroundColor: '#f2f3f5',
              borderRadius: 4,
              textTransform: 'none',
              fontWeight: 'bold',
              color: '#00004F',
              px: 4,
              py: 1.5,
              '&:hover': {
                backgroundColor: '#e0e0e0',
              },
            }}
          >
            Return & Refund Policy
          </Button>

          <Button
            variant="text"
            onClick={() => setOpenDisclaimerModal(true)}
            sx={{
              backgroundColor: '#f2f3f5',
              borderRadius: 4,
              textTransform: 'none',
              fontWeight: 'bold',
              color: '#00004F',
              px: 4,
              py: 1.5,
              '&:hover': {
                backgroundColor: '#e0e0e0',
              },
            }}
          >
            Disclaimer
          </Button>

          <Button
            variant="text"
            onClick={() => setOpenTermsModal(true)}
            sx={{
              backgroundColor: '#f2f3f5',
              borderRadius: 4,
              textTransform: 'none',
              fontWeight: 'bold',
              color: '#00004F',
              px: 4,
              py: 1.5,
              '&:hover': {
                backgroundColor: '#e0e0e0',
              },
            }}
          >
            Terms & Conditions (T&C)
          </Button>
        </Stack>
      </Stack>
      <InfoDialog
        open={openPolicyModal}
        onClose={() => setOpenPolicyModal(false)}
        title="Return & Refund Policy"
        content={returnPolicyContent}
      />
      <InfoDialog
        open={openTermsModal}
        onClose={() => setOpenTermsModal(false)}
        title="Terms & Conditions"
        content={termsAndCondtionContent}
      />
      <InfoDialog
        open={openDisclaimerModal}
        onClose={() => setOpenDisclaimerModal(false)}
        title="Product Disclaimer"
        content={productDisclaimerContent}
      />
    </MainCard>
  );
}

interface InfoDialogProps {
  open: boolean;
  onClose: () => void;
  title: string;
  content?: string;
}

function InfoDialog({open, onClose, title, content}: InfoDialogProps) {
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle sx={{display: 'flex', justifyContent: 'space-between'}}>
        {title}
        <IconButton edge="end" onClick={onClose}>
          <CloseCircle />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <div
          dangerouslySetInnerHTML={{
            __html: getHtmlContent(content),
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
