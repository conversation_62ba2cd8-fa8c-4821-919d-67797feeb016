'use client';

import {useEffect, useState, useCallback} from 'react';
import {
  Box,
  Grid,
  TextField,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  useTheme,
  useMediaQuery,
  CardContent,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import {useGetUserQuery, useUpdateUserMutation} from 'redux/auth/authApiSlice';
import {useFormik} from 'formik';
import {profileFormValidation} from 'validation/profile.validation';
import {extractCountryCode} from 'utils/countryCode';
import {useApiErrorHandler} from 'hooks/useApiErrorHandler';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {UserDto} from 'types/user-profile';
import {Gender} from 'enums/customer.enum';
import AlertDeactivateAccount from 'components/AlertDeactivateAccount';

export default function ProfileForm() {
  const [editableFields, setEditableFields] = useState({
    name: false,
    email: false,
    phoneNumber: false,
  });
  const [openDeactivateDialog, setOpenDeactivateDialog] =
    useState<boolean>(false);
  const {data: user, isLoading, refetch: refetchUser} = useGetUserQuery();
  const [updatePersonalDetails, {error: updateError, reset: updateReset}] =
    useUpdateUserMutation();

  const handleError = useApiErrorHandler();

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleDeactivateDialogClose = useCallback(() => {
    setOpenDeactivateDialog((prev: boolean) => !prev);
  }, []);

  const handleEditClick = (field: keyof typeof editableFields) => {
    setEditableFields(prev => ({...prev, [field]: true}));
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: user?.firstName ?? '',
      lastName: user?.lastName ?? '',
      phoneNumber: extractCountryCode(user?.phone ?? '')?.phoneNumber ?? '',
      email: user?.email ?? '',
      gender: user?.gender ?? '',
    },

    validationSchema: profileFormValidation,
    onSubmit: async values => {
      if (!user?.id) return;

      const countryCode =
        extractCountryCode(user?.phone ?? '')?.countryCode ?? '';
      const updatedData: Partial<UserDto> = {
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        phone: `${countryCode}${values.phoneNumber}`,
        email: values.email,
        gender: values.gender ?? undefined,
      };

      try {
        await updatePersonalDetails({
          id: user.id,
          data: updatedData,
        }).unwrap();
        await refetchUser();
        setEditableFields({name: false, email: false, phoneNumber: false});

        openSnackbar({
          open: true,
          message: 'Profile updated successfully',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      } catch (err) {
        console.error('Update failed:', err);
      }
    },
  });

  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);
  // early return until user data is loaded
  if (isLoading || !user) {
    return <CircularProgress />;
  }
  return (
    <CardContent>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={0} sx={{marginTop: '2%'}}>
          <Grid item xs={12}>
            <Box display="flex" alignItems="center">
              <Typography variant="h5" color="#515151">
                Personal Information
              </Typography>
              <Button
                variant="text"
                size="small"
                onClick={() => handleEditClick('name')}
                sx={{
                  color: '#8e24aa',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                Edit
              </Button>
            </Box>
            <Grid container spacing={2} mt={1}>
              <Grid item xs={6} md={4}>
                <TextField
                  fullWidth
                  name="firstName"
                  value={formik.values.firstName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  disabled={!editableFields.name}
                  error={
                    formik.touched.firstName && Boolean(formik.errors.firstName)
                  }
                  helperText={
                    formik.touched.firstName && formik.errors.firstName
                  }
                />
              </Grid>

              <Grid item xs={6} md={4}>
                <TextField
                  fullWidth
                  name="lastName"
                  value={formik.values.lastName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  disabled={!editableFields.name}
                  error={
                    formik.touched.lastName && Boolean(formik.errors.lastName)
                  }
                  helperText={formik.touched.lastName && formik.errors.lastName}
                />
              </Grid>
            </Grid>

            <Box mt={5}>
              <Typography variant="h5" color="#515151">
                Your Gender
              </Typography>
              <RadioGroup
                row
                name="gender"
                value={formik.values.gender}
                onChange={formik.handleChange}
                sx={{mt: 1, columnGap: 10, ml: 2}}
              >
                <FormControlLabel
                  value={Gender.Male}
                  control={<Radio />}
                  label="Male"
                />

                <FormControlLabel
                  value={Gender.Female}
                  control={<Radio />}
                  label="Female"
                />
                <FormControlLabel
                  value={Gender.Other}
                  control={<Radio />}
                  label="Other"
                />
              </RadioGroup>
            </Box>

            <Box mt={5} display="flex" alignItems="center">
              <Typography variant="h5" color="#515151">
                Email Address
              </Typography>
              <Button
                variant="text"
                size="medium"
                onClick={() => handleEditClick('email')}
                sx={{
                  color: '#8e24aa',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                Edit
              </Button>
            </Box>

            <TextField
              sx={{
                mt: 1,
                width: {
                  xs: '100%',
                  sm: '80%',
                  md: '50%',
                },
              }}
              name="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              disabled={!editableFields.email}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
            />

            {/* Mobile */}
            <Box mt={5} display="flex" alignItems="center">
              <Typography variant="h5" color="#515151">
                Mobile Number
              </Typography>
              <Button
                variant="text"
                size="small"
                onClick={() => handleEditClick('phoneNumber')}
                sx={{
                  color: '#8e24aa',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                Edit
              </Button>
            </Box>
            <TextField
              fullWidth
              sx={{
                mt: 1,
                width: {
                  xs: '100%',
                  sm: '80%',
                  md: '50%',
                },
              }}
              name="phoneNumber"
              value={formik.values.phoneNumber}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              disabled={!editableFields.phoneNumber}
              error={
                formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)
              }
              helperText={
                formik.touched.phoneNumber && formik.errors.phoneNumber
              }
              inputProps={{
                maxLength: 10,
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">{`+${extractCountryCode(user?.phone ?? '')?.countryCode ?? ''}`}</InputAdornment>
                ),
              }}
            />

            {(editableFields.name ||
              editableFields.email ||
              editableFields.phoneNumber ||
              formik.values.gender !== formik.initialValues.gender) && (
              <Box mt={5}>
                <Button
                  variant="contained"
                  type="submit"
                  sx={{
                    backgroundColor: '#00004F',
                    color: 'white',
                    fontWeight: 'bold',
                    borderRadius: '20px',
                    width: isMobile ? '100%' : '50%',
                    padding: '12px',
                    textTransform: 'none',
                  }}
                >
                  Save Changes
                </Button>
              </Box>
            )}

            <Box mt={5}>
              <Button
                variant="contained"
                onClick={handleDeactivateDialogClose}
                sx={{
                  fontWeight: 'bold',
                  borderRadius: '20px',
                  width: isMobile ? '100%' : '50%',
                  padding: '12px',
                  textTransform: 'none',
                }}
              >
                Deactivate Account
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
      <AlertDeactivateAccount
        id={user?.profileId ?? ''}
        open={openDeactivateDialog}
        handleClose={handleDeactivateDialogClose}
      />
    </CardContent>
  );
}
