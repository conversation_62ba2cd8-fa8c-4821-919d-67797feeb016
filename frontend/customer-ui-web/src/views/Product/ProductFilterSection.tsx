import {Box, Stack, Typography} from '@mui/material';
import {PageSection, SectionItem} from 'types/page-section';

const CARD_STYLE = {
  width: 200,
  borderRadius: 2,
  cursor: 'pointer',
  overflow: 'hidden',
  boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
  textAlign: 'center',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
  },
};

const PRICE_TITLE_STYLE = {
  backgroundColor: '#C8D3D5',
  padding: '8px',
  minHeight: '80px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
};

const DISCOUNT_TITLE_STYLE = {
  backgroundColor: '#C8D3D5',
  padding: '8px',
  minHeight: '80px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
};

const ProductFilterSection = ({section}: {section: PageSection}) => {
  const items = section.sectionItems || [];

  const priceFilters = items.filter(
    item =>
      Array.isArray(item.metadata?.filter) &&
      item.metadata.filter.some(f => f.field === 'price'),
  );

  const discountFilters = items.filter(
    item =>
      Array.isArray(item.metadata?.filter) &&
      item.metadata.filter.some(f => f.field === 'discountPercent'),
  );

  const renderPriceCard = (item: SectionItem) => {
    const underIndex = item.title.toLowerCase().indexOf('under');
    const prefix =
      underIndex >= 0
        ? item.title.slice(0, underIndex + 5).trim()
        : 'Price under';
    const amount =
      underIndex >= 0 ? item.title.slice(underIndex + 5).trim() : item.title;

    return (
      <Box
        key={item.id}
        sx={CARD_STYLE}
        onClick={() => window.open(item.metadata?.redirectUrl, '_blank')}
      >
        <Box
          component="img"
          src={item.previewUrl}
          alt={item.title}
          sx={{
            width: '100%',
            height: 140,
            objectFit: 'contain',
            backgroundColor: '#f5f5f5',
          }}
        />
        <Box sx={PRICE_TITLE_STYLE}>
          <Typography variant="body1">{prefix}</Typography>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              lineHeight: 1.2,
              fontSize: '2rem',
              color: '#00004F',
            }}
          >
            {amount}
          </Typography>
        </Box>
      </Box>
    );
  };

  const renderDiscountCard = (item: SectionItem) => {
    const discountParts = item.title.split(/(\d+% off)/).filter(Boolean);
    const mainText = discountParts[0].trim();
    const percentageText = discountParts[1] || item.title;

    return (
      <Box
        key={item.id}
        sx={{
          ...CARD_STYLE,
          display: 'flex',
          flexDirection: 'column-reverse',
        }}
        onClick={() => window.open(item.metadata?.redirectUrl, '_blank')}
      >
        <Box
          component="img"
          src={item.previewUrl}
          alt={item.title}
          sx={{
            width: '100%',
            height: 140,
            objectFit: 'contain',
            backgroundColor: '#f5f5f5',
          }}
        />
        <Box sx={DISCOUNT_TITLE_STYLE}>
          <Typography variant="body1">{mainText}</Typography>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              lineHeight: 1.2,
              fontSize: '2rem',
              color: '#00004F',
            }}
          >
            {percentageText}
          </Typography>
        </Box>
      </Box>
    );
  };

  return (
    <Stack direction="row" spacing={5}>
      {/* Price Filters */}
      <Box flex={1}>
        <Typography variant="h5" gutterBottom>
          Price Categories
        </Typography>
        <Stack direction="row" flexWrap="wrap" gap={2}>
          {priceFilters.map(renderPriceCard)}
        </Stack>
      </Box>

      {/* Discount Filters */}
      <Box flex={1}>
        <Typography variant="h5" gutterBottom>
          Discount Offers
        </Typography>
        <Stack direction="row" flexWrap="wrap" gap={2}>
          {discountFilters.map(renderDiscountCard)}
        </Stack>
      </Box>
    </Stack>
  );
};

export default ProductFilterSection;
