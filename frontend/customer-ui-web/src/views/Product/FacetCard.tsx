import {Box, Typography} from '@mui/material';
import {CardStyle} from 'enums/pageSection.enum';
import {useRouter} from 'next/navigation';

interface Props {
  title: string;
  subtitle?: string;
  thumbnail: string;
  facetValueIds: string[];
  cardStyle: CardStyle;
}

export default function FacetCard({
  title,
  subtitle,
  thumbnail,
  facetValueIds,
  cardStyle,
}: Props) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`listing?facetValueIds=${facetValueIds.join(',')}`);
  };

  const cardWidth = 180;
  const cardHeight = 240;

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: cardWidth,
        height: cardHeight,
        borderRadius: 2,
        overflow: 'hidden',
        cursor: 'pointer',
        boxShadow: 1,
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#C8D3D5',
      }}
    >
      {(() => {
        switch (cardStyle) {
          case CardStyle.IMAGE_TITLE_SUBTITLE:
            return (
              <>
                <Box
                  sx={{
                    p: 2,
                    flex: 1,
                    backgroundColor: '#C8D3D5',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    color: '#0E1C1F',
                  }}
                >
                  <Typography
                    variant="h6"
                    fontWeight={700}
                    sx={{color: '#0E1C1F'}}
                  >
                    {title}
                  </Typography>
                  {subtitle && (
                    <Typography
                      variant="caption"
                      sx={{color: '#0E1C1F', mt: 1}}
                    >
                      {subtitle}
                    </Typography>
                  )}
                </Box>
                <Box
                  sx={{
                    height: '50%',
                    backgroundImage: `url(${thumbnail})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                />
              </>
            );

          case CardStyle.IMAGE_TITLE:
            return (
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  display: 'flex',
                  alignItems: 'flex-end',
                  p: 2,
                }}
              >
                <Typography
                  variant="subtitle1"
                  fontWeight={600}
                  sx={{textShadow: '0 0 5px rgba(0,0,0,0.6)', color: '#FFFFFF'}}
                >
                  {title}
                </Typography>
              </Box>
            );

          case CardStyle.IMAGE_ONLY:
            return (
              <Box
                sx={{
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              />
            );

          default:
            return (
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-end',
                  p: 2,
                }}
              >
                <Typography
                  variant="subtitle1"
                  color="white"
                  fontWeight={600}
                  sx={{textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography
                    variant="body2"
                    color="white"
                    sx={{textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
                  >
                    {subtitle}
                  </Typography>
                )}
                <Typography
                  variant="caption"
                  color="white"
                  mt={1}
                  sx={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    px: 1,
                    borderRadius: 1,
                    alignSelf: 'flex-start',
                  }}
                >
                  Unknown Style: {cardStyle}
                </Typography>
              </Box>
            );
        }
      })()}
    </Box>
  );
}
