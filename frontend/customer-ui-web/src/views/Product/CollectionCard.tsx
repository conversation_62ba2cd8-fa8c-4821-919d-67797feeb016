'use client';

import {Box, Typography} from '@mui/material';
import {useRouter} from 'next/navigation';
import {SectionItem} from 'types/page-section';

type Props = {
  item: SectionItem;
};

const CollectionCard = ({item}: Props) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`listing?collectionIds=${item.entityId}`);
  };

  return (
    <Box
      onClick={handleClick}
      key={item.id}
      sx={{
        width: 200,
        flex: '0 0 auto',
        border: '1px solid #e0e0e0',
        borderRadius: '16px',
        overflow: 'hidden',
        backgroundColor: '#fff',
        cursor: 'pointer',
        position: 'relative',
        '&:hover': {
          transform: 'scale(1.02)',
        },
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: 230,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        <img
          src={item.previewUrl}
          alt={item.title || 'Collection'}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block',
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            width: '100%',
            textAlign: 'center',
            pt: 1,
          }}
        >
          <Typography
            variant="subtitle1"
            noWrap
            sx={{
              color: '#000',
              background: 'transparent',
              fontWeight: 500,
            }}
          >
            {item.collection?.name || item.title}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default CollectionCard;
