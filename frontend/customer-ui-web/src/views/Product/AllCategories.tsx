'use client';

import {
  <PERSON>,
  Grid,
  <PERSON>ack,
  Typo<PERSON>,
  Button,
  CircularProgress,
} from '@mui/material';
import {useEffect, useState} from 'react';
import {Collection, ProductVariant} from 'types/product';
import {IFilter} from 'types/api';
import {useGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';
import ProductCard from 'views/products/ProductCard';
import {useGetProductVariantsQuery} from 'redux/ecom/productVariantApiSlice';
import {useRouter} from 'next/navigation';

const AllCategoriesSection = () => {
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);

  // Fetch all collections
  const {data: collections, isLoading: isCollectionsLoading} =
    useGetCollectionsQuery({
      fields: {id: true, name: true},
    });

  // Set default collection once loaded
  useEffect(() => {
    if (collections && collections.length > 0 && !selectedCollectionId) {
      setSelectedCollectionId(collections[0].id);
    }
  }, [collections, selectedCollectionId]);

  // Prepare product variant filter
  const productVariantFilter: IFilter | void = selectedCollectionId
    ? {
        include: [
          {
            relation: 'product',
            scope: {
              ...(selectedCollectionId && {
                where: {
                  collectionId: selectedCollectionId,
                },
              }),
            },
            required: true,
          },
          {
            relation: 'productVariantPrice',
          },
          {
            relation: 'featuredAsset',
          },
        ],
        limit: 8,
      }
    : undefined;

  // Fetch product variants
  const {data: productVariants, isLoading: isVariantsLoading} =
    useGetProductVariantsQuery(productVariantFilter);

  const router = useRouter();

  return (
    <Box mt={4}>
      <Stack flexDirection="row" justifyContent="space-between" mb={2}>
        <Typography variant="h4">All Categories</Typography>
        <Button
          variant="outlined"
          onClick={() => router.push('view-more?type=all-categories')}
        >
          View More
        </Button>
      </Stack>

      {/* Categories */}
      {isCollectionsLoading ? (
        <CircularProgress size={24} />
      ) : (
        <Grid container spacing={2} mb={3}>
          {collections?.map((collection: Collection) => (
            <Grid item key={collection.id}>
              <Button
                variant={
                  selectedCollectionId === collection.id
                    ? 'contained'
                    : 'outlined'
                }
                onClick={() => setSelectedCollectionId(collection.id)}
                sx={{
                  borderRadius: 20,
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  minWidth: 120,
                  backgroundColor:
                    selectedCollectionId === collection.id
                      ? '#9A2D8E'
                      : 'white',
                  color:
                    selectedCollectionId === collection.id ? 'white' : 'black',
                }}
              >
                {collection.name}
              </Button>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Product Grid */}
      {isVariantsLoading ? (
        <CircularProgress />
      ) : (
        <Grid container spacing={3}>
          {productVariants?.map((product: ProductVariant) => (
            <Grid item xs={12} sm={6} md={3} key={product.id}>
              <ProductCard productVariant={product} />
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default AllCategoriesSection;
