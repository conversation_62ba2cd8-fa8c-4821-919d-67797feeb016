import {useEffect} from 'react';
import {<PERSON>, <PERSON>ton, <PERSON>ack, Typography} from '@mui/material';
import {useLazyGetProductVariantsQuery} from 'redux/ecom/ecomApiSlice';
import ProductCard from 'views/products/ProductCard';
import {ReviewStatus} from 'types/review';
import {User} from 'types/user-profile';
import {useRouter} from 'next/navigation';

export const GiftProductsSection = ({
  title,
  isLoggedIn,
  user,
}: {
  title: string;
  isLoggedIn: boolean;
  user?: User;
}) => {
  const [fetchGiftVariants, {data = [], isLoading}] =
    useLazyGetProductVariantsQuery();

  useEffect(() => {
    fetchGiftVariants({
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: {preview: true, id: true},
          },
        },
        {
          relation: 'product',
          required: true,
          scope: {
            fields: {description: true, id: true},
            where: {isGiftWrapAvailable: true},
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {
              price: true,
              mrp: true,
              currencyCode: true,
            },
          },
        },
        ...(isLoggedIn
          ? [
              {
                relation: 'wishlist',
                scope: {
                  where: {
                    deleted: false,
                    customerId: user?.profileId,
                  },
                  fields: {id: true},
                },
              },
            ]
          : []),
        {
          relation: 'reviews',
          scope: {
            fields: {
              rating: true,
            },
            where: {
              status: ReviewStatus.APPROVED,
            },
          },
        },
      ],
      fields: {
        name: true,
        id: true,
        featuredAssetId: true,
        productId: true,
      },
      limit: 10,
    });
  }, [fetchGiftVariants]);

  const scrollGradientSx = {
    overflowX: 'auto',
    pb: 2,
    '&::-webkit-scrollbar': {
      height: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: 'transparent',
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'linear-gradient(to right, #5847F9, #00C9FF)',
      borderRadius: '10px',
      minWidth: '30px',
    },
  };

  const router = useRouter();

  return (
    <Stack flexDirection="column" gap={3} mt={3}>
      <Stack flexDirection="row" justifyContent="space-between">
        <Typography variant="h4">{title}</Typography>
        <Button
          variant="outlined"
          onClick={() => router.push('view-more?type=gift')}
        >
          View More
        </Button>
      </Stack>
      <Stack
        flexDirection="row"
        justifyContent="space-between"
        sx={scrollGradientSx}
      >
        {isLoading ? (
          <Typography>Loading...</Typography>
        ) : (
          data.map(variant => (
            <Box
              key={variant.id}
              sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
            >
              <ProductCard productVariant={variant} />
            </Box>
          ))
        )}
      </Stack>
    </Stack>
  );
};
