'use client';

import {
  Ava<PERSON>,
  Box,
  Button,
  Divider,
  Grid,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import Image from 'next/image';
import {useDispatch} from 'react-redux';
import {setCredentials, unsetCredentials} from 'redux/auth/authSlice';
import {
  getTokenForUser,
  getCurrentUser,
  setCurrentUser,
  getAccounts,
  Account,
  removeTokenForUser,
  removeAccount,
} from 'utils/switchAccount';
import {useRouter} from 'next/navigation';
import {useEffect, useState} from 'react';

export default function SwitchAccountPage() {
  const dispatch = useDispatch();
  const router = useRouter();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [_, setCurrentEmail] = useState<string | null>(null);

  useEffect(() => {
    const syncAccounts = () => {
      const current = getCurrentUser();
      const allAccounts = getAccounts();

      const updated = allAccounts.map(acc => ({
        ...acc,
        isActive: acc.email === current,
      }));

      setAccounts(updated);
      setCurrentEmail(current);
    };

    syncAccounts();
  }, []);

  const handleSwitch = (email: string) => {
    const token = getTokenForUser(email);
    if (!token) return;

    localStorage.setItem('accessToken', token.accessToken);
    localStorage.setItem('refreshToken', token.refreshToken);
    setCurrentUser(email);

    dispatch(setCredentials(token));

    router.push('/');
  };
  const handleSignOut = (email: string) => {
    const current = getCurrentUser();
    removeTokenForUser(email);
    removeAccount(email);
    const remainingAccounts = getAccounts();
    if (email === current) {
      if (remainingAccounts.length === 0) {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('current_user');
        dispatch(unsetCredentials());
        router.push('/login');
        return;
      } else {
        const next = remainingAccounts[0];
        const token = getTokenForUser(next.email);
        if (token) {
          localStorage.setItem('acessToken', token.accessToken);
          localStorage.setItem('refreshToken', token.refreshToken);
          setCurrentUser(next.email);
          dispatch(setCredentials(token));
        }
      }
    }
    const updated = remainingAccounts.map(acc => ({
      ...acc,
      isActive: acc.email === getCurrentUser(),
    }));
    setAccounts(updated);
  };
  const handleAddAccount = async () => {
    router.push(`/login?avoid-callback=true`);
  };

  return (
    <Box
      sx={{
        width: {xs: '90%', sm: '70%', md: '50%', lg: '40%'},
        mx: 'auto',
      }}
    >
      <Box display="flex" justifyContent="center">
        <Image
          src="/assets/images/Logo landscape.png"
          alt="EcomDukes Logo"
          width={250}
          height={100}
          style={{maxWidth: '100%', height: 'auto'}}
        />
      </Box>
      <Paper
        elevation={1}
        sx={{
          borderRadius: 2,
          p: {xs: 2, sm: 3},
        }}
      >
        <Typography
          variant="h4"
          sx={{fontWeight: 'bold', mb: 2, fontSize: {xs: '1rem', sm: '1.2rem'}}}
          textAlign="center"
        >
          Switch accounts
        </Typography>
        <Divider sx={{mb: 2}} />

        {accounts.map(account => {
          const isActive = account.isActive;
          const initials = account.email?.[0].toUpperCase();

          return (
            <Box
              key={account.email}
              onClick={() => handleSwitch(account.email)}
              sx={{
                mb: 2,
                bgcolor: isActive ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                borderRadius: 1,
                px: 1,
                cursor: 'pointer',
              }}
            >
              <Grid container alignItems="center">
                <Grid item xs={2} textAlign="center" mt={0.5}>
                  <Avatar sx={{width: 40, height: 40}}>{initials}</Avatar>
                </Grid>
                <Grid item xs={8}>
                  <Typography
                    variant="h6"
                    sx={{
                      color: account.isActive ? '#0F1111' : 'text.primary',
                      fontWeight: account.isActive ? 600 : 500,
                      fontSize: {xs: '0.9rem', sm: '1rem'},
                    }}
                  >
                    {account.email}
                  </Typography>
                </Grid>
                <Grid item xs={2} textAlign="right">
                  <Button
                    size="small"
                    onClick={() => handleSignOut(account.email)}
                    sx={{
                      minWidth: 'unset',
                      fontSize: {xs: '0.75rem', sm: '0.8rem'},

                      '&:hover': {
                        backgroundImage: 'none',
                        backgroundColor: 'transparent',
                      },
                    }}
                  >
                    Sign Out
                  </Button>
                </Grid>
              </Grid>
              <Divider sx={{my: 2}} />
            </Box>
          );
        })}

        <Stack
          direction="row"
          spacing={2}
          alignItems="center"
          sx={{cursor: 'pointer'}}
          onClick={handleAddAccount}
        >
          <AddCircleOutlineIcon color="action" />
          <Typography
            color="primary"
            fontWeight={600}
            fontSize={{xs: '0.5rem', sm: '0.9rem'}}
          >
            Add New Account{' '}
          </Typography>
        </Stack>
      </Paper>
    </Box>
  );
}
