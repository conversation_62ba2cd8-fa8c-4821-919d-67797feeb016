import {cashfree} from 'utils/cashfreeUtil';
import {
  Avatar,
  Box,
  Button,
  Divider,
  FormControlLabel,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  Checkbox,
} from '@mui/material';
import MainCard from 'components/MainCard';
import {useEffect, useState} from 'react';
import CartAddressCard from './CartAddressCard';
import {
  useCheckoutCartMutation,
  useUpdateCartMutation,
} from 'redux/ecom/cartApiSlice';
import {CloseSquare, Tag} from 'iconsax-react';
import CouponSection from './CouponModal';
import {Cart} from 'types/cart';
import {PromoCode} from 'types/promoCode';
import {useSetAtom} from 'jotai';
import {productCustomizationsAtom} from 'utils/atoms/productCustomizationAtom';
import {useGetDukeCoinBalanceQuery} from 'redux/ecom/ecomDukeCoinApiSlice';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {useGetAddressesQuery} from 'redux/address/customerAddressApiSlice';
import {AddressDto} from 'types/address';
import {useCheckDiscountEligibilityQuery} from 'redux/discount/discountApiSlice';

interface PaymentSummaryProps {
  cart: Cart;
  refetchCart: () => void;
}

const PaymentSummary = ({cart, refetchCart}: PaymentSummaryProps) => {
  const [paymentMethod, setPaymentMethod] = useState('upi');
  const [gstInvoiceEnabled, setGstInvoiceEnabled] = useState(false);
  const [gstNumber, setGstNumber] = useState('');
  const [businessName, setBusinessName] = useState('');
  const [businessAddress, setBusinessAddress] = useState('');
  const [isCouponModalOpen, setCouponModalOpen] = useState(false);
  const [useDukeCoins, setUseDukeCoins] = useState(true);

  const [checkoutCart] = useCheckoutCartMutation();
  const [updateCart] = useUpdateCartMutation();

  const [appliedPromoCodes, setAppliedPromoCodes] = useState<
    Record<string, PromoCode>
  >({});
  const {data: coinData, isLoading: isCoinLoading} =
    useGetDukeCoinBalanceQuery();
  const dukeCoinDiscount =
    !isCoinLoading && useDukeCoins ? Number(coinData?.maxApplicable ?? 0) : 0;

  const resetProductCustomizations = useSetAtom(productCustomizationsAtom);
  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;
  const {data: addresses = []} = useGetAddressesQuery(customerId ?? '', {
    skip: !customerId,
  });

  const [selectedShippingAddress, setSelectedShippingAddress] =
    useState<AddressDto | null>(null);
  const [selectedBillingAddress, setSelectedBillingAddress] =
    useState<AddressDto | null>(null);

  const cartItems = cart.cartItems ?? [];
  const productIds = cartItems.map(item => item.id);

  const {totalMRP, totalPrice, totalDiscount} = cartItems.reduce(
    (acc, cartItem) => {
      const priceData = cartItem?.productVariant?.productVariantPrice;
      const quantity = cartItem.quantity;
      const mrp = parseFloat(priceData?.mrp || '0') * quantity;
      const price = parseFloat(priceData?.price || '0') * quantity;

      acc.totalMRP += mrp;
      acc.totalPrice += price;
      acc.totalDiscount += mrp - price;
      return acc;
    },
    {totalMRP: 0, totalPrice: 0, totalDiscount: 0},
  );

  let promoDiscount = 0;
  const appliedPromoCode = Object.values(appliedPromoCodes)[0];

  if (appliedPromoCode) {
    if (appliedPromoCode.type === 'flat') {
      promoDiscount = Number(appliedPromoCode.value) || 0;
    } else if (appliedPromoCode.type === 'percentage') {
      const discount =
        (totalPrice * (Number(appliedPromoCode.value) || 0)) / 100;
      promoDiscount = appliedPromoCode.maxDiscountCap
        ? Math.min(
            discount,
            Number(appliedPromoCode.maxDiscountCap) || discount,
          )
        : discount;
    }
  }

  const {data: discountEligibility} = useCheckDiscountEligibilityQuery(
    {
      cartTotal: totalPrice,
      isFromApp: true,
    },
    {
      skip: totalPrice <= 0,
    },
  );

  const discountAmount =
    discountEligibility?.appliedDiscount?.discountType === 'PERCENT'
      ? (discountEligibility.appliedDiscount.discountValue / 100) * totalPrice
      : (discountEligibility?.appliedDiscount?.discountValue ?? 0);

  // const formattedPromoDiscount = promoDiscount.toFixed(2);
  const finalPrice = Math.max(
    totalPrice - promoDiscount - dukeCoinDiscount - discountAmount,
    0,
  );
  const appliedCouponCodes = Object.values(appliedPromoCodes)
    .map(p => p.code)
    .join(', ');

  const handleCheckout = async () => {
    if (!selectedShippingAddress?.id || !selectedBillingAddress?.id) {
      return;
    }
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const payload: {
      shippingAddressId: string;
      billingAddressId: string;
      ecomDukeCoinsApplied: number;
      discountConditionId?: string;
    } = {
      shippingAddressId: selectedShippingAddress.id,
      billingAddressId: selectedBillingAddress.id,
      ecomDukeCoinsApplied: useDukeCoins
        ? Number(coinData?.maxApplicable || 0)
        : 0,
    };

    const discountConditionId =
      discountEligibility?.appliedDiscount?.conditionId;

    if (discountConditionId) {
      payload.discountConditionId = discountConditionId;
    }
    const response = await checkoutCart(payload).unwrap();
    const paymentSessionId = response?.payment_session_id;
    if (paymentSessionId) {
      resetProductCustomizations({});
      cashfree.checkout({
        paymentSessionId,
        useDukeCoins,
        returnUrl: `${baseUrl}/order-confirmation/${response.order_id}`,
        redirectTarget: '_self',
      });
    }
  };

  useEffect(() => {
    if (cart.promoCode) {
      setAppliedPromoCodes({[cart.promoCode.id]: cart.promoCode});
    }
  }, [cart.promoCode]);
  useEffect(() => {
    if (addresses.length > 0 && !selectedShippingAddress) {
      setSelectedShippingAddress(addresses[addresses.length - 1]);
    }
    if (addresses.length > 0 && !selectedBillingAddress) {
      setSelectedBillingAddress(addresses[addresses.length - 1]);
    }
  }, [addresses, selectedShippingAddress, selectedBillingAddress]);

  useEffect(() => {
    if (
      !isCoinLoading &&
      (!coinData?.maxApplicable || Number(coinData.maxApplicable) <= 0)
    ) {
      setUseDukeCoins(false);
    }
  }, [coinData, isCoinLoading]);

  return (
    <>
      <MainCard sx={{p: 3, borderRadius: 2, boxShadow: 1, mb: 2}}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          Payment Method
        </Typography>

        <RadioGroup
          value={paymentMethod}
          onChange={e => setPaymentMethod(e.target.value)}
        >
          <Box sx={{mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 3}}>
            <FormControlLabel
              value="upi"
              control={<Radio />}
              label={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Avatar src="/upi-icon.png" sx={{width: 24, height: 24}} />
                  <Typography>UPI Payment</Typography>
                </Stack>
              }
            />
          </Box>
          <Box sx={{mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 3}}>
            <FormControlLabel
              value="card"
              control={<Radio />}
              label={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Avatar src="/visa-icon.png" sx={{width: 24, height: 24}} />
                  <Typography>Card / Net Banking</Typography>
                </Stack>
              }
            />
          </Box>
        </RadioGroup>

        {!isCoinLoading && (coinData?.maxApplicable ?? 0) > 0 && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: '#f5f5f5',
              borderRadius: 2,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  checked={useDukeCoins}
                  onChange={async e => {
                    const checked = e.target.checked;
                    setUseDukeCoins(checked);

                    if (cart?.id) {
                      await updateCart({
                        id: cart.id,
                        data: {
                          ecomDukeCoinsApplied: checked
                            ? Number(coinData?.maxApplicable || 0)
                            : 0,
                        },
                      }).unwrap();
                      refetchCart();
                    }
                  }}
                />
              }
              label={
                <Stack spacing={0.5}>
                  <Typography variant="body1" fontWeight="bold">
                    Use Duke Coins
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    ₹{Number(coinData?.maxApplicable).toFixed(2)} will be
                    applied
                  </Typography>
                </Stack>
              }
            />
            <Typography variant="body1" fontWeight="bold">
              -₹
              {useDukeCoins
                ? Number(coinData?.maxApplicable).toFixed(2)
                : '0.00'}
            </Typography>
          </Box>
        )}

        <Divider sx={{my: 2}} />

        <Stack spacing={1}>
          <SummaryRow label="Item(s) Total [MRP]" value={totalMRP} />
          <SummaryRow label="Shop Discount" value={-totalDiscount} />
          <SummaryRow label="Sub Total" value={totalPrice} />
          <Divider sx={{my: 1}} />
          {discountEligibility?.appliedDiscount && (
            <SummaryRow label="Special Discount" value={-discountAmount} />
          )}
          <SummaryRow label="Promo Code" value={-promoDiscount} />
          <SummaryRow label="Gift Wrap" value={0} />
          <Divider sx={{my: 1}} />
          {discountEligibility?.discountBreakdownMessage && (
            <Typography
              variant="body2"
              color="success.main"
              sx={{mt: 1, fontWeight: 'bold'}}
            >
              {discountEligibility.discountBreakdownMessage}
            </Typography>
          )}

          {discountEligibility?.nextTierMessage && (
            <Typography variant="body1" color="primary.main" sx={{mt: 0.5}}>
              {discountEligibility.nextTierMessage}
            </Typography>
          )}

          <SummaryRow label="Delivery Charge" value={0} bold />
          <SummaryRow label="Total Payable" value={finalPrice} bold />
          <SummaryRow
            label="Estimated delivery:"
            valueText="25 May - 08 Jun"
            bold
          />
        </Stack>

        <Box sx={{mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 3}}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="body1" fontWeight="bold">
              GST Invoice
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={gstInvoiceEnabled}
                  onChange={e => setGstInvoiceEnabled(e.target.checked)}
                />
              }
              label=""
            />
          </Stack>

          {gstInvoiceEnabled && (
            <Box
              sx={{
                mt: 2,
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
              }}
            >
              <Stack spacing={2}>
                <TextField
                  fullWidth
                  size="small"
                  label="GST Number"
                  value={gstNumber}
                  onChange={e => setGstNumber(e.target.value)}
                />
                <TextField
                  fullWidth
                  size="small"
                  label="Business Name"
                  value={businessName}
                  onChange={e => setBusinessName(e.target.value)}
                />
                <TextField
                  fullWidth
                  size="small"
                  label="Business Address"
                  multiline
                  rows={2}
                  value={businessAddress}
                  onChange={e => setBusinessAddress(e.target.value)}
                />
              </Stack>
            </Box>
          )}
        </Box>

        {cart.promoCodeId && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: '#f5f5f5',
              borderRadius: 2,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography variant="body2" color="textSecondary">
              Applied Coupon Code: <strong>{appliedCouponCodes}</strong>
            </Typography>
            <Typography variant="body1" fontWeight="bold">
              ₹{promoDiscount.toFixed(2)}
            </Typography>
          </Box>
        )}

        <Button
          variant="outlined"
          fullWidth
          startIcon={<Tag />}
          sx={{mt: 2, borderRadius: '20px'}}
          onClick={() => setCouponModalOpen(true)}
        >
          {cart.promoCodeId ? 'Change Coupon Code' : 'Apply Coupon Code'}
        </Button>

        <Button
          variant="contained"
          fullWidth
          size="large"
          sx={{mt: 3, borderRadius: '20px', py: 1.5}}
          onClick={handleCheckout}
        >
          Proceed to checkout
        </Button>
      </MainCard>
      <Dialog
        open={isCouponModalOpen}
        onClose={() => setCouponModalOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            Apply Coupon
            <IconButton onClick={() => setCouponModalOpen(false)}>
              <CloseSquare />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <CouponSection
            productId={productIds?.[0]}
            cart={cart}
            appliedPromoCodes={appliedPromoCodes}
            setAppliedPromoCodes={setAppliedPromoCodes}
            handleClose={() => setCouponModalOpen(false)}
            refetchCart={refetchCart}
          />
        </DialogContent>
      </Dialog>
      <CartAddressCard
        addresses={addresses}
        selectedShippingAddress={selectedShippingAddress}
        onSelectAddress={setSelectedShippingAddress}
        selectedBillingAddress={selectedBillingAddress}
        onSelectBillingAddress={setSelectedBillingAddress}
      />{' '}
    </>
  );
};

const SummaryRow = ({
  label,
  value,
  valueText,
  bold = false,
}: {
  label: string;
  value?: number;
  valueText?: string;
  bold?: boolean;
}) => (
  <Stack direction="row" justifyContent="space-between">
    <Typography
      variant={bold ? 'h6' : 'body1'}
      fontWeight={bold ? 'bold' : 'normal'}
    >
      {label}
    </Typography>
    <Typography
      variant={bold ? 'h6' : 'body1'}
      fontWeight={bold ? 'bold' : 'normal'}
    >
      {valueText ?? `₹${value?.toFixed(2) ?? '0.00'}`}
    </Typography>
  </Stack>
);

export default PaymentSummary;
