'use client';
import {
  <PERSON>po<PERSON>,
  <PERSON>,
  IconButton,
  Card,
  CardContent,
  Link,
  Grid,
} from '@mui/material';
import {SupportStatus, TicketCategory} from 'enums/help.enum';
import {CreateTicketDto} from 'types/help';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {useFormik} from 'formik';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  MenuItem,
  DialogActions,
  Alert,
  Select,
} from '@mui/material';
import {useEffect, useState} from 'react';
import {Close} from '@mui/icons-material';
import * as Yup from 'yup';
import {Trash, Whatsapp} from 'iconsax-react';
import {
  useCreateSupportTicketMutation,
  useGetSupportContactQuery,
} from 'redux/help/helpApiSlice';
function CustomerServicePage() {
  const {data: contactList = [], isLoading} = useGetSupportContactQuery();
  const contact = contactList[0];
  const [open, setOpen] = useState(false);
  const [success, setSuccess] = useState('');
  const {data: user} = useGetUserQuery();
  const [filePreviews, setFilePreviews] = useState<string[]>([]);

  const [createSupportTicket, {isLoading: creating}] =
    useCreateSupportTicketMutation();
  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      category: '',
      attachments: [] as File[],
      priority: 1,
    },
    validationSchema: Yup.object({
      title: Yup.string().required('Title is required'),
      description: Yup.string().required('Summary is required'),
      category: Yup.string().required('Category is required'),
    }),
    onSubmit: async (values, {resetForm}) => {
      const baseImages = await Promise.all(
        values.attachments.map(
          file =>
            new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.readAsDataURL(file);
              reader.onload = () => resolve(reader.result as string);
              reader.onerror = error => reject(error);
            }),
        ),
      );

      const payload: CreateTicketDto = {
        title: values.title,
        description: values.description,
        category: values.category as TicketCategory,
        attachments: baseImages[0] ?? undefined,
        priority: Number(values.priority),
        status: SupportStatus.Open,
        assignee: user?.id ?? '',
      };

      await createSupportTicket(payload).unwrap();
      openSnackbar({
        open: true,
        message: `Ticket submitted successfully!`,
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);

      resetForm();
      setOpen(false);

      setSuccess('');
    },
  });

  useEffect(() => {
    const urls = formik.values.attachments.map(file =>
      URL.createObjectURL(file),
    );
    setFilePreviews(urls);
    return () => urls.forEach(url => URL.revokeObjectURL(url));
  }, [formik.values.attachments]);
  return (
    <Grid
      container
      sx={{
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Typography
        variant="h4"
        fontWeight={600}
        textAlign="center"
        sx={{mb: 1, fontSize: {xs: '20px', sm: '28px'}}}
      >
        Customer Service
      </Typography>

      <Typography
        variant="body1"
        textAlign="center"
        color="text.secondary"
        sx={{
          maxWidth: 600,
          mx: 'auto',
          mb: 4,
          fontSize: {xs: '14px', sm: '16px'},
        }}
      >
        Have a question or need help with your orders or account? Our support
        team is here to assist you. Feel free to reach out through the following
        channels.
      </Typography>
      <Card
        sx={{
          backgroundColor: 'white',
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          //   mt: 20,
          width: '100%',
        }}
      >
        <CardContent sx={{p: {xs: 1, sm: 2}}}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: {xs: 1, sm: 2},
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: {xs: 0.5, sm: 1},
                  flexWrap: 'wrap',
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 'bold',
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                  }}
                >
                  Mail us :
                </Typography>
                <Link
                  href={`mailto:${contact?.supportEmail}`}
                  underline="none"
                  sx={{
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                    fontWeight: 400,
                    wordBreak: 'break-word',
                  }}
                >
                  {contact?.supportEmail ?? 'N/A'}
                </Link>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: {xs: 0.5, sm: 1},
                  flexWrap: 'wrap',
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 'bold',
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                  }}
                >
                  Contact Through :
                </Typography>
                <IconButton
                  href={`https://wa.me/${contact?.supportPhone?.replace(/\D/g, '')}`}
                  target="_blank"
                  aria-label="WhatsApp"
                  sx={{
                    backgroundColor: '#25D366',
                    width: {xs: 24, sm: 28},
                    height: {xs: 24, sm: 28},
                    borderRadius: '50%',
                    '&:hover': {backgroundColor: '#1EBE5D'},
                  }}
                >
                  <Whatsapp size="18" color="#FFF" />
                </IconButton>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                    fontWeight: 400,
                    wordBreak: 'break-word',
                  }}
                >
                  {contact?.supportPhone
                    ? `+91${contact.supportPhone}`
                    : '+91 0000000000'}
                </Typography>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
      <Card
        sx={{
          backgroundColor: 'white',
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          mt: 4,
          width: '100%',
        }}
      >
        <CardContent sx={{p: {xs: 1, sm: 2}}}>
          <Box sx={{maxWidth: 600, mx: 'auto', mt: 5, textAlign: 'center'}}>
            <Typography variant="h5" sx={{mb: 3}}>
              If you're facing any issue, create a support ticket and our team
              will assist you.
            </Typography>

            <Button
              variant="contained"
              onClick={() => {
                setOpen(true);
                setSuccess('');
              }}
            >
              Create Ticket
            </Button>

            <Dialog
              open={open}
              onClose={() => setOpen(false)}
              fullWidth
              maxWidth="sm"
            >
              <DialogTitle>
                Create Support Ticket
                <IconButton
                  onClick={() => setOpen(false)}
                  sx={{position: 'absolute', right: 8, top: 8}}
                >
                  <Close />
                </IconButton>
              </DialogTitle>

              <form onSubmit={formik.handleSubmit}>
                <DialogContent dividers>
                  {success && (
                    <Alert severity="success" sx={{mb: 2}}>
                      {success}
                    </Alert>
                  )}

                  <TextField
                    fullWidth
                    placeholder="Title"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.title && Boolean(formik.errors.title)}
                    helperText={formik.touched.title && formik.errors.title}
                    sx={{mb: 2}}
                  />

                  <TextField
                    fullWidth
                    placeholder="Summary"
                    multiline
                    rows={4}
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.description &&
                      Boolean(formik.errors.description)
                    }
                    helperText={
                      formik.touched.description && formik.errors.description
                    }
                    sx={{mb: 2}}
                  />

                  <Select
                    fullWidth
                    name="category"
                    value={formik.values.category}
                    onChange={formik.handleChange}
                    displayEmpty
                    sx={{mb: 2}}
                  >
                    <MenuItem value="" disabled>
                      Category
                    </MenuItem>
                    {Object.entries(TicketCategory).map(([key, value]) => (
                      <MenuItem key={key} value={value}>
                        {value}
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.category && formik.errors.category && (
                    <Typography variant="body2" color="error" sx={{mt: 1}}>
                      {formik.errors.category}
                    </Typography>
                  )}

                  <Button
                    variant="outlined"
                    component="label"
                    fullWidth
                    sx={{mb: 2}}
                  >
                    Upload Screenshots
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      multiple
                      onChange={e => {
                        const files = e.currentTarget.files
                          ? Array.from(e.currentTarget.files)
                          : [];
                        formik.setFieldValue('attachments', [
                          ...formik.values.attachments,
                          ...files,
                        ]);
                      }}
                    />
                  </Button>

                  <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                    {filePreviews.map((url, index) => (
                      <Box
                        key={index}
                        position="relative"
                        width={64}
                        height={64}
                        sx={{cursor: 'pointer'}}
                      >
                        <img
                          src={url}
                          alt={`preview-${index}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            borderRadius: 4,
                          }}
                        />
                        <IconButton
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            bgcolor: 'white',
                          }}
                          onClick={() => {
                            const newFiles = [...formik.values.attachments];
                            newFiles.splice(index, 1);
                            formik.setFieldValue('attachments', newFiles);
                          }}
                        >
                          <Trash fontSize="small" />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                </DialogContent>

                <DialogActions>
                  <Button onClick={() => setOpen(false)} color="inherit">
                    Cancel
                  </Button>
                  <Button type="submit" variant="contained" disabled={creating}>
                    {creating ? 'Submitting...' : 'Submit Ticket'}
                  </Button>
                </DialogActions>
              </form>
            </Dialog>
          </Box>
        </CardContent>
      </Card>
    </Grid>
  );
}

export default CustomerServicePage;
