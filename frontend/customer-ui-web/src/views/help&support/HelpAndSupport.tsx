'use client';
import {
  Typo<PERSON>,
  <PERSON>,
  Card,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
} from '@mui/material';

import {ArrowDown2} from 'iconsax-react';
import draftToHtml from 'draftjs-to-html';
import {useGetHelpQuery} from 'redux/help/helpApiSlice';

function HelpAndSupportPage() {
  const {data: faqs, isLoading} = useGetHelpQuery();

  return (
    <Grid
      container
      sx={{
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Card
        sx={{
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          width: '100%',
          maxWidth: '100%',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 'bold',
            color: '#4A4A4A',
            p: 1,
          }}
        >
          What issue are you facing?
        </Typography>

        {isLoading ? (
          <Box sx={{width: '100%', textAlign: 'center', py: 4}}>
            <Typography variant="body1" sx={{mb: 2}}>
              Loading FAQs...
            </Typography>
            <CircularProgress size={24} />
          </Box>
        ) : (
          faqs?.map((faq, index) => {
            const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
            const htmlAnswer = (() => {
              if (typeof faq.answer === 'string') {
                if (isHtml(faq.answer)) {
                  return faq.answer;
                } else {
                  try {
                    const raw = JSON.parse(faq.answer);
                    return draftToHtml(raw);
                  } catch (e) {
                    // eslint-disable-next-line no-console
                    console.log(e);
                    return faq.answer;
                  }
                }
              } else {
                return draftToHtml(faq.answer);
              }
            })();
            return (
              <Accordion
                key={index}
                sx={{
                  borderRadius: '12px',
                  mt: 2,
                }}
              >
                <AccordionSummary
                  expandIcon={<ArrowDown2 />}
                  sx={{
                    fontSize: '16px',
                    fontWeight: 400,
                    padding: '12px 16px',
                    justifyContent: 'space-between',
                  }}
                >
                  {faq.question}
                </AccordionSummary>
                <AccordionDetails
                  sx={{bgcolor: '#f1f1f1', borderRadius: '12px'}}
                >
                  <div dangerouslySetInnerHTML={{__html: htmlAnswer}} />
                </AccordionDetails>
              </Accordion>
            );
          })
        )}
      </Card>
    </Grid>
  );
}

export default HelpAndSupportPage;
