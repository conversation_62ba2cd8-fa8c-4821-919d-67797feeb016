'use client';
import {
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON>con<PERSON>utton,
  Card,
  Card<PERSON>ontent,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Select,
} from '@mui/material';
import {
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  MenuItem,
  DialogActions,
  Alert,
} from '@mui/material';
import {useEffect, useState} from 'react';
import {Close} from '@mui/icons-material';
import * as Yup from 'yup';
import {ArrowDown2, Trash} from 'iconsax-react';
import draftToHtml from 'draftjs-to-html';
import {useFormik} from 'formik';
import {
  useCreateSupportTicketMutation,
  useGetHelpQuery,
} from 'redux/help/helpApiSlice';
import {SupportStatus, TicketCategory} from 'enums/help.enum';
import {CreateTicketDto} from 'types/help';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';

function HelpAndSupportPage() {
  const {data: faqs, isLoading} = useGetHelpQuery();
  const [open, setOpen] = useState(false);
  const [success, setSuccess] = useState('');
  const {data: user} = useGetUserQuery();
  const [filePreviews, setFilePreviews] = useState<string[]>([]);

  const [createSupportTicket, {isLoading: creating}] =
    useCreateSupportTicketMutation();
  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      category: '',
      attachments: [] as File[],
      priority: 1,
    },
    validationSchema: Yup.object({
      title: Yup.string().required('Title is required'),
      description: Yup.string().required('Summary is required'),
      category: Yup.string().required('Category is required'),
    }),
    onSubmit: async (values, {resetForm}) => {
      const baseImages = await Promise.all(
        values.attachments.map(
          file =>
            new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.readAsDataURL(file);
              reader.onload = () => resolve(reader.result as string);
              reader.onerror = error => reject(error);
            }),
        ),
      );

      const payload: CreateTicketDto = {
        title: values.title,
        description: values.description,
        category: values.category as TicketCategory,
        attachments: baseImages[0] ?? undefined,
        priority: Number(values.priority),
        status: SupportStatus.Open,
        assignee: user?.id ?? '',
      };

      await createSupportTicket(payload).unwrap();
      openSnackbar({
        open: true,
        message: `Ticket submitted successfully!`,
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);

      resetForm();
      setOpen(false);

      setSuccess('');
    },
  });

  useEffect(() => {
    const urls = formik.values.attachments.map(file =>
      URL.createObjectURL(file),
    );
    setFilePreviews(urls);
    return () => urls.forEach(url => URL.revokeObjectURL(url));
  }, [formik.values.attachments]);
  return (
    <Grid
      container
      sx={{
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Card
        sx={{
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          width: '100%',
          maxWidth: '100%',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 'bold',
            color: '#4A4A4A',
            p: 1,
          }}
        >
          What issue are you facing?
        </Typography>

        {isLoading ? (
          <Box sx={{width: '100%', textAlign: 'center', py: 4}}>
            <Typography variant="body1" sx={{mb: 2}}>
              Loading FAQs...
            </Typography>
            <CircularProgress size={24} />
          </Box>
        ) : (
          faqs?.map((faq, index) => {
            const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
            const htmlAnswer = (() => {
              if (typeof faq.answer === 'string') {
                if (isHtml(faq.answer)) {
                  return faq.answer;
                } else {
                  try {
                    const raw = JSON.parse(faq.answer);
                    return draftToHtml(raw);
                  } catch (e) {
                    // eslint-disable-next-line no-console
                    console.log(e);
                    return faq.answer;
                  }
                }
              } else {
                return draftToHtml(faq.answer);
              }
            })();
            return (
              <Accordion
                key={index}
                sx={{
                  borderRadius: '12px',
                  mt: 2,
                }}
              >
                <AccordionSummary
                  expandIcon={<ArrowDown2 />}
                  sx={{
                    fontSize: '16px',
                    fontWeight: 400,
                    padding: '12px 16px',
                    justifyContent: 'space-between',
                  }}
                >
                  {faq.question}
                </AccordionSummary>
                <AccordionDetails
                  sx={{bgcolor: '#f1f1f1', borderRadius: '12px'}}
                >
                  <div dangerouslySetInnerHTML={{__html: htmlAnswer}} />
                </AccordionDetails>
              </Accordion>
            );
          })
        )}
      </Card>

      <Card
        sx={{
          backgroundColor: 'white',
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          mt: 4,
          width: '100%',
        }}
      >
        <CardContent sx={{p: {xs: 1, sm: 2}}}>
          <Box sx={{maxWidth: 600, mx: 'auto', mt: 5, textAlign: 'center'}}>
            <Typography variant="h5" sx={{mb: 3}}>
              If you're facing any issue, create a support ticket and our team
              will assist you.
            </Typography>

            <Button
              variant="contained"
              onClick={() => {
                setOpen(true);
                setSuccess('');
              }}
            >
              Create Ticket
            </Button>

            <Dialog
              open={open}
              onClose={() => setOpen(false)}
              fullWidth
              maxWidth="sm"
            >
              <DialogTitle>
                Create Support Ticket
                <IconButton
                  onClick={() => setOpen(false)}
                  sx={{position: 'absolute', right: 8, top: 8}}
                >
                  <Close />
                </IconButton>
              </DialogTitle>

              <form onSubmit={formik.handleSubmit}>
                <DialogContent dividers>
                  {success && (
                    <Alert severity="success" sx={{mb: 2}}>
                      {success}
                    </Alert>
                  )}

                  <TextField
                    fullWidth
                    placeholder="Title"
                    name="title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.title && Boolean(formik.errors.title)}
                    helperText={formik.touched.title && formik.errors.title}
                    sx={{mb: 2}}
                  />

                  <TextField
                    fullWidth
                    placeholder="Summary"
                    multiline
                    rows={4}
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.description &&
                      Boolean(formik.errors.description)
                    }
                    helperText={
                      formik.touched.description && formik.errors.description
                    }
                    sx={{mb: 2}}
                  />

                  <Select
                    fullWidth
                    name="category"
                    value={formik.values.category}
                    onChange={formik.handleChange}
                    displayEmpty
                    sx={{mb: 2}}
                  >
                    <MenuItem value="" disabled>
                      Category
                    </MenuItem>
                    {Object.entries(TicketCategory).map(([key, value]) => (
                      <MenuItem key={key} value={value}>
                        {value}
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.category && formik.errors.category && (
                    <Typography variant="body2" color="error" sx={{mt: 1}}>
                      {formik.errors.category}
                    </Typography>
                  )}

                  <Button
                    variant="outlined"
                    component="label"
                    fullWidth
                    sx={{mb: 2}}
                  >
                    Upload Screenshots
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      multiple
                      onChange={e => {
                        const files = e.currentTarget.files
                          ? Array.from(e.currentTarget.files)
                          : [];
                        formik.setFieldValue('attachments', [
                          ...formik.values.attachments,
                          ...files,
                        ]);
                      }}
                    />
                  </Button>

                  <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                    {filePreviews.map((url, index) => (
                      <Box
                        key={index}
                        position="relative"
                        width={64}
                        height={64}
                        sx={{cursor: 'pointer'}}
                      >
                        <img
                          src={url}
                          alt={`preview-${index}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            borderRadius: 4,
                          }}
                        />
                        <IconButton
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            bgcolor: 'white',
                          }}
                          onClick={() => {
                            const newFiles = [...formik.values.attachments];
                            newFiles.splice(index, 1);
                            formik.setFieldValue('attachments', newFiles);
                          }}
                        >
                          <Trash fontSize="small" />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                </DialogContent>

                <DialogActions>
                  <Button onClick={() => setOpen(false)} color="inherit">
                    Cancel
                  </Button>
                  <Button type="submit" variant="contained" disabled={creating}>
                    {creating ? 'Submitting...' : 'Submit Ticket'}
                  </Button>
                </DialogActions>
              </form>
            </Dialog>
          </Box>
        </CardContent>
      </Card>
    </Grid>
  );
}

export default HelpAndSupportPage;
