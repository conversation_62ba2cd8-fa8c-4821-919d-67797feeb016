'use client';
import {
  Typo<PERSON>,
  <PERSON>,
  IconButton,
  Card,
  CardContent,
  Link,
  Grid,
} from '@mui/material';

import {Whatsapp} from 'iconsax-react';
import {useGetSupportContactQuery} from 'redux/help/helpApiSlice';
function CustomerServicePage() {
  const {data: contactList = [], isLoading} = useGetSupportContactQuery();
  const contact = contactList[0];

  return (
    <Grid
      container
      sx={{
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Typography
        variant="h4"
        fontWeight={600}
        textAlign="center"
        sx={{mb: 1, fontSize: {xs: '20px', sm: '28px'}}}
      >
        Customer Service
      </Typography>

      <Typography
        variant="body1"
        textAlign="center"
        color="text.secondary"
        sx={{
          maxWidth: 600,
          mx: 'auto',
          mb: 4,
          fontSize: {xs: '14px', sm: '16px'},
        }}
      >
        Have a question or need help with your orders or account? Our support
        team is here to assist you. Feel free to reach out through the following
        channels.
      </Typography>
      <Card
        sx={{
          backgroundColor: 'white',
          borderRadius: 3,
          p: 2,
          boxShadow: 'none',
          //   mt: 20,
          width: '100%',
        }}
      >
        <CardContent sx={{p: {xs: 1, sm: 2}}}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: {xs: 1, sm: 2},
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: {xs: 0.5, sm: 1},
                  flexWrap: 'wrap',
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 'bold',
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                  }}
                >
                  Mail us :
                </Typography>
                <Link
                  href={`mailto:${contact?.supportEmail}`}
                  underline="none"
                  sx={{
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                    fontWeight: 400,
                    wordBreak: 'break-word',
                  }}
                >
                  {contact?.supportEmail ?? 'N/A'}
                </Link>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: {xs: 0.5, sm: 1},
                  flexWrap: 'wrap',
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 'bold',
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                  }}
                >
                  Contact Through :
                </Typography>
                <IconButton
                  href={`https://wa.me/${contact?.supportPhone?.replace(/\D/g, '')}`}
                  target="_blank"
                  aria-label="WhatsApp"
                  sx={{
                    backgroundColor: '#25D366',
                    width: {xs: 24, sm: 28},
                    height: {xs: 24, sm: 28},
                    borderRadius: '50%',
                    '&:hover': {backgroundColor: '#1EBE5D'},
                  }}
                >
                  <Whatsapp size="18" color="#FFF" />
                </IconButton>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#4A4A4A',
                    fontSize: {xs: '14px', sm: '16px'},
                    fontWeight: 400,
                    wordBreak: 'break-word',
                  }}
                >
                  {contact?.supportPhone ?? '+91 0000000000'}
                </Typography>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    </Grid>
  );
}

export default CustomerServicePage;
