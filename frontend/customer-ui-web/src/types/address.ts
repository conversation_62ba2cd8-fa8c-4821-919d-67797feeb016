import {Status} from 'enums/customer.enum';

interface User {
  firstName: string;
  lastName: string;
  email: string;
}

interface UserTenant {
  id: string;
  user: User;
}

export interface CustomerDto {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  userTenantId?: string;
  customerId?: string;
  status?: string;
  addresses?: AddressDto[];
}
export interface AddressDto {
  id?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  addressType?: string;
  customerId?: string;
  locality: string;
  name: string;
  phoneNumber: string;
  landmark?: string;
  alternativePhoneNumber?: string;
}

export interface Customer {
  id: string;
  customerId?: string;
  status?: Status.ACTIVE;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  createdOn: string;
  createdBy?: string;
  modifiedOn: string;
  modifiedBy?: string;
  deleted: boolean;
  deletedOn?: string | null;
  deletedBy?: string | null;
  userTenant: UserTenant;
  addresses?: AddressDto[];
}
