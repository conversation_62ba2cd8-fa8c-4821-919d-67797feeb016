import {
  CardStyle,
  PageType,
  SectionItemType,
  SectionType,
} from 'enums/pageSection.enum';
import {Collection, ProductVariant} from './product';

export interface SectionItemMetadata {
  redirectUrl?: string;
  productIds?: string[];
  maxItems?: number;
  viewAllLink?: string;
  categoryId?: string;
  showRating?: boolean;
  showPrice?: boolean;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  richText?: string;
  filter?: FilterCondition[];
  facetValueIds?: string[];
}

export interface PageSection {
  id: string;
  type: SectionType;
  title: string;
  metadata?: object;
  cardStyle?: CardStyle;
  pageType: PageType;
  displayOrder: number;
  isActive: boolean;
  sectionItems?: SectionItem[];
}

export interface SectionItem {
  id: string;
  entityType: SectionItemType;
  entityId?: string;
  imageUrl?: string;
  previewUrl?: string;
  title: string;
  subtitle?: string;
  metadata?: SectionItemMetadata;
  displayOrder: number;
  pageSectionId: string;
  pageSection?: PageSection;
  productVariants?: ProductVariant[];
  collection?: Collection;
}

export interface FilterCondition {
  field: 'price' | 'discountPercent';
  operator: 'eq' | 'gt' | 'lt' | 'between';
  value: string | number | [string | number, string | number];
}
