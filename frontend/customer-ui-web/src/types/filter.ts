export interface FilterValue {
  label: string;
  value: string;
  productVariantIds: string[];
}
export interface FilterGroup {
  label: string;
  isFacet: boolean;
  values: FilterValue[];
}
export interface IFilter {
  limit?: number;
  skip?: number;
  order?: Array<Record<string, unknown> | string>;
  where?: Record<string, unknown>;
  fields?: Record<string, boolean>;
  include?: Array<Record<string, unknown> | string>;
}

export interface IFilterWithKeyword extends IFilter {
  keyword?: string;
  facetValueIds?: string[];
  collectionIds?: string[];
}
