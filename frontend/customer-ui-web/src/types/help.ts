import {SupportStatus, TicketCategory} from 'enums/help.enum';

export interface CreateTicketDto {
  category: TicketCategory;
  title: string;
  description: string;
  attachments?: string;
  priority?: number;
  status: SupportStatus;
  assignee?: string;
}
export interface HelpItem {
  question: string;
  answer: string;
  category: string;
  visibility: number;
  status: number;
}
export interface SupportContactInfo {
  id: string;
  supportEmail: string;
  supportPhone: string;
  status: number;
  visibility: number;
}
