import {ApiSliceIdentifier} from 'enums/api.enum';
import {Category} from 'enums/terms.enum';
import {apiSlice} from 'redux/apiSlice';
import {LegalVisibility} from 'types/legal-category.enum';
import {LegalType} from 'types/termsandprivacy';

export const privacyApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPrivacyAndPolicy: builder.query<LegalType[], string>({
      query: code => ({
        url: '/legals',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              category: {inq: [Category.CUSTOMER]},
              visibility: {
                inq: [LegalVisibility.CUSTOMER, LegalVisibility.ALL],
              },
            },
          }),
        },
        headers: {
          Authorization: `Bearer ${code}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),
  }),
});

export const {useGetPrivacyAndPolicyQuery} = privacyApiSlice;
