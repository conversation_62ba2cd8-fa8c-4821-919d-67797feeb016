import {ApiSliceIdentifier} from 'enums/api.enum';
import {Category} from 'enums/terms.enum';
import {apiSlice} from 'redux/apiSlice';
import {Privacy} from 'types/termsandprivacy';

export const privacyApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPrivacyAndPolicy: builder.query<Privacy[], string>({
      query: code => ({
        url: '/privacy-policies',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              category: {inq: [Category.CUSTOMER]},
            },
          }),
        },
        headers: {
          Authorization: `Bearer ${code}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),
  }),
});

export const {useGetPrivacyAndPolicyQuery} = privacyApiSlice;
