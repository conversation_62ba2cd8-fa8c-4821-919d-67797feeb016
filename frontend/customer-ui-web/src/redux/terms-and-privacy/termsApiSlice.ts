import {ApiSliceIdentifier} from 'enums/api.enum';
import {Category} from 'enums/terms.enum';
import {apiSlice} from 'redux/apiSlice';
import {TokenResponse} from 'types/auth';
import {Terms} from 'types/termsandprivacy';

export const termsApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTermsAndCondition: builder.query<Terms[], string>({
      query: code => ({
        url: '/terms-and-conditions',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              category: {inq: [Category.CUSTOMER]},
            },
          }),
        },
        headers: {
          Authorization: `Bearer ${code}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),

    getGuestToken: builder.mutation<TokenResponse, void>({
      query: () => ({
        url: '/auth/guest',
        method: 'POST',

        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {useGetTermsAndConditionQuery, useGetGuestTokenMutation} =
  termsApiSlice;
