import {ApiSliceIdentifier} from 'enums/api.enum';
import {Category} from 'enums/terms.enum';
import {apiSlice} from 'redux/apiSlice';
import {TokenResponse} from 'types/auth';
import {LegalVisibility} from 'types/legal-category.enum';
import {LegalType} from 'types/termsandprivacy';

export const termsApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTermsAndCondition: builder.query<LegalType[], string>({
      query: code => ({
        url: '/legals',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              category: {inq: [Category.CUSTOMER]},
              visibility: {
                inq: [LegalVisibility.CUSTOMER, LegalVisibility.ALL],
              },
            },
          }),
        },
        headers: {
          Authorization: `Bearer ${code}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),

    getGuestToken: builder.mutation<TokenResponse, void>({
      query: () => ({
        url: '/auth/guest',
        method: 'POST',

        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {useGetTermsAndConditionQuery, useGetGuestTokenMutation} =
  termsApiSlice;
