import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from 'redux/apiSlice';
import {CreateTicketDto, HelpItem, SupportContactInfo} from 'types/help';
import {LegalVisibility} from 'types/legal-category.enum';

export const supportApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    createSupportTicket: builder.mutation<void, CreateTicketDto>({
      query: formData => ({
        url: '/tickets',
        method: 'POST',
        body: formData,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getHelp: builder.query<HelpItem[], void>({
      query: () => ({
        url: `/helps`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {
                inq: [LegalVisibility.CUSTOMER, LegalVisibility.ALL],
              },
            },
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getSupportContact: builder.query<SupportContactInfo[], void>({
      query: () => ({
        url: `/support`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {
                inq: [LegalVisibility.CUSTOMER, LegalVisibility.ALL],
              },
            },
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useCreateSupportTicketMutation,
  useGetHelpQuery,
  useGetSupportContactQuery,
} = supportApiSlice;
