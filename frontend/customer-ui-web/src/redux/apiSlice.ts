import type {
  BaseQueryApi,
  BaseQueryFn,
  <PERSON>tchA<PERSON><PERSON>,
  FetchBaseQueryError,
  FetchBaseQueryMeta,
} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {
  AuthResData,
  setCredentials,
  setGuestToken,
  unsetCredentials,
  unsetGuestToken,
} from './auth/authSlice';
import {getBaseUrl} from './redux.helper';
import type {RootState} from './store';
import {ApiTagTypes} from './types';
import {ApiSliceIdentifier} from 'enums/api.enum';
import {useApiErrorHandler} from '../hooks/useApiErrorHandler';
import {ApiRetryData, setRetryArgs} from './retry/retrySlice';

export interface Args {
  url: string;
  method?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  body?: any;
  apiSliceIdentifier?: ApiSliceIdentifier;
}

const RESULT_ERROR_STATUS = 401;
const FORBIDDEN_ERROR_STATUS = 403;
const authEndpoints = [
  'verifyOtp',
  'login',
  'sendOtp',
  'signUp',
  'createSignUpToken',
  'updatePassword',
];
interface IFetchArgs {
  url: string;
  method?: string;
  body?: any;
  apiSliceIdentifier?: ApiSliceIdentifier;
  isFormData?: boolean;
}

const getBaseQuery = (baseUrl: string) => {
  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers, {getState}) {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('x-origin', 'ecomdukes-customer');

      return headers;
    },
  });
  return baseQuery;
};

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
// const RESULT_ERROR_STATUS = 401;
const baseQueryWithReauth: BaseQueryFn<
  IFetchArgs,
  // {
  //   url: string;
  //   method?: string;
  //   // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //   body?: any;
  //   apiSliceIdentifier?: ApiSliceIdentifier;
  // },
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;

  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);
  const handleError = useApiErrorHandler();

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers, {getState}) {
      const state = getState() as RootState;
      const token = state.auth.accessToken ?? state.auth.guestToken;

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('x-origin', 'ecomdukes-customer');

      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);
  if (result?.error?.status === FORBIDDEN_ERROR_STATUS) {
    api.dispatch(unsetCredentials());
    api.dispatch(unsetGuestToken());
    api.dispatch(
      setRetryArgs({baseUrl: baseUrl as string, args, extraOptions, api}),
    );
  }

  if (
    result.error?.status === RESULT_ERROR_STATUS &&
    !authEndpoints.includes(api.endpoint)
  ) {
    const refreshToken = localStorage.getItem('refreshToken');

    if (!refreshToken) {
      localStorage.removeItem('guestToken');
      // No refresh token: try guest token flow
      return await handleGuestTokenFlow(
        baseQuery,
        args,
        api,
        extraOptions,
        state,
      );
    }

    // Try refreshing access token
    // try to get a new token
    const refreshResult = await baseQuery(
      {
        url: getBaseUrl(state) + '/auth/token-refresh',
        method: 'POST',
        // body: {refreshToken: (api.getState() as RootState).auth.refreshToken},
        body: {refreshToken},
      },
      api,
      extraOptions,
    );
    if (refreshResult.data) {
      api.dispatch(setCredentials(refreshResult.data as AuthResData));
      return await baseQuery(args, api, extraOptions);
    }

    // Refresh failed: clear credentials and fallback to guest token
    api.dispatch(unsetCredentials());
    return await handleGuestTokenFlow(
      baseQuery,
      args,
      api,
      extraOptions,
      state,
    );
  }

  if (result?.error) {
    handleError(result.error);
  }
  return result;
};

const handleGuestTokenFlow = async (
  baseQuery: BaseQueryFn<
    string | FetchArgs,
    unknown,
    FetchBaseQueryError,
    {},
    FetchBaseQueryMeta
  >,
  args: IFetchArgs,
  api: BaseQueryApi,
  extraOptions: any,
  state: RootState,
) => {
  const guestToken = localStorage.getItem('guestToken');

  if (guestToken) {
    return await baseQuery(args, api, extraOptions);
  }

  const guestTokenResponse = await baseQuery(
    {
      url: getBaseUrl(state, args.apiSliceIdentifier) + '/auth/guest',
      method: 'POST',
    },
    api,
    extraOptions,
  );

  if (guestTokenResponse.data) {
    const newGuestToken = guestTokenResponse.data as AuthResData;
    localStorage.setItem('guestToken', newGuestToken.accessToken);
    api.dispatch(
      setGuestToken({
        accessToken: newGuestToken.accessToken,
        expires: newGuestToken.expires,
        refreshToken: '',
      }),
    );
    return await baseQuery(args, api, extraOptions);
  } else {
    return guestTokenResponse;
  }
};
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
  tagTypes: Object.values(ApiTagTypes),
});

export const retryApi = async (params: ApiRetryData) => {
  const {baseUrl, args, api, extraOptions} = params;
  if (!args || !baseUrl || !api) return;
  const baseQuery = getBaseQuery(baseUrl as string);
  let result = await baseQuery(args, api, extraOptions ?? {});
  if (result?.error) {
    useApiErrorHandler();
  }
  return result;
};
