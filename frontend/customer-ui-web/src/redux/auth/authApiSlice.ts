import {apiSlice} from '../apiSlice';
import {ChangePasswordRequest, SignupDto, TokenResponse} from 'types/auth';
import {ILoginForm} from './types';
import {User} from 'types/user-profile';
import {ApiTagTypes} from '../../redux/types';
import {ApiSliceIdentifier} from 'enums/api.enum';
import {Status} from 'enums/customer.enum';

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    login: builder.mutation({
      query: (body: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {
          ...body,
          client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET,
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    exchangeToken: builder.mutation<
      TokenResponse,
      {code: string; clientId?: string}
    >({
      query: ({code, clientId}) => ({
        url: '/auth/token',
        method: 'POST',
        body: {
          clientId: clientId ?? process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          code,
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        headers: {
          Authorization: `Bearer ${code}`,
        },
      }),
    }),
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/logout',
        method: 'POST',
        body: {refreshToken},
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    createSignUpToken: builder.mutation<{code: string}, string>({
      query: (email: string) => ({
        url: '/auth/sign-up/create-token',
        method: 'POST',
        cache: 'no-cache',
        body: {
          email,
          data: {
            client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
            client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET,
          },
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    signUp: builder.mutation({
      query: ({token, signupData}: {token: string; signupData: SignupDto}) => ({
        url: '/auth/sign-up/create-user',
        method: 'POST',
        body: signupData,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    getUser: builder.query<User, void>({
      query: () => ({
        url: '/auth/me',
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        providesTags: [ApiTagTypes.User],
      }),
    }),
    updateUser: builder.mutation<User, {id: string; data: Partial<User>}>({
      query: ({id, data}) => ({
        url: `/profile/${id}`,
        method: 'PATCH',
        body: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          gender: data.gender,
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    createGuestToken: builder.mutation<TokenResponse, void>({
      query: () => ({
        url: '/auth/guest',
        method: 'POST',

        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    updatePassword: builder.mutation<
      void,
      Omit<ChangePasswordRequest, 'refreshToken'>
    >({
      query: body => {
        const refreshToken = localStorage.getItem('refreshToken');
        return {
          url: '/auth/change-password',
          method: 'PATCH',
          body: {
            ...body,
            refreshToken,
          },
          apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        };
      },
    }),
    updateCustomerStatus: builder.mutation<void, {id: string; status: Status}>({
      query: ({id, status}) => ({
        url: `/customers/${id}/status`,
        method: 'PATCH',
        body: {
          status,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      invalidatesTags: [ApiTagTypes.User],
    }),
  }),
});

export const {
  useGetUserQuery,
  useUpdatePasswordMutation,
  useLoginMutation,
  useExchangeTokenMutation,
  useLogoutMutation,
  useCreateSignUpTokenMutation,
  useSignUpMutation,
  useCreateGuestTokenMutation,
  useUpdateUserMutation,
  useUpdateCustomerStatusMutation,
} = authApiSlice;
