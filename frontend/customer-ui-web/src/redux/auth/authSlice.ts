/* eslint-disable camelcase */
import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {RootState} from 'redux/store';

export interface AuthData {
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
  guestToken: string | null;
  authNotice: string | null;
}

export interface AuthResData {
  accessToken: string;
  expires: number;
  refreshToken: string;
  token_type?: string;
  'not-before-policy'?: number;
  session_state?: string;
  scope?: string;
}

export interface AuthState extends AuthData {
  isLoggedIn: boolean;
  authNotice: string | null;
}

const initialState: AuthState = {
  accessToken:
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null,
  refreshToken:
    typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null,
  expires: null,
  guestToken:
    typeof window !== 'undefined'
      ? (localStorage.getItem('guestToken') ?? '')
      : '',
  isLoggedIn:
    typeof window !== 'undefined'
      ? !!localStorage.getItem('accessToken')
      : false,
  authNotice: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const {accessToken, refreshToken, expires} = action.payload;
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      state.expires = expires;
      state.isLoggedIn = true;
    },
    unsetCredentials: state => {
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
    },
    setGuestToken: (state, action: PayloadAction<AuthResData>) => {
      const {accessToken, expires} = action.payload;
      state.guestToken = accessToken;
      state.expires = expires;
      state.isLoggedIn = false;
      localStorage.setItem('guestToken', accessToken);
    },
    unsetGuestToken: state => {
      //setting redux state
      localStorage.removeItem('guestToken');
      //setting redux state
      state.guestToken = null;
      state.expires = null;
    },
    setAuthNotice: (state, action: PayloadAction<string>) => {
      state.authNotice = action.payload;
    },
    clearAuthNotice: state => {
      state.authNotice = null;
    },
  },
});

export const {
  setCredentials,
  unsetCredentials,
  setGuestToken,
  unsetGuestToken,
  setAuthNotice,
  clearAuthNotice,
} = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) =>
  Boolean(state.auth.isLoggedIn || state.auth.guestToken);
export const selectCurrentAccessToken = (state: RootState) =>
  state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) =>
  state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
export const selectCurrentAuthNotice = (state: RootState) =>
  state.auth.authNotice;
