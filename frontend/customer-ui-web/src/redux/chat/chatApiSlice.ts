import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from '../apiSlice';
import {ChatDto, ChatMessage} from 'types/chat';
import {Seller} from 'types/seller';

export const chatApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getChatMessages: builder.query<ChatMessage[], string>({
      query: chatId => ({
        url: `/chats/${chatId}/chat-messages`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            order: ['created_on ASC'],
          }),
        },
      }),
    }),
    createChat: builder.mutation<ChatDto, {sellerId: string}>({
      query: ({sellerId}) => ({
        url: `/chats`,
        method: 'POST',
        body: {sellerId},
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getUserChats: builder.query<ChatDto[], {search?: string}>({
      query: ({search}) => ({
        url: `/chats/my`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'seller',
                scope: {
                  include: [
                    {
                      relation: 'userTenant',
                      scope: {
                        include: ['user'],
                      },
                    },
                  ],
                },
              },
            ],
          }),
          ...(search ? {search} : {}),
        },
      }),
    }),

    getSellerById: builder.query<
      Seller,
      {id: string; include?: Array<Record<string, unknown> | string>}
    >({
      query: ({id, include}) => ({
        url: `/sellers/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: include || [],
          }),
        },
      }),
    }),
    sendChatMessage: builder.mutation<
      ChatMessage,
      {chatId: string; message: {message: string}}
    >({
      query: ({chatId, message}) => ({
        url: `/chat-messages`,
        method: 'POST',
        body: {
          ...message,
          chatId,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useGetChatMessagesQuery,
  useGetUserChatsQuery,
  useCreateChatMutation,
  useGetSellerByIdQuery,
  useSendChatMessageMutation,
} = chatApiSlice;
