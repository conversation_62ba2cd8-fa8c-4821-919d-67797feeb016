import {ApiSliceIdentifier} from 'enums/api.enum';
import {buildFilterParams, Count, IFilter} from 'types/api';
import {apiSlice} from 'redux/apiSlice';
import {ProductVariant} from 'types/product';

export const productVariantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getProductVariants: builder.query<ProductVariant[], IFilter | void>({
      query: filter => ({
        url: '/product-variants',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined),
        },
      }),
    }),

    getProductVariantsCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({where, include}) => ({
        url: '/product-variants/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include,
          }),
        },
      }),
    }),
  }),
});

export const {
  useGetProductVariantsQuery,
  useGetProductVariantsCountQuery,
  useLazyGetProductVariantsQuery,
  useLazyGetProductVariantsCountQuery,
} = productVariantApiSlice;
