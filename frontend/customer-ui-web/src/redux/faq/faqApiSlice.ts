import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from '../apiSlice';
import {FaqVisibility} from 'enums/faq.enum';
import {Faq} from 'types/faq';

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getFaqs: builder.query<Faq[], void>({
      query: () => ({
        url: `/faqs`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {inq: [FaqVisibility.CUSTOMER, FaqVisibility.ALL]},
            },
            order: ['priority DESC'],
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {useGetFaqsQuery} = authApiSlice;
