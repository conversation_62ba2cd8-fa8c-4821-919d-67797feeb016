import * as Yup from 'yup';

export const addressValidationSchema = Yup.object({
  name: Yup.string().required('Name is required'),
  zipCode: Yup.string()
    .matches(/^\d{5,6}$/, 'Pincode must be 5 or 6 digits')
    .required('Pincode is required'),
  phoneNumber: Yup.string()
    .matches(/^\d{10}$/, 'Enter a valid 10-digit mobile number')
    .required('Phone is required'),

  locality: Yup.string(),
  addressLine1: Yup.string().required('Address is required'),
  addressLine2: Yup.string(),
  city: Yup.string().required('City is required'),
  state: Yup.string().required('State is required'),
  country: Yup.string().required('Country is required'),
  addressType: Yup.string().required('Address type is required'),
  alternativePhoneNumber: Yup.string()
    .matches(/^\d{10}$/, 'Alternate phone must be 10 digits')
    .notRequired(),
  landmark: Yup.string().notRequired(),
});
