import * as Yup from 'yup';

export const profileFormValidation = Yup.object().shape({
  firstName: Yup.string()
    .trim()
    .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed')
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('First Name is required'),

  lastName: Yup.string()
    .trim()
    .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed')
    .max(255, 'Too Long!')
    .required('Last Name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phoneNumber: Yup.string()
    .min(10, 'Please enter a valid phone number')
    .max(10, 'Please enter a valid phone number')
    .required('Phone number is required'),
  gender: Yup.string(),
});
