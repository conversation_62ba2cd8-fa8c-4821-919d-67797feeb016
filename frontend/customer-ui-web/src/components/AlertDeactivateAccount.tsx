// material-ui
import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import DialogContent from '@mui/material/DialogContent';

// project import
import Avatar from 'components/@extended/Avatar';
import { PopupTransition } from 'components/@extended/Transitions';

import { ThemeMode } from 'config';
import { openSnackbar } from 'api/snackbar';

// assets
import { ProfileDelete } from 'iconsax-react';

// types
import { SnackbarProps } from 'types/snackbar';
import { Status } from 'enums/customer.enum';
import { useUpdateCustomerStatusMutation } from 'redux/auth/authApiSlice';
import { useAppDispatch } from 'redux/hooks';
import { unsetCredentials } from 'redux/auth/authSlice';
import { apiSlice } from 'redux/apiSlice';
import { ApiTagTypes } from 'redux/types';
import { useRouter } from 'next/navigation';

interface Props {
  id: string;
  open: boolean;
  handleClose: () => void;
}

// ==============================|| CUSTOMER - DEACTIVATE ACCOUNT ||============================== //

export default function AlertDeactivateAccount({ id, open, handleClose }: Props) {
  const theme = useTheme();
  const [updateCustomerStatus] = useUpdateCustomerStatusMutation();
  const dispatch = useAppDispatch();
  const router = useRouter();

  const deactivateHandler = async () => {
    try {
      await updateCustomerStatus({ id, status: Status.INACTIVE }).unwrap();

      openSnackbar({
        open: true,
        message: 'Account deactivated successfully',
        anchorOrigin: { vertical: 'top', horizontal: 'right' },
        variant: 'alert',
        alert: {
          color: 'success'
        }
      } as SnackbarProps);
      
      handleClose();
      dispatch(unsetCredentials());
      dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
      router.push('/login');
    } catch (error) {
      openSnackbar({
        open: true,
        message: 'Failed to deactivate account. Please try again.',
        anchorOrigin: { vertical: 'top', horizontal: 'right' },
        variant: 'alert',
        alert: {
          color: 'error'
        }
      } as SnackbarProps);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      keepMounted
      TransitionComponent={PopupTransition}
      maxWidth="xs"
      aria-labelledby="deactivate-account-title"
      aria-describedby="deactivate-account-description"
    >
      <DialogContent sx={{ mt: 2, my: 1 }}>
        <Stack alignItems="center" spacing={3.5}>
          <Avatar
            color="error"
            sx={{
              width: 72,
              height: 72,
              fontSize: '1.75rem',
              color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
            }}
          >
            <ProfileDelete />
          </Avatar>
          <Stack spacing={2}>
            <Typography variant="h5" align="center">
              Are you sure you want to deactivate your account?
            </Typography>
            <Typography align="center">
              You can reactivate anytime by logging in again. This action will temporarily disable your account.
            </Typography>
          </Stack>

          <Stack direction="row" spacing={2} sx={{ width: 1 }}>
            <Button fullWidth color="secondary" variant="outlined" onClick={handleClose}>
              Cancel
            </Button>
            <Button fullWidth color="error" variant="contained" onClick={deactivateHandler} autoFocus>
              Confirm Deactivation
            </Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}
