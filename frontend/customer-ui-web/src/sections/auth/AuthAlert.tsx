// material-ui
import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import DialogContent from '@mui/material/DialogContent';

// project import
import Avatar from 'components/@extended/Avatar';
import { PopupTransition } from 'components/@extended/Transitions';

import { ThemeMode } from 'config';

// assets
import { ProfileDelete } from 'iconsax-react';

interface Props {
  message: string | null;
  open: boolean;
  handleClose: () => void;
}

export default function AuthAlert({ message, open, handleClose }: Props) {
  const theme = useTheme();

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      keepMounted
      TransitionComponent={PopupTransition}
      maxWidth="xs"
      aria-labelledby="column-delete-title"
      aria-describedby="column-delete-description"
    >
      <DialogContent sx={{ mt: 2, my: 1 }}>
        <Stack alignItems="center" spacing={3.5}>
          <Avatar
            color="error"
            sx={{
              width: 72,
              height: 72,
              fontSize: '1.75rem',
              color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
            }}
          >
            <ProfileDelete />
          </Avatar>
          <Stack spacing={2}>
            <Typography variant="h5" align="center" paddingX={5}>
              {message}
            </Typography>
          </Stack>
          <Stack direction="row" spacing={2} sx={{ width: 1 }}>
            <Button fullWidth color="primary" variant="contained" onClick={() => handleClose()} autoFocus>
              Okay
            </Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}
