'use client';

import {useState, SyntheticEvent, useEffect} from 'react';

import Link from 'next/link';

// material-ui
import Grid from '@mui/material/Grid';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import FormHelperText from '@mui/material/FormHelperText';
import Links from '@mui/material/Link';

// third-party
import * as Yup from 'yup';
import {Formik} from 'formik';

// project-imports
import IconButton from 'components/@extended/IconButton';
import AnimateButton from 'components/@extended/AnimateButton';

// assets
import {Eye, EyeSlash} from 'iconsax-react';
import SocialMedia from './SocialMedia';
import {ILoginForm} from '../../../redux/auth/types';
import {
  useExchangeTokenMutation,
  useLoginMutation,
} from '../../../redux/auth/authApiSlice';
import {useApiErrorHandler} from '../../../hooks/useApiErrorHandler';
import LoadingButton from 'components/@extended/LoadingButton';
import {useDispatch} from 'react-redux';
import {setCredentials} from '../../../redux/auth/authSlice';
import {useRouter} from 'next/navigation';
import {
  saveAccount,
  saveTokenForUser,
  setCurrentUser,
  StoredToken,
} from 'utils/switchAccount';

// ============================|| JWT - LOGIN ||============================ //

export default function AuthLogin() {
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };
  const dispatch = useDispatch();
  const handleError = useApiErrorHandler();

  const handleMouseDownPassword = (event: SyntheticEvent) => {
    event.preventDefault();
  };
  const [loginApi, {isLoading, error, reset: loginReset}] = useLoginMutation();
  const [
    exchangeTokenApi,
    {isLoading: tokenLoading, error: tokenError, reset: tokenReset},
  ] = useExchangeTokenMutation();
  const router = useRouter();

  const handleLogin = async (values: ILoginForm) => {
    const result = await loginApi(values).unwrap();
    if (result) {
      const token = await exchangeTokenApi({code: result.code}).unwrap();

      dispatch(setCredentials(token));

      const storedToken: StoredToken = {
        accessToken: token.accessToken,
        refreshToken: token.refreshToken,
        expires: Date.now() + 60 * 60 * 1000,
      };

      const account = {
        id: Date.now(),
        email: values.username,
      };

      saveTokenForUser(account.email, storedToken);
      saveAccount(account);
      setCurrentUser(account.email);

      router.push('/');
    }
  };

  useEffect(() => {
    if (error) {
      handleError(error);
      loginReset();
    }
    if (tokenError) {
      handleError(tokenError);
      tokenReset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error, handleError, tokenError]);

  return (
    <>
      <Formik
        initialValues={{
          username: '',
          password: '',
        }}
        validationSchema={Yup.object().shape({
          username: Yup.string().email().required('Email is required'),
          password: Yup.string().max(255).required('Password is required'),
        })}
        onSubmit={async (values, {setSubmitting}) => {
          setSubmitting(true);
          await handleLogin(values); // Ensure the login function completes before continuing
          setSubmitting(false);
        }}
      >
        {({
          errors,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting,
          touched,
          values,
        }) => (
          <form noValidate onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="username-login">Email</InputLabel>
                  <OutlinedInput
                    id="username-login"
                    type="username"
                    value={values.username}
                    name="username"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    fullWidth
                    error={Boolean(touched.username && errors.username)}
                    placeholder="Enter Username"
                  />
                </Stack>
                {touched.username && errors.username && (
                  <FormHelperText
                    error
                    id="standard-weight-helper-text-username-login"
                  >
                    {errors.username}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="password-login">Password</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.password && errors.password)}
                    id="-password-login"
                    type={showPassword ? 'text' : 'password'}
                    value={values.password}
                    name="password"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="Enter Password"
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                          color="secondary"
                        >
                          {showPassword ? <Eye /> : <EyeSlash />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                </Stack>
                {touched.password && errors.password && (
                  <FormHelperText
                    error
                    id="standard-weight-helper-text-password-login"
                  >
                    {errors.password}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12} sx={{mt: -1}}>
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                  spacing={2}
                >
                  <Links
                    variant="h6"
                    component={Link}
                    href={'/forgot-password'}
                    color="text.secondary"
                  >
                    Forgot Password?
                  </Links>
                </Stack>
              </Grid>
              <Grid
                item
                xs={12}
                alignItems={'center'}
                display={'flex'}
                justifyContent={'center'}
              >
                <AnimateButton>
                  <LoadingButton
                    disableElevation
                    disabled={isSubmitting || isLoading || tokenLoading}
                    fullWidth
                    size="large"
                    type="submit"
                    variant="contained"
                    color="primary"
                    sx={{width: '200px', height: '40px', fontWeight: 'bold'}}
                    loading={isLoading || tokenLoading}
                  >
                    Submit
                  </LoadingButton>
                </AnimateButton>
              </Grid>
              <Grid
                item
                xs={12}
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Typography variant="body1">OR</Typography>
              </Grid>
            </Grid>
          </form>
        )}
      </Formik>

      <Grid>
        <SocialMedia />
      </Grid>
      <Divider />
    </>
  );
}
