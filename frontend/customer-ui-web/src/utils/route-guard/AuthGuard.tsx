'use client';

import {useEffect} from 'react';

// next

// project-imports
import Loader from 'components/Loader';

// types
import {GuardProps} from 'types/auth';
import {useCreateGuestTokenMutation} from '../../redux/auth/authApiSlice';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import {selectCurrentLoginStatus, setGuestToken} from 'redux/auth/authSlice';
import {useSelector} from 'react-redux';
import {useRouter} from 'next/navigation';

// ==============================|| AUTH GUARD ||============================== //

export default function AuthGuard({children}: GuardProps) {
  const isLoggedInOrGuest = useSelector(selectCurrentLoginStatus);
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const guestToken = useAppSelector(state => state.auth.guestToken);
  const dispatch = useAppDispatch();
  const [createGuestToken, {isLoading: guestLoading, isUninitialized}] =
    useCreateGuestTokenMutation();
  const router = useRouter();

  const handleGuestToken = async () => {
    const response = await createGuestToken().unwrap();
    dispatch(setGuestToken(response));
    localStorage.setItem('guestToken', response.accessToken);
  };

  useEffect(() => {
    if (!isLoggedIn && guestToken === '') {
      handleGuestToken();
    }
  }, [isLoggedIn]);

  useEffect(() => {
    if (guestLoading || (isUninitialized && guestToken !== null)) {
      return;
    }

    if (!isLoggedInOrGuest && !isLoggedIn) {
      router.push('/login');
    }
  }, [isLoggedInOrGuest, guestLoading, isUninitialized, isLoggedIn]);

  if (guestLoading) return <Loader />;

  return <>{children}</>;
}
