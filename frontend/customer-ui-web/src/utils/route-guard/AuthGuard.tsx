'use client';

import {useEffect, useState} from 'react';

// next

// project-imports
import Loader from 'components/Loader';

// types
import {GuardProps} from 'types/auth';
import {
  useCreateGuestTokenMutation,
  useGetUserQuery,
} from '../../redux/auth/authApiSlice';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import {
  clearAuthNotice,
  selectCurrentLoginStatus,
  setGuestToken,
} from 'redux/auth/authSlice';
import {useSelector} from 'react-redux';
import {useRouter} from 'next/navigation';
import AuthAlert from 'sections/auth/AuthAlert';

// ==============================|| AUTH GUARD ||============================== //

export default function AuthGuard({children}: GuardProps) {
  const isLoggedInOrGuest = useSelector(selectCurrentLoginStatus);
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const guestToken = useAppSelector(state => state.auth.guestToken);
  const AuthNotice = useAppSelector(state => state.auth.authNotice);

  const dispatch = useAppDispatch();
  const [open, setOpen] = useState<boolean>(false);

  const [createGuestToken, {isLoading: guestLoading, isUninitialized}] =
    useCreateGuestTokenMutation();
  const {refetch: refetchUser} = useGetUserQuery(undefined, {
    skip: !isLoggedIn,
  });
  const router = useRouter();

  const handleGuestToken = async () => {
    const response = await createGuestToken().unwrap();
    dispatch(setGuestToken(response));
    localStorage.setItem('guestToken', response.accessToken);
  };

  useEffect(() => {
    if (!isLoggedIn && guestToken === '') {
      handleGuestToken();
    }
  }, [isLoggedIn]);

  useEffect(() => {
    if (AuthNotice) {
      setOpen(true);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }
    if (guestLoading || (isUninitialized && guestToken !== null)) {
      return;
    }

    if (!isLoggedInOrGuest && !isLoggedIn) {
      router.push('/login');
    }
  }, [
    isLoggedInOrGuest,
    guestLoading,
    isUninitialized,
    isLoggedIn,
    AuthNotice,
  ]);

  if (guestLoading) return <Loader />;

  return (
    <>
      <AuthAlert
        message={AuthNotice}
        open={open}
        handleClose={() => {
          dispatch(clearAuthNotice()), setOpen(!open), refetchUser();
        }}
      />
      {children}
    </>
  );
}
