'use client';

import {useEffect} from 'react';
import {useRouter, useSearchParams} from 'next/navigation';
import {useAppSelector} from 'redux/hooks';
import {APP_DEFAULT_PATH} from 'config';
import {GuardProps} from 'types/auth';
import {useSelector} from 'react-redux';
import {selectLoginCallback} from 'redux/apimonitor/apiMonitorSlice';
import {retryApi} from 'redux/apiSlice';

// ==============================|| GUEST GUARD ||============================== //

export default function GuestGuard({children}: GuardProps) {
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const callback = useSelector(selectLoginCallback);
  const apiRetryArgs = useAppSelector(state => state.apiRetry);
  const search = useSearchParams();

  const router = useRouter();

  useEffect(() => {
    if (search.get('avoid-callback') === 'true') {
      return;
    }
    if (isLoggedIn) {
      router.push(callback ?? APP_DEFAULT_PATH);
    }
    if (apiRetryArgs && isLoggedIn) {
      retryApi(apiRetryArgs);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoggedIn, apiRetryArgs]);

  return <>{children}</>;
}
