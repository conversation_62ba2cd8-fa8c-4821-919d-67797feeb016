export interface StoredToken {
  accessToken: string;
  refreshToken: string;
  expires: number;
}

export interface Account {
  id: number;
  email: string;
  isActive?: boolean;
}

export function saveTokenForUser(email: string, token: StoredToken) {
  const tokens = JSON.parse(localStorage.getItem('auth_tokens') || '{}');
  tokens[email] = token;
  localStorage.setItem('auth_tokens', JSON.stringify(tokens));
}

export function getTokenForUser(email: string): StoredToken | null {
  const tokens = JSON.parse(localStorage.getItem('auth_tokens') || '{}');
  return tokens[email] || null;
}
export function saveAccount(account: Account) {
  const accounts: Account[] = getAccounts();
  const exists = accounts.find(acc => acc.email === account.email);

  if (!exists) {
    accounts.push(account);
    localStorage.setItem('auth_accounts', JSON.stringify(accounts));
  }
}

export function getAccounts(): Account[] {
  return JSON.parse(localStorage.getItem('auth_accounts') || '[]');
}

export function setCurrentUser(email: string) {
  localStorage.setItem('current_user', email);
}

export function getCurrentUser(): string | null {
  return localStorage.getItem('current_user');
}
export function removeTokenForUser(email: string) {
  const tokens = JSON.parse(localStorage.getItem('auth_tokens') || '{}');
  delete tokens[email];
  localStorage.setItem('auth_tokens', JSON.stringify(tokens));
}

export function removeAccount(email: string) {
  const accounts = getAccounts().filter(acc => acc.email !== email);
  localStorage.setItem('auth_accounts', JSON.stringify(accounts));
}
