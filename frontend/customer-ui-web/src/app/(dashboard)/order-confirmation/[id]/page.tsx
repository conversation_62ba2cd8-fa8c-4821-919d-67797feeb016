'use client';

import {useParams} from 'next/navigation';
import OrderConfirmation from 'views/order-confirmation/OrderConfirmation';
import OrderDetails from 'views/order-confirmation/OrderProcessing';
import OrderSummary from 'views/order-confirmation/OrderSummary';
import {useGetOrderByIdQuery} from 'redux/order/orderApiSlice';
import {IFilter} from 'types/filter';
import {fieldsExcludeMetaFields} from 'types/api';

export default function ThankYouPage() {
  const params = useParams();
  const orderId =
    typeof params.id === 'string' ? params.id : (params.id?.[0] ?? '');

  const filter: IFilter = {
    include: [
      {
        relation: 'orderLineItems',
        scope: {
          fields: fieldsExcludeMetaFields,
          include: [
            {
              relation: 'productVariant',
              scope: {
                fields: fieldsExcludeMetaFields,
                include: [
                  {
                    relation: 'featuredAsset',
                    scope: {
                      fields: fieldsExcludeMetaFields,
                    },
                  },
                ],
              },
            },
          ],
        },
      },
      {
        relation: 'customer',
      },
      {
        relation: 'shippingAddress',
      },
      {
        relation: 'billingAddress',
      },
    ],
  };

  const {
    data: order,
    isLoading,
    isError,
  } = useGetOrderByIdQuery({id: orderId, filter});

  if (isLoading) return <p>Loading...</p>;
  if (isError || !order) return <p>Failed to load order.</p>;

  const formattedDate = new Date(order.createdOn).toLocaleDateString('en-GB');
  const formattedTime = new Date(order.createdOn).toLocaleTimeString('en-GB');

  return (
    <>
      <OrderConfirmation
        orderId={order.orderId as string}
        email={order.customer?.userTenant.user.email as string}
        date={formattedDate}
        time={formattedTime}
      />
      <OrderDetails order={order} />
      <OrderSummary order={order} />
    </>
  );
}
