'use client';

import {useEffect, useState} from 'react';
import {ChatDto} from 'types/chat';
import {Grid, Box} from '@mui/material';
import AccountMenu from 'views/order-details/AccountMenu';
import UserCard from 'views/order-details/UserCard';
import ChatWidget from 'views/message/CustomerChat';

export default function CustomerMessagePage() {
  const [chat, setChat] = useState<ChatDto | null>(null);

  useEffect(() => {
    const storedChat = sessionStorage.getItem('selectedChat');
    if (storedChat) {
      const parsed = JSON.parse(storedChat);
      setChat(parsed);
    }
  }, []);

  return (
    <Grid container spacing={3} px={6}>
      <Grid item xs={12} sm={3}>
        <UserCard imageUrl={''} name={''} />
        <Box sx={{mt: 2}}>
          <AccountMenu />
        </Box>
      </Grid>
      <Grid item xs={12} sm={9}>
        {chat && <ChatWidget chat={chat} />}
      </Grid>
    </Grid>
  );
}
