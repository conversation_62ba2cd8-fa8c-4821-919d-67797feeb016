'use client';
import {Box, Grid} from '@mui/material';
import TermsCondition from 'views/authentication/TermsAndCondition';
import AccountMenu from 'views/order-details/AccountMenu';
import UserCard from 'views/order-details/UserCard';

export default function ProfilePage() {
  return (
    <Grid container spacing={3} px={6}>
      {/* Left side */}
      <Grid item xs={12} sm={3}>
        <UserCard imageUrl={''} name={''} />
        <Box sx={{mt: 2}}>
          <AccountMenu />
        </Box>
      </Grid>

      {/* Right side */}
      <Grid item xs={12} sm={9}>
        <TermsCondition />
      </Grid>
    </Grid>
  );
}
