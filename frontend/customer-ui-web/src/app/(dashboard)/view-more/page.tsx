'use client';

import {useSearchParams} from 'next/navigation';
import {Box, Typography} from '@mui/material';
import ViewAllGifts from 'views/view-more/ViewAllGifts';
import ViewAllMostViewed from 'views/view-more/ViewAllMostViewed';
import ViewAllTopSelling from 'views/view-more/ViewAllTopSelling';
import ViewAllCategories from 'views/view-more/ViewAllCategories';

export default function ViewMorePage() {
  const searchParams = useSearchParams();
  const type = searchParams.get('type');

  const renderComponent = () => {
    switch (type) {
      case 'gift':
        return <ViewAllGifts />;
      case 'most-viewed':
        return <ViewAllMostViewed />;
      case 'top-selling':
        return <ViewAllTopSelling />;
      case 'all-categories':
        return <ViewAllCategories />;
      default:
        return <Typography>Invalid section type.</Typography>;
    }
  };

  return <Box p={3}>{renderComponent()}</Box>;
}
