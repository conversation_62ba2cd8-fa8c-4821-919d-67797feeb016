'use client';

import {useParams} from 'next/navigation';
import {useEffect, useState} from 'react';
import {Box, Grid, Card, CardContent, Typography} from '@mui/material';
import draftToHtml from 'draftjs-to-html';
import {useDispatch} from 'react-redux';
import {
  useGetGuestTokenMutation,
  useGetTermsAndConditionQuery,
} from 'redux/terms-and-privacy/termsApiSlice';
import {setGuestToken} from 'redux/auth/authSlice';
import {Legals} from 'types/legal-category.enum';
import AccountMenu from 'views/order-details/AccountMenu';
import UserCard from 'views/order-details/UserCard';

const slugToTypeMap: Record<string, Legals> = {
  terms: Legals.TermsofUse,
  privacy: Legals.PrivacyPolicy,
  affiliate: Legals.AffiliatePolicy,
  refund: Legals.ReturnRefundPolicy,
  infringement: Legals.InfringementPolicy,
  agreements: Legals.Agreements,
  licence: Legals.Licence,
  disclaimer: Legals.Disclaimer,
  guidelines: Legals.Guideline,
};

const LegalPage = () => {
  const {slug} = useParams();
  const dispatch = useDispatch();
  const [guestCode, setGuestCode] = useState<string | null>(null);
  const [getToken] = useGetGuestTokenMutation();

  const accessToken =
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;

  useEffect(() => {
    if (!accessToken) {
      const handleGuestLogin = async () => {
        const result = await getToken().unwrap();
        if (result?.accessToken) {
          dispatch(setGuestToken(result));
          setGuestCode(result.accessToken);
        }
      };
      handleGuestLogin();
    }
  }, [accessToken, getToken, dispatch]);

  const tokenToUse = accessToken || guestCode;

  const {data: termData, isLoading} = useGetTermsAndConditionQuery(
    tokenToUse!,
    {
      skip: !tokenToUse,
    },
  );

  const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
  const currentType = slugToTypeMap[slug as string];
  const legalContentList = termData?.filter(term => term.type === currentType);
  return (
    <Grid container spacing={3} px={6}>
      <Grid item xs={12} sm={3}>
        <UserCard imageUrl={''} name={''} />
        <Box mt={2}>
          <AccountMenu />
        </Box>
      </Grid>

      <Grid item xs={12} sm={9}>
        <Card sx={{borderRadius: 3, p: 2}}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : legalContentList?.length ? (
            legalContentList.map((item, index) => {
              let htmlContent = '<p>No content</p>';
              try {
                htmlContent =
                  typeof item.data === 'string'
                    ? isHtml(item.data)
                      ? item.data
                      : draftToHtml(JSON.parse(item.data))
                    : draftToHtml(item.data);
              } catch (err) {
                console.error('Failed to convert content:', err);
                htmlContent = '<p>Error loading content.</p>';
              }

              return (
                <CardContent key={index} sx={{mb: 2}}>
                  <div dangerouslySetInnerHTML={{__html: htmlContent}} />
                </CardContent>
              );
            })
          ) : (
            <Typography>No legal content found for {slug}.</Typography>
          )}
        </Card>
      </Grid>
    </Grid>
  );
};

export default LegalPage;
