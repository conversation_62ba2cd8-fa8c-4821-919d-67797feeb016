'use client';

import {<PERSON>, Button, Grid, Stack, Typography} from '@mui/material';
import {
  useGetMostViewedProductsQuery,
  useGetPageSectionsQuery,
  useGetRecentlyViewedProductsQuery,
  useGetTopSellingProductsQuery,
} from 'redux/ecom/pageSectionApiSlice';
import {
  SectionItemType,
  CardStyle,
  PageType,
  SectionType,
  PRODUCT_SECTION_TYPES,
} from '../../../enums/pageSection.enum';
import Carousel from 'views/Product/Carousel';
import {CustomFacetCard} from 'views/Product/CustomFacetCard';
import {CustomFacetCardWithTitle} from 'views/Product/CustomFacetCardWithTitle';
import {CustomFacetCardWithBackground} from 'views/Product/CustomFacetCardWithBackground';
import {SplitCard} from 'views/Product/SplitCard';
import {PageSection, SectionItem} from 'types/page-section';
import {fieldsExcludeMetaFields} from 'types/api';
import ProductCard from 'views/products/ProductCard';
import BannerSection from 'views/Product/Banner';
import TextBlockSection from 'views/Product/TextBlock';
import CollectionCard from 'views/Product/CollectionCard';
import {ReviewStatus} from 'types/review';
import {useAppSelector} from 'redux/hooks';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import React from 'react';
import {IFilter} from 'types/filter';
import ProductFilterSection from 'views/Product/ProductFilterSection';
import AllCategoriesSection from 'views/Product/AllCategories';
import FacetCard from 'views/Product/FacetCard';
import {GiftProductsSection} from 'views/Product/GiftProductsSection';
import {useRouter} from 'next/navigation';

export default function HomeScreen() {
  const {data: sections = [], isLoading} = useGetPageSectionsQuery({
    where: {isActive: true, pageType: PageType.HOME},
    order: ['displayOrder ASC'],
    include: [
      {relation: 'sectionItems', scope: {fields: fieldsExcludeMetaFields}},
    ],
    fields: fieldsExcludeMetaFields,
  });
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});

  const productFilter: IFilter = {
    include: [
      {
        relation: 'featuredAsset',
        scope: {
          fields: {preview: true, id: true},
        },
      },
      {
        relation: 'product',
        scope: {
          fields: {description: true, id: true},
        },
      },
      {
        relation: 'productVariantPrice',
        scope: {
          fields: {
            price: true,
            mrp: true,
            currencyCode: true,
          },
        },
      },
      ...(isLoggedIn
        ? [
            {
              relation: 'wishlist',
              scope: {
                where: {
                  deleted: false,
                  customerId: user?.profileId,
                },
                fields: {id: true},
              },
            },
          ]
        : []),
      {
        relation: 'reviews',
        scope: {
          fields: {
            rating: true,
          },
          where: {
            status: ReviewStatus.APPROVED,
          },
        },
      },
    ],
    fields: {
      name: true,
      id: true,
      featuredAssetId: true,
      productId: true,
    },
  };

  const {data: mostViewed = []} = useGetMostViewedProductsQuery(productFilter, {
    skip: !isLoggedIn,
  });

  const {data: recentlyViewed = []} = useGetRecentlyViewedProductsQuery(
    productFilter,
    {skip: !isLoggedIn},
  );

  const {data: topSelling = []} = useGetTopSellingProductsQuery(productFilter);

  const scrollGradientSx = {
    overflowX: 'auto',
    pb: 2,
    '&::-webkit-scrollbar': {
      height: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: 'transparent',
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'linear-gradient(to right, #5847F9, #00C9FF)',
      borderRadius: '10px',
      minWidth: '30px',
    },
  };

  const router = useRouter();

  const renderSectionItem = (item: SectionItem, cardStyle: CardStyle) => {
    switch (item.entityType) {
      case SectionItemType.PRODUCT:
        return item.productVariants?.map(variant => (
          <ProductCard key={variant.id} productVariant={variant} />
        ));

      case SectionItemType.CATEGORY:
        return (
          <CustomFacetCard
            key={item.id}
            name={item.title}
            thumbnail={item.previewUrl as string}
          />
        );

      // case SectionItemType.IMAGE:
      case SectionItemType.CUSTOM_CARD:
        switch (cardStyle) {
          case CardStyle.IMAGE_TITLE_SUBTITLE:
            return (
              <CustomFacetCardWithTitle
                key={item.id}
                name={item.title}
                description={item.subtitle as string}
                thumbnail={item.previewUrl as string}
              />
            );

          case CardStyle.OFFER_CARD:
          case CardStyle.OFFER_CARD_INVERSE:
            return (
              <SplitCard
                key={item.id}
                name={item.title}
                price={0}
                thumbnail={item.previewUrl as string}
                reverse={cardStyle === CardStyle.OFFER_CARD_INVERSE}
              />
            );

          case CardStyle.IMAGE_ONLY:
          default:
            return (
              <CustomFacetCardWithBackground
                key={item.id}
                thumbnail={item.previewUrl as string}
                title={item.title}
              />
            );
        }

      default:
        return null;
    }
  };

  const renderSection = (section: PageSection) => {
    const items = section.sectionItems || [];

    if (section.type === SectionType.CAROUSEL) {
      const carouselSlides = items
        .filter(item => !!item.previewUrl)
        .map(item => ({
          imageUrl: item.previewUrl as string,
          redirectUrl: item.metadata?.redirectUrl || '#',
          title: item.title || '',
        }));

      return <Carousel slides={carouselSlides} />;
    }

    if (section.type === SectionType.TEXT_BLOCK) {
      return <TextBlockSection title={section.title} items={items} />;
    }

    if (section.type === SectionType.BANNER) {
      const bannerItem = items.find(item => item.previewUrl);
      if (!bannerItem) return null;

      return (
        <BannerSection
          imageUrl={bannerItem.previewUrl as string}
          altText={bannerItem.title || 'Banner'}
          link={bannerItem.metadata?.redirectUrl}
        />
      );
    }

    if (section.type === SectionType.FEATURED_COLLECTION) {
      return (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack
            flexDirection="row"
            justifyContent="space-between"
            sx={scrollGradientSx}
          >
            <Typography variant="h4">{section.title}</Typography>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
            sx={{pb: 2}}
          >
            {items.map(item => (
              <CollectionCard key={item.id} item={item} />
            ))}
          </Stack>
        </Stack>
      );
    }

    // Handle sections that display product cards
    if (PRODUCT_SECTION_TYPES.includes(section.type)) {
      const allProductVariants = items.flatMap(
        item => item.productVariants || [],
      );
      return (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="h4">{section.title}</Typography>
            <Button variant="outlined">View More</Button>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
          >
            {allProductVariants.map(variant => (
              <Box
                key={variant.id}
                sx={{
                  minWidth: 240,
                  maxWidth: 300,
                  flex: '0 0 auto',
                }}
              >
                <ProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      );
    }

    if (section.type === SectionType.RECENTLY_VIEWED) {
      return recentlyViewed.length > 0 ? (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Typography variant="h4">{section.title}</Typography>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
            sx={{...scrollGradientSx, pb: 2}}
          >
            {recentlyViewed.map(variant => (
              <Box
                key={variant.id}
                sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
              >
                <ProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null;
    }

    if (section.type === SectionType.MOST_VIEWED) {
      return mostViewed.length > 0 ? (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="h4">{section.title}</Typography>
            <Button
              variant="outlined"
              onClick={() => router.push('/view-more?type=most-viewed')}
            >
              View More
            </Button>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
            sx={{...scrollGradientSx, pb: 2}}
          >
            {mostViewed.map(variant => (
              <Box
                key={variant.id}
                sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
              >
                <ProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null;
    }

    if (section.type === SectionType.TOP_SELLING) {
      return topSelling.length > 0 ? (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Stack flexDirection="row" justifyContent="space-between">
            <Typography variant="h4">{section.title}</Typography>
            <Button
              variant="outlined"
              onClick={() => router.push('/view-more?type=top-selling')}
            >
              View More
            </Button>
          </Stack>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={5}
            overflow="scroll"
            sx={{...scrollGradientSx, pb: 2}}
          >
            {topSelling.map(variant => (
              <Box
                key={variant.id}
                sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
              >
                <ProductCard productVariant={variant} />
              </Box>
            ))}
          </Stack>
        </Stack>
      ) : null;
    }

    if (section.type === SectionType.PRODUCT_FILTER) {
      return <ProductFilterSection section={section} />;
    }

    if (section.type === SectionType.ALL_CATEGORIES) {
      return <AllCategoriesSection />;
    }

    if (section.type === SectionType.FACETS) {
      return (
        <Stack flexDirection="column" gap={3} mt={3}>
          <Typography variant="h4">{section.title}</Typography>
          <Stack
            flexDirection="row"
            flexWrap="nowrap"
            gap={3}
            overflow="scroll"
            sx={{...scrollGradientSx, pb: 2}}
          >
            {items.map(item => (
              <FacetCard
                key={item.id}
                title={item.title}
                subtitle={item.subtitle}
                thumbnail={item.previewUrl as string}
                facetValueIds={item.metadata?.facetValueIds || []}
                cardStyle={section.cardStyle as CardStyle}
              />
            ))}
          </Stack>
        </Stack>
      );
    }

    if (section.type === SectionType.GIFT_PRODUCTS) {
      return (
        <GiftProductsSection
          title={section.title}
          isLoggedIn={isLoggedIn}
          user={user}
        />
      );
    }

    // Default section rendering
    return (
      <Stack flexDirection="column" gap={3} mt={3}>
        <Stack flexDirection="row" justifyContent="space-between">
          <Typography variant="h4">{section.title}</Typography>
        </Stack>
        <Stack
          flexDirection="row"
          flexWrap="nowrap"
          gap={5}
          overflow="scroll"
          sx={{...scrollGradientSx, pb: 2}}
        >
          {items.flatMap(
            (item: SectionItem) =>
              renderSectionItem(item, section.cardStyle as CardStyle) ?? [],
          )}
        </Stack>
      </Stack>
    );
  };

  return (
    <Box>
      {isLoading ? (
        <Typography>Loading...</Typography>
      ) : (
        <Grid container>
          <Grid item xs={12}>
            <Grid container px={3}>
              {(() => {
                return sections.map((section, index) => {
                  return (
                    <Grid item xs={12} mt={4}>
                      {renderSection(section)}
                    </Grid>
                  );
                });
              })()}
            </Grid>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}
