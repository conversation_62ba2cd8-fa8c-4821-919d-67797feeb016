'use client';
import {Box, Grid} from '@mui/material';
import AccountMenu from 'views/order-details/AccountMenu';
import UserCard from 'views/order-details/UserCard';
import ChangePassword from 'views/change-password/ChangePassword';

export default function ChangePasswordPage() {
  return (
    <Grid container spacing={3} px={6}>
      <Grid item xs={12} sm={3}>
        <UserCard imageUrl={''} name={''} />
        <Box sx={{mt: 2}}>
          <AccountMenu />
        </Box>
      </Grid>
      <Grid item xs={12} sm={9}>
        <ChangePassword />
      </Grid>
    </Grid>
  );
}
