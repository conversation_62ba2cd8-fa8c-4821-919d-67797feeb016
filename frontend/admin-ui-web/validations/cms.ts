import { SectionType } from 'types/cms';
import * as Yup from 'yup';
export const carouselItemSchema = Yup.object({
  imageUrl: Yup.string().required(),
  title: Yup.string(),
  subtitle: Yup.string(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  metadata: Yup.object({
    redirectUrl: Yup.string().url().label('Redirect URL')
  }).notRequired()
});

export const bannerItemSchema = Yup.object({
  imageUrl: Yup.string().required(),
  title: Yup.string(),
  subtitle: Yup.string(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  metadata: Yup.object({
    redirectUrl: Yup.string().url().label('Redirect URL')
  }).notRequired()
});

export const featuredProductsItemSchema = Yup.object({
  title: Yup.string(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  metadata: Yup.object({
    productIds: Yup.array().of(Yup.string().required())
  }).required()
});

export const categoryGridItemSchema = Yup.object({
  imageUrl: Yup.string().required(),
  title: Yup.string().required(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  metadata: Yup.object({
    categoryId: Yup.string().required()
  }).required()
});

export const textBlockItemSchema = Yup.object({
  title: Yup.string(),
  metadata: Yup.object({
    richText: Yup.string().required('Text block content is required')
  }).required(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required()
});

export const sectionSchema = Yup.object({
  id: Yup.string().required(),
  type: Yup.string().oneOf(Object.values(SectionType)).required(),
  title: Yup.string().required()
});

export const sectionsValidationSchema = Yup.object({
  sections: Yup.array().of(sectionSchema)
});

export const occasionCardsItemSchema = Yup.object({
  imageUrl: Yup.string().required('Image is required'),
  title: Yup.string().required('Title is required'),
  subtitle: Yup.string(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  entityId: Yup.string().required('Category is required'),
  metadata: Yup.object({
    viewAllLink: Yup.string().url('Must be a valid URL')
  }).notRequired()
});

export const featuredCollectionItemSchema = Yup.object({
  imageUrl: Yup.string().required('Image is required'),
  title: Yup.string().required('Title is required'),
  subtitle: Yup.string(),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  entityId: Yup.string().required('Collection is required'),
  metadata: Yup.object({
    redirectUrl: Yup.string().url('Must be a valid URL')
  }).notRequired()
});

export const productCardsItemSchema = Yup.object({
  title: Yup.string().required('Title is required'),
  displayOrder: Yup.number().required(),
  entityType: Yup.string().required(),
  entityId: Yup.string().required('Product is required'),
  metadata: Yup.object({
    showPrice: Yup.boolean(),
    showRating: Yup.boolean(),
    showAddToCart: Yup.boolean(),
    showWishlist: Yup.boolean(),
    redirectUrl: Yup.string().url('Must be a valid URL')
  }).notRequired()
});

export const productFilterItemSchema = Yup.object().shape({
  id: Yup.string().optional(),
  title: Yup.string().required('Title is required'),
  subtitle: Yup.string().optional(),
  imageUrl: Yup.string().required('Image is required'),
  entityType: Yup.string().oneOf(['productFilter']).required('Entity type must be productFilter'),
  entityId: Yup.mixed().nullable().optional(), // Not needed for productFilter type
  metadata: Yup.object()
    .shape({
      redirectUrl: Yup.string().url('Must be a valid URL').optional(),
      filters: Yup.object()
        .required('Filters are required')
        .test('is-not-empty', 'Filters cannot be empty', (value) => {
          return value && Object.keys(value).length > 0;
        })
    })
    .required('Metadata is required'),
  displayOrder: Yup.number().optional()
});

export const facetItemSchema = Yup.object().shape({
  id: Yup.string().optional(),
  title: Yup.string().required('Title is required'),
  subtitle: Yup.string().optional(),
  imageUrl: Yup.string().required('Image is required'),
  entityType: Yup.string().oneOf(['facet-values'], 'Entity type must be facet-values').required('Entity type is required'),
  entityId: Yup.mixed().nullable().optional(), // Not needed for facet-values
  metadata: Yup.object()
    .shape({
      facetValueIds: Yup.array().of(Yup.string()).min(1, 'At least one facet value must be selected').required('Facet values are required')
    })
    .required('Metadata is required'),
  displayOrder: Yup.number().optional()
});
