import * as Yup from 'yup';
import { EditorState, convertToRaw } from 'draft-js';

export const legalsValidationSchema = Yup.object({
  data: Yup.mixed<EditorState>().test(
    'is-not-empty',
    'Content is required',
    (value) => value instanceof EditorState && convertToRaw(value.getCurrentContent()).blocks.some((block) => block.text.trim() !== '')
  ),
  category: Yup.string().required('Category is required'),
  type: Yup.string().required('Type is required'),
  visibility: Yup.number().required('Visibility is required')
});
