import * as Yup from 'yup';
export const serviceValidationSchema = Yup.object().shape({
  name: Yup.string().required('Service name is required'),
  price: Yup.number().required('Price is required').min(0, 'Price must be non-negative'),
  currency: Yup.string().required('Currency is required'),
  taxCategoryId: Yup.string().required('Tax category is required'),
  isActive: Yup.boolean().required()
});
