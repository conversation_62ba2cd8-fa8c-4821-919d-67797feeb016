'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetCustomerByIdQuery } from 'redux/app/customer/customerApiSlice';
import CustomerCreate from 'views/customers/CustomerCreate';
import { customerIncludes } from 'constants/customer';
import { Status } from 'enums/customer.enum';

const EditCustomerPage = ({ params }: { params: { id: string } }) => {
  const customerId = params.id;

  const {
    data: customerData,
    isLoading,
    error,
    refetch
  } = useGetCustomerByIdQuery({
    id: customerId,
    filter: { include: customerIncludes }
  });

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !customerData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          Failed to fetch customer details.
        </Typography>
      </Container>
    );
  }

  const user = customerData.userTenant?.user;

  const initialValues = {
    firstName: user?.firstName ?? '',
    lastName: user?.lastName ?? '',
    email: user?.email ?? '',
    phone: user?.phone ?? '',
    dob: user?.dob ?? '',
    gender: user?.gender ?? '',
    addresses: customerData.addresses ?? [],
    status: customerData.status ?? Status.ACTIVE
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <CustomerCreate isEdit customerId={customerId} initialValues={initialValues} refetch={refetch} />
    </Container>
  );
};

export default EditCustomerPage;
