'use client';

import { useEffect } from 'react';
import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetServiceByIdQuery } from 'redux/app/service/serviceApiSlice';
import EcomDukeServiceCreate from 'views/ecomduke-service/ServiceCreate';

const EditServicePage = ({ params }: { params: { id: string } }) => {
  const serviceId = params.id;

  const { data: serviceData, isLoading, error, refetch } = useGetServiceByIdQuery({ id: serviceId });

  useEffect(() => {
    refetch();
  }, [refetch]);

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !serviceData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          Failed to fetch service details.
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <EcomDukeServiceCreate
          isEdit
          serviceId={serviceId}
          initialValues={{
            name: serviceData.name ?? '',
            description: serviceData.description ?? '',
            price: serviceData.price ?? 0,
            currency: serviceData.currency ?? 'INR',
            taxCategoryId: serviceData.taxCategoryId ?? '',
            isActive: serviceData.isActive ?? true
          }}
        />
      </Container>
    </Container>
  );
};

export default EditServicePage;
