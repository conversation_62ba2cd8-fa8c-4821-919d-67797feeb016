'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetLegalByIdQuery } from 'redux/app/contents/legals/legalsApiSlice';
import LegalsForm from 'views/legals/LegalsForm';

const EditLegalsPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { data: legalData, isLoading, error } = useGetLegalByIdQuery(id);

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error || !legalData) {
    return <Typography color="error">Failed to load Legal Section</Typography>;
  }

  return (
    <Container maxWidth="md">
      <LegalsForm
        initialValues={{
          ...legalData
        }}
        isEdit
        legalsId={id}
      />
    </Container>
  );
};

export default EditLegalsPage;
