'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import FaqCreate from 'views/faq/FaqCreate';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import { useGetFaqByIdQuery } from 'redux/app/contents/faq/faqApiSlice';

const EditFaqPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { data: faqData, isLoading, error, refetch } = useGetFaqByIdQuery({ id });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error || !faqData) {
    return <Typography color="error">Failed to load FAQ</Typography>;
  }

  return (
    <Container maxWidth="md">
      <FaqCreate
        initialValues={{
          ...faqData,
          visibility: (faqData.visibility ?? FaqVisibility.ALL) as FaqVisibility,
          status: (faqData.status ?? FaqStatus.ACTIVE) as FaqStatus
        }}
        isEdit
        faqId={id}
        refetch={refetch}
      />
    </Container>
  );
};

export default EditFaqPage;
