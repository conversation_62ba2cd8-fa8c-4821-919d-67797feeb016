'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import SupportCreate from 'views/support/SupportCreate';
import { useGetSupportByIdQuery } from 'redux/app/contents/support/supportApiSlice';

const EditSupportPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { data: faqData, isLoading, error } = useGetSupportByIdQuery({ id });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error || !faqData) {
    return <Typography color="error">Failed to load FAQ</Typography>;
  }

  return (
    <Container maxWidth="md">
      <SupportCreate
        initialValues={{
          ...faqData,
          visibility: (faqData.visibility ?? FaqVisibility.ALL) as FaqVisibility,
          status: (faqData.status ?? FaqStatus.ACTIVE) as FaqStatus
        }}
        isEdit
        faqId={id}
      />
    </Container>
  );
};

export default EditSupportPage;
