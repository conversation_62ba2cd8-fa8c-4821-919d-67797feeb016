'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetPromoCodeByIdQuery } from 'redux/app/promo-code/promoApiSlice';
import PromoCodeCreate from 'views/promo-code/PromoCodeForm';

const PromoCodeEditPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { data: promoCodeData, isLoading, error, refetch } = useGetPromoCodeByIdQuery({ id });

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !promoCodeData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography color="error" variant="h6">
          Failed to load promo code
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <PromoCodeCreate
        isEdit={true}
        promoCodeId={id}
        initialValues={{
          code: promoCodeData.code ?? '',
          type: promoCodeData.type ?? 'FLAT',
          value: promoCodeData.value ?? 0,
          appliesTo: promoCodeData.appliesTo ?? 'PRODUCTS',
          productIds: promoCodeData.productIds || promoCodeData.promoCodeProducts?.map((p) => p.productId) || [],
          categoryIds: promoCodeData.categoryIds || promoCodeData.promoCodeCollections?.map((c) => c.collectionId) || [],
          minCartValue: promoCodeData.minCartValue ?? undefined,
          maxDiscountCap: promoCodeData.maxDiscountCap ?? undefined,
          usageLimitPerUser: promoCodeData.usageLimitPerUser ?? undefined,
          usageLimitTotal: promoCodeData.usageLimitTotal ?? undefined,
          isFirstTimeUserOnly: promoCodeData.isFirstTimeUserOnly ?? false,
          isRepeatUserOnly: promoCodeData.isRepeatUserOnly ?? false,
          validFrom: promoCodeData.validFrom ?? '',
          validTill: promoCodeData.validTill ?? '',
          visibility: promoCodeData.visibility ?? 'GENERAL',
          isActive: promoCodeData.isActive ?? true
        }}
        refetch={refetch}
      />
    </Container>
  );
};

export default PromoCodeEditPage;
