'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetCollectionsByIdQuery } from 'redux/app/collections/collectionApiSlice';
import CollectionCreate from 'views/collections/CollectionCreate';

const CollectionEditPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const {
    data: collectionData,
    isLoading,
    error,
    refetch
  } = useGetCollectionsByIdQuery({
    id,
    filter: { include: [{ relation: 'featuredAsset' }] }
  });

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !collectionData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography color="error" variant="h6">
          Failed to load category
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <CollectionCreate
        isEdit
        collectionId={id}
        refetch={refetch}
        initialValues={{
          name: collectionData.name ?? '',
          parentId: collectionData.parentId ?? '',
          featuredAssetId: collectionData.featuredAssetId ?? '',
          isRoot: collectionData.isRoot ?? false,
          position: collectionData.position ?? 0
        }}
        featuredAsset={
          collectionData.featuredAsset
            ? {
                id: collectionData.featuredAsset.id,
                previewUrl: collectionData.featuredAsset.previewUrl
              }
            : undefined
        }
      />
    </Container>
  );
};

export default CollectionEditPage;
