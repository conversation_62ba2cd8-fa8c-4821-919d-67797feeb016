'use client';

import { useFormik } from 'formik';
import dynamic from 'next/dynamic';
import { EditorProps } from 'react-draft-wysiwyg';
import { convertToRaw, EditorState, convertFromRaw } from 'draft-js';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { TextField, Button, Select, MenuItem, Box, Typography, CardContent, Card, Grid } from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import { helpValidationSchema } from '../../../validations/faq';
import { useEffect } from 'react';
import { LegalCategory } from 'enums/legal-category.enum';
import { useCreateHelpMutation, useUpdateHelpMutation } from 'redux/app/contents/help/helpApiSlice';
import { HelpItem } from 'types/admin';

const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as unknown as React.FC<EditorProps>), {
  ssr: false
});
const categoryOptions = Object.values(LegalCategory);

interface HelpCreateProps {
  initialValues?: Partial<HelpItem>;
  isEdit?: boolean;
  faqId?: string;
  refetch?: () => Promise<void> | any;
}

function HelpCreate({ initialValues, isEdit = false, faqId, refetch }: HelpCreateProps) {
  const router = useRouter();
  const [createHelp] = useCreateHelpMutation();
  const [updateHelp] = useUpdateHelpMutation();

  const formatEnumKey = (key: string) => {
    return key
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const initialEditorState =
    initialValues?.answer && typeof initialValues.answer === 'string'
      ? (() => {
          try {
            return EditorState.createWithContent(convertFromRaw(JSON.parse(initialValues.answer)));
          } catch {
            return EditorState.createEmpty();
          }
        })()
      : EditorState.createEmpty();

  const formik = useFormik({
    initialValues: {
      question: initialValues?.question || '',
      answer: initialEditorState,
      category: initialValues?.category || '',
      visibility: initialValues?.visibility ?? FaqVisibility.ALL,
      status: initialValues?.status ?? FaqStatus.ACTIVE
    },
    validationSchema: helpValidationSchema,
    onSubmit: async (values) => {
      if (values.answer instanceof EditorState) {
        const rawContentState = convertToRaw(values.answer.getCurrentContent());
        const stringifiedAnswer = JSON.stringify(rawContentState);

        const faqData: HelpItem = {
          question: values.question,
          answer: stringifiedAnswer,
          category: values.category,
          status: values.status,
          visibility: values.visibility
        };
        if (isEdit && faqId) {
          await updateHelp({ id: faqId, data: faqData }).unwrap();
          openSnackbar({
            open: true,
            message: 'FAQ Updated successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
          router.push('/contents/help');
        } else {
          await createHelp(faqData).unwrap();
          openSnackbar({
            open: true,
            message: 'FAQ Created successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
          router.push('/contents/help');
        }
        await refetch?.();
      }
    }
  });
  useEffect(() => {
    const fetchData = async () => {
      if (refetch) {
        await refetch();
      }
    };
    fetchData();
  }, [refetch]);

  return (
    <Card sx={{ maxWidth: '100%', mx: 'auto', mt: 2, p: 2, boxShadow: 3 }}>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {isEdit ? 'Edit FAQ' : 'Create HELP'}
          </Typography>

          <TextField
            fullWidth
            variant="outlined"
            placeholder="Enter question here"
            name="question"
            value={formik.values.question}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.question && Boolean(formik.errors.question)}
            helperText={formik.touched.question && formik.errors.question}
            margin="dense"
          />

          <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
            Help faqs Answer
          </Typography>
          <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 300 }}>
            <Editor
              editorState={formik.values.answer}
              onEditorStateChange={(editorState: any) => formik.setFieldValue('answer', editorState)}
              wrapperClassName="wrapper-class"
              editorClassName="editor-class"
              toolbarClassName="toolbar-class"
            />
          </Box>
          {formik.touched.answer && formik.errors.answer && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {String(formik.errors.answer)}
            </Typography>
          )}

          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6}>
              <Select fullWidth name="category" value={formik.values.category} onChange={formik.handleChange} displayEmpty>
                <MenuItem value="" disabled>
                  Category
                </MenuItem>
                {categoryOptions.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    {cat}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.category && formik.errors.category && (
                <Typography variant="body2" color="error">
                  {formik.errors.category}
                </Typography>
              )}
            </Grid>

            <Grid item xs={6}>
              <Select
                fullWidth
                name="visibility"
                value={formik.values.visibility}
                onChange={(event) => formik.setFieldValue('visibility', Number(event.target.value))}
              >
                <MenuItem value="" disabled>
                  Visibility{' '}
                </MenuItem>
                {Object.entries(FaqVisibility)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {formatEnumKey(key)}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>

            <Grid item xs={6}>
              <Select
                fullWidth
                name="status"
                value={formik.values.status}
                onChange={(event) => formik.setFieldValue('status', Number(event.target.value))}
              >
                <MenuItem value="" disabled>
                  Status{' '}
                </MenuItem>
                {Object.entries(FaqStatus)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {formatEnumKey(key)}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button type="submit" variant="contained" color="primary" disableElevation>
              {isEdit ? 'Update HELP' : 'Create HELP'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

export default HelpCreate;
