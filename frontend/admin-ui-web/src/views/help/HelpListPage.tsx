'use client';

import { useMemo, useState, MouseEvent, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import moment from 'moment-timezone';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import Loader from 'components/Loader';
import { ThemeMode } from 'config';
import { Chip } from '@mui/material';
import { FaqStatus } from 'enums/faq-status.enum';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import { HelpItem } from 'types/admin';
import HelpTable from './HelpTable';
import AlertHelpDelete from './AlertHelpDelete';
import { useGetHelpQuery, useGetHelpsCountQuery } from 'redux/app/contents/help/helpApiSlice';
import { LegalCategory } from 'enums/legal-category.enum';

const HelpListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const visibilityLabels = useMemo(
    () => ({
      [FaqVisibility.ALL]: 'All Users',
      [FaqVisibility.ADMIN]: 'Admin Only',
      [FaqVisibility.SELLER]: 'Seller Only',
      [FaqVisibility.CUSTOMER]: 'Customer Only',
      [FaqVisibility.SellOnEcomdukes]: 'Sell on Ecomdukes'
    }),
    []
  );

  const statusLabels = useMemo(
    () => ({
      [FaqStatus.ACTIVE]: { label: 'Active', color: 'success' as const },
      [FaqStatus.INACTIVE]: { label: 'Inactive', color: 'default' as const }
    }),
    []
  );

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [faqDeleteId, setfaqDeleteId] = useState<string>('');

  const canCreate = hasPermission(PermissionKeys.CreateHelp);
  const canEdit = hasPermission(PermissionKeys.UpdateHelp);
  const canDelete = hasPermission(PermissionKeys.DeleteHelp);

  const [open, setOpen] = useState<boolean>(false);
  const {
    data: faqList,
    isLoading: faqListLoading,
    refetch
  } = useGetHelpQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['faq.quetions']),
    ...convertPaginationToLoopback(pagination)
  });

  const { data: faqCount, isLoading: faqCountLoading } = useGetHelpsCountQuery();

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);
  useEffect(() => {
    const fetchData = async () => {
      if (refetch) {
        await refetch();
      }
    };
    fetchData();
  }, [refetch]);

  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Question',
        accessorKey: 'question',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.question ?? '-'}</Typography>
      },
      {
        header: 'Visibility',
        accessorKey: 'visibility',
        cell: ({ row }) => {
          const visibility: FaqVisibility = row.original.visibility;
          return visibilityLabels[visibility] || 'Unknown';
        }
      },
      {
        header: 'Category',
        accessorKey: 'category',
        cell: ({ row }) => {
          const category: LegalCategory = row.original.category;
          return LegalCategory[category] || 'Unknown';
        }
      },
      {
        header: 'CreatedOn',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format('DD-MM-YYYY') ?? '-'}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ row }) => {
          const status: FaqStatus = row.original.status;
          const chipData = statusLabels[status] || { label: 'Unknown', color: 'default' };
          return <Chip color={chipData.color} label={chipData.label} size="small" variant="outlined" />;
        }
      },

      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/contents/help/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setfaqDeleteId(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete, handleClose, statusLabels, visibilityLabels]
  );

  return (
    <>
      {faqCountLoading || faqListLoading ? (
        <Loader />
      ) : (
        <HelpTable
          {...{
            data: faqList as HelpItem[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: faqListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: faqCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertHelpDelete refetch={refetch} id={faqDeleteId} title={faqDeleteId} open={open} handleClose={handleClose} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewHelp)(HelpListPage);
