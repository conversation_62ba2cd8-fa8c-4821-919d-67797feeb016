'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, Grid, TextField, Typography, Button, CircularProgress, Switch, Box } from '@mui/material';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useGetConfigurationsQuery, useUpdateConfigurationMutation } from 'redux/app/configuration/configurationApiSlice';
import { configurationValidationSchema } from '../../../validations/configuration';
import { useFormik } from 'formik';
import { Configuration } from 'types/configuration';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';

export default function ConfigurationEdit() {
  const { data: configurations, isLoading } = useGetConfigurationsQuery();
  const [updateConfiguration] = useUpdateConfigurationMutation();
  const { hasPermission } = useAuth();

  // 🟢 Local state to hold and update config values (keeps original order)
  const [configs, setConfigs] = useState<Configuration[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingValue, setEditingValue] = useState<string>('');

  const canEdit = hasPermission(PermissionKeys.UpdateConfiguration);

  // Initialize local state once when configurations are fetched
  useEffect(() => {
    if (configurations) {
      setConfigs(configurations);
    }
  }, [configurations]);

  const formik = useFormik({
    initialValues: { value: '' },
    validationSchema: configurationValidationSchema,
    onSubmit: async (values, { resetForm }) => {
      if (!editingId) return;

      await updateConfiguration({
        id: editingId,
        data: { value: values.value }
      }).unwrap();

      openSnackbar({
        open: true,
        message: 'Configuration updated successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);

      // ✅ Update only the edited config locally
      setConfigs((prevConfigs) => prevConfigs.map((c) => (c.id === editingId ? { ...c, value: values.value } : c)));

      setEditingId(null);
      resetForm();
    }
  });

  if (isLoading) return <CircularProgress />;
  if (!configs.length) return <div>No configurations found</div>;

  const handleEditClick = (config: Configuration) => {
    setEditingId(config.id);
    setEditingValue(config.value);
    formik.setFieldValue('value', config.value);
  };

  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditingValue(e.target.value);
    formik.setFieldValue('value', e.target.value);
  };

  const handleBooleanToggle = () => {
    const newValue = editingValue === 'true' ? 'false' : 'true';
    setEditingValue(newValue);
    formik.setFieldValue('value', newValue);
  };

  const isBooleanString = (value: string) => value === 'true' || value === 'false';

  return (
    <Box sx={{ mt: 2 }}>
      <Card sx={{ maxWidth: 800, mx: 'auto', p: 2 }}>
        <CardContent>
          <Typography variant="h5" sx={{ mb: 4 }}>
            Edit Configurations
          </Typography>

          <Grid container direction="column" spacing={2}>
            {configs.map((config) => {
              const isEditing = editingId === config.id;
              const isBooleanValue = isBooleanString(config.value);

              return (
                <Grid
                  key={config.id}
                  container
                  spacing={2}
                  alignItems="center"
                  wrap="wrap"
                  sx={{
                    borderBottom: '1px solid #e0e0e0',
                    pb: 1
                  }}
                >
                  <Grid item xs={12} sm={4}>
                    <Typography
                      sx={{
                        wordBreak: 'break-word',
                        fontWeight: 500,
                        color: 'text.secondary'
                      }}
                    >
                      {config.label}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={5}>
                    {isEditing ? (
                      isBooleanValue ? (
                        <Switch checked={editingValue === 'true'} onChange={handleBooleanToggle} color="primary" />
                      ) : (
                        <TextField
                          fullWidth
                          name="value"
                          value={editingValue}
                          onChange={handleValueChange}
                          error={formik.touched.value && Boolean(formik.errors.value)}
                          helperText={formik.touched.value && formik.errors.value}
                          size="small"
                        />
                      )
                    ) : (
                      <Typography sx={{ wordBreak: 'break-word' }}>{config.value}</Typography>
                    )}
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    {isEditing ? (
                      <Button
                        type="submit"
                        variant="contained"
                        color="primary"
                        size="small"
                        onClick={(e) => {
                          e.preventDefault();
                          formik.handleSubmit();
                        }}
                        sx={{ borderRadius: 5 }}
                      >
                        Save
                      </Button>
                    ) : (
                      canEdit && (
                        <Button
                          variant="outlined"
                          color="secondary"
                          size="small"
                          onClick={() => handleEditClick(config)}
                          sx={{ borderRadius: 5, mt: 1 }}
                        >
                          Edit
                        </Button>
                      )
                    )}
                  </Grid>
                </Grid>
              );
            })}
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}
