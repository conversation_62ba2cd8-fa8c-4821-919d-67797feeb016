'use client';

import Image from 'next/image';
import { <PERSON>, Button, Chip, Grid, Stack, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { useGetCollectionsByIdQuery, useUpdateCollectionsMutation } from 'redux/app/collections/collectionApiSlice';
import { Collection } from 'types/collection';
import Loader from 'components/Loader';
import { CollectionStatus } from 'enums/collection-status.enum';

export default function CollectionView({ collectionId }: { collectionId: string }) {
  const { data, isLoading, error, refetch } = useGetCollectionsByIdQuery({
    id: collectionId,
    filter: {
      include: [{ relation: 'featuredAsset' }, { relation: 'parent' }, { relation: 'childrens' }, { relation: 'taxCategory' }]
    }
  });

  const [updateCollection, { isLoading: isUpdating }] = useUpdateCollectionsMutation();

  const handleStatusUpdate = async (newStatus: CollectionStatus) => {
    if (!data?.id) return;
    await updateCollection({
      id: data.id,
      data: { status: newStatus }
    }).unwrap();

    await refetch();
  };

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching category</Typography>;

  return (
    <Grid container spacing={2.5}>
      {/* Left Panel */}
      <Grid item xs={12} sm={5} md={4} lg={4} xl={3}>
        <MainCard sx={{ height: 250 }}>
          <Stack spacing={2} alignItems="center">
            <Box
              sx={{
                width: 180,
                height: 180,
                bgcolor: 'secondary.200',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                borderRadius: 1
              }}
            >
              <Image
                src={data?.featuredAsset?.previewUrl || '/placeholder.png'}
                alt="collection-image"
                width={180}
                height={180}
                style={{ objectFit: 'cover', width: '100%', height: '100%' }}
              />
            </Box>
            <Chip label={!data?.parentId ? 'Root' : 'Child'} color={!data?.parentId ? 'success' : 'info'} size="small" />
            <Chip
              label={data.status?.toUpperCase() ?? 'UNKNOWN'}
              color={data.status === CollectionStatus.ACTIVE ? 'success' : data.status === CollectionStatus.REJECTED ? 'error' : 'warning'}
              variant="outlined"
              size="small"
            />
          </Stack>
        </MainCard>
      </Grid>

      {/* Right Panel */}
      <Grid item xs={12} sm={7} md={8} lg={8} xl={9}>
        <Stack spacing={2.5}>
          <MainCard title="Category Info" sx={{ overflowY: 'auto' }}>
            <Grid container spacing={1.5}>
              <Grid item xs={4} md={3}>
                <Typography color="text.secondary">Name</Typography>
              </Grid>
              <Grid item xs={8} md={9}>
                <Typography variant="h6">{data?.name}</Typography>
              </Grid>

              {data?.parent && (
                <>
                  <Grid item xs={4} md={3}>
                    <Typography color="text.secondary">Parent</Typography>
                  </Grid>
                  <Grid item xs={8} md={9}>
                    <Typography variant="h6">{data.parent.name}</Typography>
                  </Grid>
                </>
              )}

              {!!data.childrens?.length && (
                <>
                  <Grid item xs={4} md={3}>
                    <Typography color="text.secondary">Childrens</Typography>
                  </Grid>
                  <Grid item xs={8} md={9}>
                    <Stack direction="row" spacing={0.5} flexWrap="wrap">
                      {data.childrens.map((child: Collection, idx) => (
                        <Chip key={idx} label={child.name} variant="outlined" />
                      ))}
                    </Stack>
                  </Grid>
                </>
              )}

              <Grid item xs={4} md={3}>
                <Typography color="text.secondary">Tax Category</Typography>
              </Grid>
              <Grid item xs={8} md={9}>
                <Typography variant="h6">{data?.taxCategory?.name || '-'}</Typography>
              </Grid>

              {/* ✅ Status Field */}
              <Grid item xs={4} md={3}>
                <Typography color="text.secondary">Status</Typography>
              </Grid>
              <Grid item xs={8} md={9}>
                <Chip
                  label={data.status.charAt(0).toUpperCase() + data.status.slice(1)}
                  color={
                    data.status === CollectionStatus.ACTIVE ? 'success' : data.status === CollectionStatus.REJECTED ? 'error' : 'warning'
                  }
                  variant="outlined"
                  size="small"
                />
              </Grid>

              <Grid item xs={4} md={3}>
                <Typography color="text.secondary">Created On</Typography>
              </Grid>
              <Grid item xs={8} md={9}>
                <Typography variant="h6">{data?.createdOn ? new Date(data.createdOn).toLocaleString() : '-'}</Typography>
              </Grid>

              {/* ✅ Status Action Buttons */}
              {data.status === CollectionStatus.PENDING && (
                <>
                  <Grid item xs={4} md={3}>
                    <Typography color="text.secondary">Actions</Typography>
                  </Grid>
                  <Grid item xs={8} md={9}>
                    <Stack direction="row" spacing={2}>
                      <Button
                        variant="contained"
                        color="success"
                        onClick={() => handleStatusUpdate(CollectionStatus.ACTIVE)}
                        disabled={isUpdating}
                      >
                        Approve
                      </Button>
                      <Button
                        variant="contained"
                        color="error"
                        onClick={() => handleStatusUpdate(CollectionStatus.REJECTED)}
                        disabled={isUpdating}
                      >
                        Reject
                      </Button>
                    </Stack>
                  </Grid>
                </>
              )}
            </Grid>
          </MainCard>
        </Stack>
      </Grid>
    </Grid>
  );
}
