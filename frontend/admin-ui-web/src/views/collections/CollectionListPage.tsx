'use client';

import { useMemo, useState, MouseEvent, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import Loader from 'components/Loader';
import IconButton from 'components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import moment from 'moment-timezone';
import IndeterminateCheckbox from '../../components/third-party/react-table/IndeterminateCheckbox';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import { DEFAULT_DATE_FORMAT, ThemeMode } from 'config';
import { PermissionKeys } from 'enums/permission-keys.enum';

import { useGetCollectionCountQuery, useGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import AlertCollectionDelete from './AlertCollectionDelete';
import CollectionTable from './CollectionTable';
import { Collection } from 'types/collection';

const CollectionListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [collectionDeleteId, setCollectionDeleteId] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);

  const canCreate = hasPermission(PermissionKeys.CreateCollection);
  const canEdit = hasPermission(PermissionKeys.UpdateCollection);
  const canDelete = hasPermission(PermissionKeys.DeleteCollection);

  const {
    data: collectionList,
    isLoading: collectionListLoading,
    refetch
  } = useGetCollectionsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    ...convertPaginationToLoopback(pagination),
    include: [{ relation: 'featuredAsset' }, { relation: 'parent' }, { relation: 'taxCategory' }]
  });

  const { data: collectionCount, isLoading: collectionCountLoading } = useGetCollectionCountQuery({
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    ...convertPaginationToLoopback(pagination),
    include: [{ relation: 'featuredAsset' }]
  });

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);

  useEffect(() => {
    refetch?.();
  }, [refetch]);

  const columns = useMemo<ColumnDef<Collection>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Name',
        accessorKey: 'name',
        enableSorting: true,
        cell: ({ row }) => {
          const imageUrl = row.original?.featuredAsset?.previewUrl;
          const name = row.original?.name ?? '-';

          return (
            <Stack direction="row" alignItems="center" spacing={2}>
              {imageUrl && (
                <img
                  src={imageUrl}
                  alt="Featured Asset"
                  style={{
                    width: 40,
                    height: 40,
                    objectFit: 'cover',
                    borderRadius: 4
                  }}
                />
              )}
              <Typography>{name}</Typography>
            </Stack>
          );
        }
      },

      {
        header: 'Parent',
        accessorFn: (row) => row.parent?.name ?? '—',
        enableSorting: false,
        cell: ({ row }) => <Typography>{row.original.parent?.name ?? '—'}</Typography>
      },
      {
        header: 'Tax Category',
        accessorFn: (row) => row.taxCategory?.name ?? '—',
        enableSorting: false,
        cell: ({ row }) => <Typography>{row.original.taxCategory?.name ?? '—'}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'status',
        enableSorting: false,
        cell: ({ row }) => <Typography>{row.original.status ?? '-'}</Typography>
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format(DEFAULT_DATE_FORMAT) ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="flex-start" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/collections/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setCollectionDeleteId(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete, handleClose]
  );

  return (
    <>
      {collectionCountLoading || collectionListLoading ? (
        <Loader />
      ) : (
        <CollectionTable
          {...{
            data: collectionList as Collection[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: collectionListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: collectionCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertCollectionDelete
        refetch={refetch}
        id={collectionDeleteId}
        title={collectionList?.find((collection) => collection.id === collectionDeleteId)?.name ?? collectionDeleteId}
        open={open}
        handleClose={handleClose}
      />
    </>
  );
};

export default withPermission(PermissionKeys.ViewCollection)(CollectionListPage);
