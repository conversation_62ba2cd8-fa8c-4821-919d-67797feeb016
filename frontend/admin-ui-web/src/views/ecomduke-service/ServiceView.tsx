'use client';

import React, { useEffect } from 'react';
import { Grid, Typography, Box, Card, CardContent } from '@mui/material';
import { useGetServiceByIdQuery } from 'redux/app/service/serviceApiSlice';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';

interface ViewServiceProps {
  serviceId: string;
}

const ViewService = ({ serviceId }: ViewServiceProps) => {
  const { data: serviceData, isLoading, error, refetch } = useGetServiceByIdQuery({ id: serviceId });
  const { data: taxCategories = [] } = useGetTaxCategoriesQuery();

  useEffect(() => {
    refetch();
  }, [refetch]);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (error || !serviceData) return <Typography color="error">Error fetching service data</Typography>;

  // Match tax category name from taxCategoryId
  const taxCategory = taxCategories.find((tax) => tax.id === serviceData.taxCategoryId);
  const taxCategoryName = taxCategory ? taxCategory.name : 'N/A';

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Service Details
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Name" value={serviceData.name} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem
                  label="Price"
                  value={
                    serviceData.price != null && !isNaN(Number(serviceData.price))
                      ? `${serviceData.currency} ${Number(serviceData.price).toFixed(2)}`
                      : `${serviceData.currency} 0.00`
                  }
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <DisplayItem label="Tax Category" value={taxCategoryName} />
              </Grid>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Status" value={serviceData.isActive ? 'Active' : 'Inactive'} />
              </Grid>

              <Grid item xs={12}>
                <DisplayItem label="Description" value={serviceData.description || 'No description provided.'} isMultiline />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const DisplayItem = ({ label, value, isMultiline = false }: { label: string; value: string | number; isMultiline?: boolean }) => (
  <Box sx={{ mb: 1.5 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary',
        whiteSpace: isMultiline ? 'pre-line' : 'nowrap'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default ViewService;
