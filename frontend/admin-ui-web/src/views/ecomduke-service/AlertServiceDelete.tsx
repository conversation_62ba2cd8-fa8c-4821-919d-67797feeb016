'use client';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import DialogContent from '@mui/material/DialogContent';
import { Trash } from 'iconsax-react';

import Avatar from 'components/@extended/Avatar';
import { PopupTransition } from 'components/@extended/Transitions';
import { ThemeMode } from 'config';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useDeleteEcomServiceMutation } from 'redux/app/service/serviceApiSlice';

interface Props {
  id: string;
  name: string;
  open: boolean;
  handleClose: () => void;
  refetch: () => void;
}

export default function AlertServiceDelete({ id, name, open, handleClose, refetch }: Props) {
  const theme = useTheme();
  const [deleteService] = useDeleteEcomServiceMutation();

  const deleteHandler = async () => {
    await deleteService(id).unwrap();
    openSnackbar({
      open: true,
      message: 'Service deleted successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    handleClose();
    refetch();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      keepMounted
      TransitionComponent={PopupTransition}
      maxWidth="xs"
      aria-labelledby="service-delete-title"
      aria-describedby="service-delete-description"
    >
      <DialogContent sx={{ mt: 2, my: 1 }}>
        <Stack alignItems="center" spacing={3.5}>
          <Avatar
            color="error"
            sx={{
              width: 72,
              height: 72,
              fontSize: '1.75rem',
              color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
            }}
          >
            <Trash />
          </Avatar>

          <Stack spacing={2}>
            <Typography variant="h4" align="center">
              Are you sure you want to delete?
            </Typography>
            <Typography align="center">
              By deleting
              <Typography variant="subtitle1" component="span">
                {' '}
                {name}{' '}
              </Typography>
              this service will be permanently removed.
            </Typography>
          </Stack>

          <Stack direction="row" spacing={2} sx={{ width: 1 }}>
            <Button fullWidth onClick={handleClose} color="secondary" variant="outlined">
              Cancel
            </Button>
            <Button fullWidth color="error" variant="contained" onClick={deleteHandler} autoFocus>
              Delete
            </Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}
