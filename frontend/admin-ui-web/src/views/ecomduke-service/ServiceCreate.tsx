'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Container,
  Grid,
  TextField,
  Typography,
  MenuItem,
  InputLabel,
  Switch,
  FormControlLabel,
  Button
} from '@mui/material';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import { useCreateEcomServiceMutation, useUpdateEcomServiceMutation } from 'redux/app/service/serviceApiSlice';
import { SnackbarProps } from 'types/snackbar';
import { openSnackbar } from 'api/snackbar';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';
import { serviceValidationSchema } from '../../../validations/ecomdukeservice';

interface EcomDukeServiceFormProps {
  initialValues?: {
    name: string;
    description?: string;
    price: number;
    currency: string;
    taxCategoryId: string;
    isActive: boolean;
  };
  isEdit?: boolean;
  serviceId?: string;
  refetch?: () => void;
}

const currencyOptions = ['INR', 'USD', 'EUR'];

export default function EcomDukeServiceCreate({ initialValues, isEdit = false, serviceId, refetch }: EcomDukeServiceFormProps) {
  const router = useRouter();
  const [createService] = useCreateEcomServiceMutation();
  const [updateService] = useUpdateEcomServiceMutation();
  const { data: taxCategories = [] } = useGetTaxCategoriesQuery();

  const formik = useFormik({
    initialValues: {
      name: initialValues?.name || '',
      description: initialValues?.description || '',
      price: initialValues?.price || 0.0,
      currency: initialValues?.currency || 'INR',
      taxCategoryId: initialValues?.taxCategoryId || '',
      isActive: initialValues?.isActive ?? true
    },
    validationSchema: serviceValidationSchema,
    onSubmit: async (values) => {
      const payload = {
        ...values,
        price: parseFloat(values.price as any)
      };

      if (isEdit && serviceId) {
        await updateService({ id: serviceId, data: payload }).unwrap();
        openSnackbar({
          open: true,
          message: 'Service updated successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      } else {
        await createService(payload).unwrap();
        openSnackbar({
          open: true,
          message: 'Service created successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      }
      refetch?.();
      router.push('/ecomduke-service');
    }
  });

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            {isEdit ? 'Update Service' : 'Create Service'}
          </Typography>
          <form onSubmit={formik.handleSubmit} noValidate>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <InputLabel htmlFor="name">Service Name</InputLabel>
                <TextField
                  fullWidth
                  id="name"
                  name="name"
                  variant="outlined"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </Grid>

              <Grid item xs={12}>
                <InputLabel htmlFor="description">Description</InputLabel>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  id="description"
                  name="description"
                  variant="outlined"
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.description && Boolean(formik.errors.description)}
                  helperText={formik.touched.description && formik.errors.description}
                />
              </Grid>

              <Grid item xs={6}>
                <InputLabel htmlFor="price">Price</InputLabel>
                <TextField
                  fullWidth
                  type="number"
                  id="price"
                  name="price"
                  variant="outlined"
                  value={formik.values.price}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.price && Boolean(formik.errors.price)}
                  helperText={formik.touched.price && formik.errors.price}
                />
              </Grid>

              <Grid item xs={6}>
                <InputLabel htmlFor="currency">Currency</InputLabel>
                <TextField
                  select
                  fullWidth
                  id="currency"
                  name="currency"
                  variant="outlined"
                  value={formik.values.currency}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.currency && Boolean(formik.errors.currency)}
                  helperText={formik.touched.currency && formik.errors.currency}
                >
                  {currencyOptions.map((cur) => (
                    <MenuItem key={cur} value={cur}>
                      {cur}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <InputLabel htmlFor="taxCategoryId">Tax Category</InputLabel>
                <TextField
                  select
                  fullWidth
                  id="taxCategoryId"
                  name="taxCategoryId"
                  value={formik.values.taxCategoryId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.taxCategoryId && Boolean(formik.errors.taxCategoryId)}
                  helperText={formik.touched.taxCategoryId && formik.errors.taxCategoryId}
                >
                  <MenuItem value="" disabled>
                    Select tax category
                  </MenuItem>
                  {taxCategories.map((tax) => (
                    <MenuItem key={tax.id} value={tax.id}>
                      {tax.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.isActive}
                      onChange={(e) => formik.setFieldValue('isActive', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Active"
                />
              </Grid>

              <Grid item xs={12} display="flex" justifyContent="flex-end">
                <Button type="submit" variant="contained" color="primary">
                  {isEdit ? 'Update Service' : 'Create Service'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Container>
  );
}
