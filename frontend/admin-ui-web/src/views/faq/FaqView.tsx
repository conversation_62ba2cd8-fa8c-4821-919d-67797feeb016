import React, { useEffect } from 'react';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import { EditorState, convertFromRaw } from 'draft-js';
import dynamic from 'next/dynamic';
import { FaqPriority } from 'enums/faq-priority.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { EditorProps } from 'react-draft-wysiwyg';
import { useGetFaqByIdQuery } from 'redux/app/contents/faq/faqApiSlice';

const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as any), {
  ssr: false,
  loading: () => <p>Loading...</p>
});

const formatEnumKey = (key: string) => {
  return key
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

function ViewFaq({ faqId }: { faqId: string }) {
  const { data: faqData, isLoading, error, refetch } = useGetFaqByIdQuery({ id: faqId });

  useEffect(() => {
    refetch();
  }, []);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (error || !faqData) return <Typography>Error fetching FAQ</Typography>;

  let editorState = EditorState.createEmpty();
  const contentState = convertFromRaw(JSON.parse(faqData.answer));
  editorState = EditorState.createWithContent(contentState);

  const getFormattedEnumValue = (enumObj: any, value: number) => {
    const enumKey = Object.keys(enumObj).find((key) => enumObj[key] === value);
    return enumKey ? formatEnumKey(enumKey) : 'Unknown';
  };

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12} md={4}>
        <Card sx={{ p: 1, boxShadow: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <Typography variant="h4">FAQ Details</Typography>
            </Box>
            <Typography display="flex" flexDirection="column">
              <strong>Priority:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block'
                }}
              >
                {getFormattedEnumValue(FaqPriority, faqData.priority)}
              </Box>
            </Typography>
            <Typography display="flex" flexDirection="column" sx={{ mt: 1 }}>
              <strong>Category:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block'
                }}
              >
                {faqData.category}
              </Box>
            </Typography>
            <Typography display="flex" flexDirection="column" sx={{ mt: 1 }}>
              <strong>Status:</strong>
              <Box
                sx={{
                  p: 1,
                  backgroundColor: '#f5f5f5',
                  borderRadius: 1,
                  display: 'inline-block'
                }}
              >
                {getFormattedEnumValue(FaqStatus, faqData.status!)}
              </Box>
            </Typography>
            <Typography display="flex" flexDirection="column" sx={{ mt: 1 }}>
              <strong>Visibility:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block'
                }}
              >
                {getFormattedEnumValue(FaqVisibility, faqData.visibility!)}
              </Box>
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={8}>
        <Card sx={{ mb: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4">Question:</Typography>
            <Typography variant="h6">{faqData.question}</Typography>
          </CardContent>
        </Card>
        <Card sx={{ p: 1, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4">Answer:</Typography>
            <Box
              sx={{
                p: 2,
                border: '1px solid #ccc',
                borderRadius: 2,
                minHeight: 150,
                maxHeight: 300,
                overflow: 'auto',
                mt: 2
              }}
            >
              <Editor editorState={editorState} toolbarHidden readOnly />
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

export default ViewFaq;
