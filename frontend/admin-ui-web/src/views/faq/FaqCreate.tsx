'use client';

import { useFormik } from 'formik';
import dynamic from 'next/dynamic';
import { EditorProps } from 'react-draft-wysiwyg';
import { convertToRaw, EditorState, convertFromRaw } from 'draft-js';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { TextField, Button, Select, MenuItem, Box, Typography, CardContent, Card, Grid } from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { FaqPriority } from 'enums/faq-priority.enum';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import { faqValidationSchema } from '../../../validations/faq';
import { useCreateFaqMutation, useUpdateFaqMutation } from 'redux/app/contents/faq/faqApiSlice';
import { LegalCategory } from 'enums/legal-category.enum';

const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as unknown as React.FC<EditorProps>), {
  ssr: false
});
const categoryOptions = Object.values(LegalCategory);

interface FaqFormProps {
  initialValues?: {
    question: string;
    answer: string;
    priority: number;
    category: string;
    visibility: FaqVisibility;
    status: FaqStatus;
  };
  isEdit?: boolean;
  faqId?: string;
  refetch?: () => void;
}

function FaqCreate({ initialValues, isEdit = false, faqId, refetch }: FaqFormProps) {
  const router = useRouter();
  const [createFaq] = useCreateFaqMutation();
  const [updateFaq] = useUpdateFaqMutation();

  const formatEnumKey = (key: string) => {
    return key
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const initialEditorState =
    initialValues?.answer && typeof initialValues.answer === 'string'
      ? (() => {
          try {
            return EditorState.createWithContent(convertFromRaw(JSON.parse(initialValues.answer)));
          } catch {
            return EditorState.createEmpty();
          }
        })()
      : EditorState.createEmpty();

  const formik = useFormik({
    initialValues: {
      question: initialValues?.question || '',
      answer: initialEditorState,
      priority: initialValues?.priority || FaqPriority.Low,
      category: initialValues?.category || '',
      visibility: initialValues?.visibility ?? FaqVisibility.ALL,
      status: initialValues?.status ?? FaqStatus.ACTIVE
    },
    validationSchema: faqValidationSchema,
    onSubmit: async (values) => {
      if (values.answer instanceof EditorState) {
        const content = JSON.stringify(convertToRaw(values.answer.getCurrentContent()));

        const faqData = {
          question: values.question,
          answer: content,
          priority: values.priority,
          category: values.category,
          status: values.status,
          visibility: values.visibility
        };
        if (isEdit && faqId) {
          await updateFaq({ id: faqId, data: faqData }).unwrap();
          openSnackbar({
            open: true,
            message: 'FAQ Updated successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
          refetch?.();
          router.push('/contents/faq');
        } else {
          await createFaq(faqData).unwrap();
          openSnackbar({
            open: true,
            message: 'FAQ Created successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
          refetch?.();
          router.push('/contents/faq');
        }
      }
    }
  });

  return (
    <Card sx={{ maxWidth: '100%', mx: 'auto', mt: 2, p: 2, boxShadow: 3 }}>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {isEdit ? 'Edit FAQ' : 'Create FAQ'}
          </Typography>

          <TextField
            fullWidth
            variant="outlined"
            placeholder="Enter question here"
            name="question"
            value={formik.values.question}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.question && Boolean(formik.errors.question)}
            helperText={formik.touched.question && formik.errors.question}
            margin="dense"
          />

          <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
            FAQ Answer
          </Typography>
          <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 300 }}>
            <Editor
              editorState={formik.values.answer}
              onEditorStateChange={(editorState: any) => formik.setFieldValue('answer', editorState)}
              wrapperClassName="wrapper-class"
              editorClassName="editor-class"
              toolbarClassName="toolbar-class"
            />
          </Box>
          {formik.touched.answer && formik.errors.answer && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {String(formik.errors.answer)}
            </Typography>
          )}

          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Priority
              </Typography>
              <Select
                fullWidth
                name="priority"
                value={formik.values.priority}
                onChange={(event) => formik.setFieldValue('priority', Number(event.target.value))}
              >
                {Object.entries(FaqPriority)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {key}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Category
              </Typography>
              <Select
                fullWidth
                name="category"
                value={formik.values.category}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.category && Boolean(formik.errors.category)}
              >
                {categoryOptions.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    {cat}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.category && formik.errors.category && (
                <Typography variant="body2" color="error">
                  {formik.errors.category}
                </Typography>
              )}
            </Grid>

            <Grid item xs={6}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Visibility
              </Typography>
              <Select
                fullWidth
                name="visibility"
                value={formik.values.visibility}
                onChange={(event) => formik.setFieldValue('visibility', Number(event.target.value))}
              >
                {Object.entries(FaqVisibility)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {formatEnumKey(key)}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Status
              </Typography>
              <Select
                fullWidth
                name="status"
                value={formik.values.status}
                onChange={(event) => formik.setFieldValue('status', Number(event.target.value))}
              >
                {Object.entries(FaqStatus)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {formatEnumKey(key)}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button type="submit" variant="contained" color="primary">
              {isEdit ? 'Update FAQ' : 'Create FAQ'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

export default FaqCreate;
