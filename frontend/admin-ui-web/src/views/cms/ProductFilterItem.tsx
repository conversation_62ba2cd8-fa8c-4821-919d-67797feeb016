import { Box, Button, Grid, MenuItem, Select, TextField, Typography } from '@mui/material';
import { Save2 as Save } from 'iconsax-react';
import { FC, useEffect, useState } from 'react';
import MainCard from 'components/MainCard';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { SectionItem } from 'types/cms';

const fieldOptions = [
  { label: 'Price', value: 'price' },
  { label: 'Discount %', value: 'discountPercent' }
];

const operatorOptions = [
  { label: 'Equals', value: 'eq' },
  { label: 'Greater Than', value: 'gt' },
  { label: 'Less Than', value: 'lt' },
  { label: 'Between', value: 'between' }
];

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: React.Dispatch<React.SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const ProductFilterItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);
  const [filterField, setFilterField] = useState('price');
  const [operator, setOperator] = useState('lt');
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [isSaved, setIsSaved] = useState(true);

  useEffect(() => {
    // Parse filter from existing metadata
    const filters = data.metadata?.filter;
    if (Array.isArray(filters) && filters.length > 0) {
      const f = filters[0];
      setFilterField(f.field);
      setOperator(f.operator);
      setValue1(f.value);
      if (f.operator === 'between') setValue2(f.value[1] || '');
    }
  }, [data.metadata]);

  const handleSave = () => {
    const filterObj = {
      field: filterField,
      operator,
      value: operator === 'between' ? [value1, value2] : value1
    };

    const updated: SectionItem = {
      ...localData,
      metadata: {
        ...localData.metadata,
        filter: [filterObj],
        redirectUrl: localData.metadata?.redirectUrl || ''
      }
    };

    onChange(updated);
    setIsSaved(true);
    setFileMap({ ...fileMap });
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SingleFileUpload
            setFieldValue={(field: string, value: any): void => {
              setLocalData((prev) => ({ ...prev, imageUrl: value?.preview ?? '' }));
              setFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={fileMap[data.id ?? '']}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Card Title"
            value={localData.title || ''}
            onChange={(e) => {
              setLocalData({ ...localData, title: e.target.value });
              setIsSaved(false);
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Typography variant="subtitle2">Product Filter</Typography>
          <Box display="flex" gap={2} mt={1}>
            <Select
              value={filterField}
              onChange={(e) => {
                setFilterField(e.target.value);
                setIsSaved(false);
              }}
              size="small"
            >
              {fieldOptions.map((opt) => (
                <MenuItem key={opt.value} value={opt.value}>
                  {opt.label}
                </MenuItem>
              ))}
            </Select>

            <Select
              value={operator}
              onChange={(e) => {
                setOperator(e.target.value);
                setIsSaved(false);
              }}
              size="small"
            >
              {operatorOptions.map((opt) => (
                <MenuItem key={opt.value} value={opt.value}>
                  {opt.label}
                </MenuItem>
              ))}
            </Select>

            <TextField
              type="number"
              placeholder="Value"
              size="small"
              value={value1}
              onChange={(e) => {
                setValue1(e.target.value);
                setIsSaved(false);
              }}
            />

            {operator === 'between' && (
              <TextField
                type="number"
                placeholder="To"
                size="small"
                value={value2}
                onChange={(e) => {
                  setValue2(e.target.value);
                  setIsSaved(false);
                }}
              />
            )}
          </Box>
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Redirection URL (Optional)"
            value={localData.metadata?.redirectUrl || ''}
            onChange={(e) => {
              setLocalData((prev) => ({
                ...prev,
                metadata: {
                  ...prev.metadata,
                  redirectUrl: e.target.value
                }
              }));
              setIsSaved(false);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button variant="contained" color="primary" startIcon={<Save />} onClick={handleSave} disabled={isSaved}>
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
