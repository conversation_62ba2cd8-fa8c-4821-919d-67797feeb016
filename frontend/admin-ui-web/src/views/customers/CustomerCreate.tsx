'use client';

import React, { useEffect, useMemo, useState } from 'react';
import {
  Card,
  CardContent,
  Container,
  Grid,
  TextField,
  Typography,
  MenuItem,
  IconButton,
  Button,
  Select,
  InputLabel,
  InputAdornment
} from '@mui/material';
import { useFormik, FormikProvider, FieldArray, FormikErrors } from 'formik';
import { useRouter } from 'next/navigation';
import { AddCircle, CloseCircle } from 'iconsax-react';
import { AddressDto } from 'types/customer';
import { useCreateCustomerMutation, useUpdateCustomerByIdMutation } from 'redux/app/customer/customerApiSlice';
import { customerValidationSchema } from '../../../validations/customer';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { Gender, Status } from 'enums/customer.enum';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { Country, State } from 'country-state-city';

const addressTypeOptions = [
  { value: 'HOME', label: 'Home' },
  { value: 'WORK', label: 'Work' },
  { value: 'OTHER', label: 'Other' }
];

interface CustomerFormProps {
  initialValues?: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    addresses?: AddressDto[];
    status: Status.ACTIVE;
    dob?: string | null;
    gender: string;
  };
  isEdit?: boolean;
  customerId?: string;
  onSubmit?: (values: any) => Promise<void>;
  refetch: () => void;
}

export default function CustomerCreate({ initialValues, isEdit = false, customerId, refetch }: CustomerFormProps) {
  const router = useRouter();
  const [createCustomer] = useCreateCustomerMutation();
  const [updateCustomer] = useUpdateCustomerByIdMutation();
  const countries = useMemo(() => {
    const allCountries = Country.getAllCountries();
    return allCountries.sort((a, b) => a.name.localeCompare(b.name));
  }, []);

  const [states, setStates] = useState<{ name: string; isoCode: string }[]>([]);
  const formik = useFormik({
    initialValues: {
      firstName: initialValues?.firstName || '',
      lastName: initialValues?.lastName || '',
      email: initialValues?.email || '',
      phone: initialValues?.phone?.startsWith('+91') ? initialValues.phone.replace('+91', '').trim() : initialValues?.phone || '',
      dob: initialValues?.dob ? dayjs(initialValues?.dob).toISOString() : null,
      gender: initialValues?.gender,
      addresses: initialValues?.addresses?.length
        ? initialValues.addresses
        : [
            {
              addressLine1: '',
              addressLine2: '',
              city: '',
              state: '',
              zipCode: '',
              country: '',
              addressType: '',
              customerId: '',
              locality: '',
              name: '',
              phoneNumber: '',
              landmark: '',
              alternativePhoneNumber: ''
            }
          ]
    },
    validationSchema: customerValidationSchema,
    onSubmit: async (values) => {
      const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

      const formattedValues = {
        ...values,
        firstName: capitalize(values.firstName.trim()),
        lastName: capitalize(values.lastName.trim()),
        phone: '+91' + values.phone.trim()
      };

      if (isEdit && customerId) {
        await updateCustomer({ id: customerId, data: formattedValues }).unwrap();
        openSnackbar({
          open: true,
          message: 'Customer updated successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      } else {
        await createCustomer(formattedValues).unwrap();
        openSnackbar({
          open: true,
          message: 'Customer created successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      }
      refetch();
      router.push('/customers');
    }
  });
  useEffect(() => {
    if (formik.values.addresses[0]?.country) {
      const selectedCountry = countries.find((c) => c.name === formik.values.addresses[0]?.country);
      if (selectedCountry) {
        const stateList = State.getStatesOfCountry(selectedCountry.isoCode);
        setStates(stateList);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.addresses[0]?.country, countries]);

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Create Customer
          </Typography>

          <FormikProvider value={formik}>
            <form onSubmit={formik.handleSubmit} noValidate>
              <Grid container spacing={3}>
                {/* Name */}
                <Grid item xs={12} sm={6}>
                  <InputLabel htmlFor="firstName">First Name</InputLabel>
                  <TextField
                    fullWidth
                    id="firstName"
                    name="firstName"
                    variant="outlined"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                    helperText={formik.touched.firstName && formik.errors.firstName}
                    sx={{ borderRadius: '20px' }}
                    InputProps={{
                      style: { borderRadius: '8px' }
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <InputLabel htmlFor="lastName">Last Name</InputLabel>
                  <TextField
                    fullWidth
                    id="lastName"
                    name="lastName"
                    variant="outlined"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                    helperText={formik.touched.lastName && formik.errors.lastName}
                    sx={{ borderRadius: '20px' }}
                    InputProps={{
                      style: { borderRadius: '8px' }
                    }}
                  />
                </Grid>

                {/* Email */}
                <Grid item xs={12} sm={6}>
                  <InputLabel htmlFor="email">Email Address</InputLabel>
                  <TextField
                    fullWidth
                    id="email"
                    name="email"
                    type="email"
                    variant="outlined"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                    sx={{ borderRadius: '20px' }}
                    InputProps={{
                      style: { borderRadius: '8px' }
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <InputLabel htmlFor="phone">Phone</InputLabel>
                  <TextField
                    name="phone"
                    fullWidth
                    variant="outlined"
                    value={formik.values.phone}
                    onChange={(e) => {
                      const { value } = e.target;
                      if (/^\d{0,10}$/.test(value)) {
                        formik.setFieldValue('phone', value);
                      }
                    }}
                    onBlur={formik.handleBlur}
                    error={formik.touched.phone && Boolean(formik.errors.phone)}
                    helperText={formik.touched.phone && formik.errors.phone}
                    sx={{ borderRadius: '20px' }}
                    inputProps={{
                      maxLength: 10,
                      inputMode: 'numeric',
                      pattern: '[0-9]*'
                    }}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">+91</InputAdornment>,
                      style: { borderRadius: '8px' }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" color="text.secondary">
                    Gender
                  </Typography>
                  <Select
                    name="gender"
                    value={formik.values.gender}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.gender && Boolean(formik.errors.gender)}
                    fullWidth
                    displayEmpty
                    sx={{ borderRadius: '8px' }}
                  >
                    <MenuItem disabled value="">
                      Select Gender
                    </MenuItem>
                    <MenuItem value={Gender.Male}>Male</MenuItem>
                    <MenuItem value={Gender.Female}>Female</MenuItem>
                    <MenuItem value={Gender.Others}>Others</MenuItem>
                  </Select>
                  {formik.touched.gender && typeof formik.errors.gender === 'string' && (
                    <Typography color="error" variant="caption">
                      {formik.errors.gender}
                    </Typography>
                  )}
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" color="text.secondary">
                    Date of Birth
                  </Typography>
                  <Grid sx={{ display: 'flex' }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        name="dob"
                        value={formik.values.dob ? dayjs(formik.values.dob) : null}
                        onChange={(newValue) => {
                          formik.setFieldValue('dob', newValue);
                        }}
                        slotProps={{
                          textField: {
                            placeholder: 'DD/MM/YYYY',
                            fullWidth: true,
                            InputProps: {
                              style: { borderRadius: '8px' }
                            },
                            error: formik.touched.dob && Boolean(formik.errors.dob),
                            helperText: formik.touched.dob && formik.errors.dob,
                            onBlur: formik.handleBlur
                          }
                        }}
                      />
                    </LocalizationProvider>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="h5" gutterBottom sx={{ ml: 1 }}>
                    Addresses
                  </Typography>
                </Grid>

                <FieldArray
                  name="addresses"
                  render={(arrayHelpers) => (
                    <>
                      {formik.values.addresses?.map((address, index) => {
                        const touched = formik.touched.addresses?.[index] as Partial<AddressDto> | undefined;
                        const error = formik.errors.addresses?.[index] as FormikErrors<AddressDto> | undefined;

                        return (
                          <Grid
                            container
                            spacing={2}
                            key={index}
                            sx={{ mb: 2, p: 2, border: '1px solid #ddd', borderRadius: 2, ml: 4, mt: 2 }}
                          >
                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.name`}>Name</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.name`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.name`)}
                                error={Boolean(touched?.name && error?.name)}
                                helperText={touched?.name && error?.name}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.phoneNumber`}>Phone Number</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.phoneNumber`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.phoneNumber`)}
                                value={formik.values.addresses[index].phoneNumber}
                                onChange={(e) => {
                                  const { value } = e.target;
                                  if (/^\d{0,10}$/.test(value)) {
                                    formik.setFieldValue(`addresses.${index}.phoneNumber`, value);
                                  }
                                }}
                                onBlur={formik.handleBlur}
                                error={Boolean(touched?.phoneNumber && error?.phoneNumber)}
                                helperText={touched?.phoneNumber && error?.phoneNumber}
                                sx={{ borderRadius: '20px' }}
                                inputProps={{
                                  maxLength: 10,
                                  inputMode: 'numeric',
                                  pattern: '[0-9]*'
                                }}
                                InputProps={{
                                  startAdornment: <InputAdornment position="start">+91</InputAdornment>,
                                  style: { borderRadius: '8px' }
                                }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.locality`}>Locality</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.locality`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.locality`)}
                                error={Boolean(touched?.locality && error?.locality)}
                                helperText={touched?.locality && error?.locality}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.landmark`}>Landmark</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.landmark`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.landmark`)}
                                error={Boolean(touched?.landmark && error?.landmark)}
                                helperText={touched?.landmark && error?.landmark}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.addressLine1`}>Address Line 1</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.addressLine1`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.addressLine1`)}
                                error={Boolean(touched?.addressLine1 && error?.addressLine1)}
                                helperText={touched?.addressLine1 && error?.addressLine1}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.addressLine2`}>Address Line 2</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.addressLine2`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.addressLine2`)}
                                error={Boolean(touched?.addressLine2 && error?.addressLine2)}
                                helperText={touched?.addressLine2 && error?.addressLine2}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.alternativePhoneNumber`}>Alternative Phone Number</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.alternativePhoneNumber`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.alternativePhoneNumber`)}
                                value={formik.values.addresses[index].alternativePhoneNumber}
                                onChange={(e) => {
                                  const { value } = e.target;
                                  if (/^\d{0,10}$/.test(value)) {
                                    formik.setFieldValue(`addresses.${index}.alternativePhoneNumber`, value);
                                  }
                                }}
                                onBlur={formik.handleBlur}
                                error={Boolean(touched?.alternativePhoneNumber && error?.alternativePhoneNumber)}
                                helperText={touched?.alternativePhoneNumber && error?.alternativePhoneNumber}
                                sx={{ borderRadius: '20px' }}
                                inputProps={{
                                  maxLength: 10,
                                  inputMode: 'numeric',
                                  pattern: '[0-9]*'
                                }}
                                InputProps={{
                                  startAdornment: <InputAdornment position="start">+91</InputAdornment>,
                                  style: { borderRadius: '8px' }
                                }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="h6">Country</Typography>
                              <TextField
                                select
                                fullWidth
                                name={`addresses.${index}.country`}
                                value={formik.values.addresses[index]?.country || ''}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                error={Boolean(touched?.country && error?.country)}
                                helperText={touched?.country && error?.country}
                                sx={{
                                  mb: 2,
                                  '& .MuiOutlinedInput-root': { borderRadius: '10px' }
                                }}
                                SelectProps={{
                                  displayEmpty: true,
                                  MenuProps: {
                                    PaperProps: {
                                      style: {
                                        maxHeight: 300,
                                        width: 300
                                      }
                                    }
                                  }
                                }}
                              >
                                <MenuItem value="" disabled>
                                  Select country
                                </MenuItem>
                                {countries.map((country) => (
                                  <MenuItem key={country.isoCode} value={country.name}>
                                    {country.name}
                                  </MenuItem>
                                ))}
                              </TextField>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <Typography variant="h6">State</Typography>
                              <TextField
                                select
                                fullWidth
                                name={`addresses.${index}.state`}
                                value={formik.values.addresses[index]?.state || ''}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                error={Boolean(touched?.state && error?.state)}
                                helperText={touched?.state && error?.state}
                                sx={{
                                  mb: 2,
                                  '& .MuiOutlinedInput-root': { borderRadius: '10px' }
                                }}
                                SelectProps={{
                                  displayEmpty: true,
                                  MenuProps: {
                                    PaperProps: {
                                      style: {
                                        maxHeight: 300,
                                        width: 300
                                      }
                                    }
                                  }
                                }}
                              >
                                <MenuItem value="" disabled>
                                  Select state
                                </MenuItem>
                                {states.map((state) => (
                                  <MenuItem key={state.isoCode} value={state.name}>
                                    {state.name}
                                  </MenuItem>
                                ))}
                              </TextField>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.city`}>City</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.city`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.city`)}
                                error={Boolean(touched?.city && error?.city)}
                                helperText={touched?.city && error?.city}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.zipCode`}>Zip Code</InputLabel>
                              <TextField
                                fullWidth
                                id={`addresses.${index}.zipCode`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.zipCode`)}
                                error={Boolean(touched?.zipCode && error?.zipCode)}
                                helperText={touched?.zipCode && error?.zipCode}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <InputLabel htmlFor={`addresses.${index}.addressType`}>Address Type</InputLabel>
                              <TextField
                                select
                                fullWidth
                                id={`addresses.${index}.addressType`}
                                variant="outlined"
                                {...formik.getFieldProps(`addresses.${index}.addressType`)}
                                error={Boolean(touched?.addressType && error?.addressType)}
                                helperText={touched?.addressType && error?.addressType}
                                sx={{ borderRadius: '20px' }}
                                InputProps={{ style: { borderRadius: '8px' } }}
                              >
                                {addressTypeOptions.map((option) => (
                                  <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                  </MenuItem>
                                ))}
                              </TextField>
                            </Grid>

                            <Grid item xs={12} display="flex" justifyContent="flex-end" gap={1}>
                              {(formik.values.addresses ?? []).length > 1 && (
                                <IconButton color="error" onClick={() => arrayHelpers.remove(index)} aria-label="Remove Address">
                                  <CloseCircle />
                                </IconButton>
                              )}
                              {index === formik.values.addresses.length - 1 &&
                                ['addressLine1', 'city', 'state', 'zipCode', 'country', 'addressType'].every((field) =>
                                  Boolean(address[field as keyof typeof address])
                                ) && (
                                  <IconButton
                                    color="primary"
                                    onClick={() =>
                                      arrayHelpers.push({
                                        addressLine1: '',
                                        addressLine2: '',
                                        city: '',
                                        state: '',
                                        zipCode: '',
                                        country: '',
                                        addressType: ''
                                      })
                                    }
                                    aria-label="Add Address"
                                  >
                                    <AddCircle />
                                  </IconButton>
                                )}
                            </Grid>
                          </Grid>
                        );
                      })}
                    </>
                  )}
                />

                <Grid item xs={12} display="flex" justifyContent="flex-end">
                  <Button type="submit" variant="contained" color="primary">
                    {isEdit ? 'Update Customer' : 'Create Customer'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </FormikProvider>
        </CardContent>
      </Card>
    </Container>
  );
}
