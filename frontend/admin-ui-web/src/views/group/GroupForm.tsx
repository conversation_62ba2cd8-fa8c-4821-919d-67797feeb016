'use client';

import { useFormik } from 'formik';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { TextField, Button, Box, Grid } from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import React from 'react';
import MainCard from 'components/MainCard';
import { InputLabel } from '@mui/material';

import { useCreateGroupMutation } from 'redux/app/campaigns/groupApiSlice';
import { GroupSchema } from '../../../validations/group';

function GroupForm() {
  const router = useRouter();
  const [createGroup] = useCreateGroupMutation();

  const handleGroupSubmit = async (values: any) => {
    try {
      const groupData = {
        name: values.name
      };

      await createGroup(groupData as any).unwrap();

      openSnackbar({
        open: true,
        message: 'Group created successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);

      router.push('/marketing/groups');
    } catch {}
  };

  const formik = useFormik({
    initialValues: {
      name: ''
    },
    validationSchema: GroupSchema,
    onSubmit: handleGroupSubmit
  });

  return (
    <MainCard>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={4} padding={2}>
          <Grid item xs={12} sm={6}>
            {' '}
            <InputLabel sx={{ mb: 1 }}>Group Name</InputLabel>
            <TextField
              placeholder="Enter group name"
              fullWidth
              name="name"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.name && Boolean(formik.errors.name)}
              helperText={Boolean(formik.errors.name) && formik.touched.name && formik.errors.name}
            />
          </Grid>
        </Grid>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button type="submit" variant="contained" color="primary">
            Submit
          </Button>
        </Box>
      </form>
    </MainCard>
  );
}

export default GroupForm;
