'use client';
import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import DialogContent from '@mui/material/DialogContent';
import Avatar from 'components/@extended/Avatar';
import { PopupTransition } from 'components/@extended/Transitions';
import { ThemeMode } from 'config';
import { openSnackbar } from 'api/snackbar';
import { Trash } from 'iconsax-react';
import { SnackbarProps } from 'types/snackbar';
import { useDeleteGroupByIdMutation } from 'redux/app/campaigns/groupApiSlice';

interface Props {
  id: string;
  title: string;
  open: boolean;
  handleClose: () => void;
  refetch: () => void;
}

export default function AlertGroupDelete({ id, title, open, handleClose, refetch }: Props) {
  const theme = useTheme();
  const [deleteGroupById] = useDeleteGroupByIdMutation();

  const deletehandler = async () => {
    try {
      await deleteGroupById(id).unwrap();
      openSnackbar({
        open: true,
        message: 'Group deleted successfully',
        variant: 'alert',
        alert: {
          color: 'success'
        }
      } as SnackbarProps);
      handleClose();
      refetch();
    } catch {}
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      keepMounted
      TransitionComponent={PopupTransition}
      maxWidth="xs"
      aria-labelledby="column-delete-title"
      aria-describedby="column-delete-description"
    >
      <DialogContent sx={{ mt: 2, my: 1 }}>
        <Stack alignItems="center" spacing={3.5}>
          <Avatar
            color="error"
            sx={{
              width: 72,
              height: 72,
              fontSize: '1.75rem',
              color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
            }}
          >
            <Trash />
          </Avatar>
          <Stack spacing={2}>
            <Typography variant="h4" align="center">
              Are you sure you want to delete?
            </Typography>
            <Typography align="center">
              By deleting
              <Typography variant="subtitle1" component="span">
                {' '}
                {title}{' '}
              </Typography>
              this Group will be permanently removed.
            </Typography>
          </Stack>

          <Stack direction="row" spacing={2} sx={{ width: 1 }}>
            <Button fullWidth onClick={handleClose} color="secondary" variant="outlined">
              Cancel
            </Button>
            <Button fullWidth color="error" variant="contained" onClick={deletehandler} autoFocus>
              Delete
            </Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}
