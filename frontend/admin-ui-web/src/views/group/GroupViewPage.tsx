'use client';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import MainCard from 'components/MainCard';
import { Box, Grid } from '@mui/material';
import { useEffect } from 'react';
import Loader from 'components/Loader';
import { useGetGroupByIdQuery } from 'redux/app/campaigns/groupApiSlice';

interface GroupViewProps {
  id: string;
  refetch: () => void;
}

const DisplayItem = ({ label, value }: { label: string; value: string | number | undefined }) => (
  <Box>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default function GroupView({ id, refetch: refetchGroupList }: GroupViewProps) {
  const { data, isLoading, error, refetch } = useGetGroupByIdQuery({ id });

  useEffect(() => {
    refetch();
  }, [refetch]);

  if (isLoading) return <Loader />;
  if (error) return <Typography color="error">Error loading Group details.</Typography>;
  if (!id) return <Typography>No Group data available</Typography>;

  return (
    <Grid container spacing={2.5}>
      <Grid item xs={12} md={4}>
        <MainCard title="Group">
          <Stack spacing={1}>{data && <DisplayItem label="Name" value={`${data.name}`} />}</Stack>
        </MainCard>
      </Grid>
      <Grid item xs={12} md={8}>
        <MainCard
          title={
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="h5">Message</Typography>
            </Stack>
          }
        >
          <Box
            sx={{
              height: 220,
              overflowY: 'auto'
            }}
          ></Box>
        </MainCard>
      </Grid>
    </Grid>
  );
}
