'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, Typography, Box, Grid, Divider, Stack, Chip } from '@mui/material';
import Loader from 'components/Loader';
import { useGetFeatureByIdQuery } from 'redux/app/admin/ecomApiSlice';

interface FeatureViewProps {
  featureId: string;
}

function FeatureView({ featureId }: FeatureViewProps) {
  const { data, isLoading, error, refetch } = useGetFeatureByIdQuery({
    id: featureId,
    include: [{ relation: 'featureValues' }]
  });

  useEffect(() => {
    refetch?.();
  }, [refetch]);

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching feature</Typography>;

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Feature Details
            </Typography>
            <Divider />

            <Box sx={infoBoxWrapper}>
              <Typography variant="h6" sx={{ mb: 1, mt: 3 }}>
                Name
              </Typography>
              <Box sx={infoBoxStyle}>{data.name}</Box>
            </Box>

            <Box sx={infoBoxWrapper}>
              <Typography variant="h6" sx={{ mb: 1, mt: 3 }}>
                Category
              </Typography>
              <Box sx={infoBoxStyle}>{data.category ?? '-'}</Box>
            </Box>

            <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
              Values
            </Typography>
            {Array.isArray(data.featureValues) && data.featureValues.length > 0 ? (
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {data.featureValues.map((val) => {
                  const displayValue = val.value.toLowerCase() === 'true' ? 'Yes' : val.value.toLowerCase() === 'false' ? 'No' : val.value;

                  return <Chip key={val.id} label={displayValue} variant="outlined" />;
                })}
              </Stack>
            ) : (
              <Typography color="text.secondary">No values assigned</Typography>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

const infoBoxWrapper = {
  display: 'flex',
  flexDirection: 'column',
  width: '100%'
};

const infoBoxStyle = {
  p: 1,
  border: '1px solid',
  borderColor: 'divider',
  borderRadius: 1,
  backgroundColor: '#f5f5f5'
};

export default FeatureView;
