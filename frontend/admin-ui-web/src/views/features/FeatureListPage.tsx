'use client';

import { useMemo, useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { Add, Eye } from 'iconsax-react';
import moment from 'moment-timezone';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import Loader from 'components/Loader';
import { ThemeMode } from 'config';
import { Chip } from '@mui/material';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useGetFeaturesCountQuery, useGetFeaturesForListQuery } from 'redux/app/admin/ecomApiSlice';
import { Feature } from 'redux/app/types/plan.type';
import FeatureTable from './FeatureTable';

const FeatureListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const canCreate = hasPermission(PermissionKeys.CreateFeature);
  const canEdit = hasPermission(PermissionKeys.UpdateFeature);
  const canDelete = hasPermission(PermissionKeys.DeleteFeature);

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const [, setOpen] = useState<boolean>(false);
  const {
    data: featureList = [],
    isLoading: featureListLoading,
    refetch
  } = useGetFeaturesForListQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    include: [{ relation: 'featureValues' }],
    ...convertPaginationToLoopback(pagination)
  });

  const { data: featureCountData, isLoading: featureCountLoading } = useGetFeaturesCountQuery();

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);
  useEffect(() => {
    const fetchData = async () => {
      if (refetch) {
        await refetch();
      }
    };
    fetchData();
  }, [refetch]);

  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Feature Name',
        accessorKey: 'name',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.name ?? '-'}</Typography>
      },
      {
        header: 'Feature Values',
        accessorKey: 'featureValues',
        cell: ({ row }) => {
          const values = row.original.featureValues || [];
          return (
            <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
              {values.slice(0, 3).map((v: any) => {
                // coerce boolean‐like string or actual boolean to Yes/No
                const display = v.value === true || v.value === 'true' ? 'Yes' : v.value === false || v.value === 'false' ? 'No' : v.value;
                return (
                  <Chip
                    key={v.id}
                    label={display}
                    size="small"
                    variant="outlined"
                    sx={{
                      maxWidth: 120,
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  />
                );
              })}
              {values.length > 3 && (
                <Chip
                  label={`+${values.length - 3} more`}
                  size="small"
                  variant="outlined"
                  color="default"
                  sx={{
                    maxWidth: 120,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                />
              )}
            </Stack>
          );
        }
      },
      {
        header: 'Category',
        accessorKey: 'category',
        cell: ({ row }) => <Typography>{row.original?.category ?? '-'}</Typography>
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format('DD-MM-YYYY') ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );
          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [theme, router, canEdit, canDelete, handleClose]
  );

  return (
    <>
      {featureCountLoading || featureListLoading ? (
        <Loader />
      ) : (
        <FeatureTable
          {...{
            data: featureList as Feature[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: featureListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: featureCountData?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
    </>
  );
};

export default withPermission(PermissionKeys.ViewFeature)(FeatureListPage);
