'use client';

import { useFormik } from 'formik';
import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  Chip,
  CircularProgress,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  TextField,
  Typography
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useCreatePromoCodeMutation, useUpdatePromoCodeMutation } from 'redux/app/promo-code/promoApiSlice';
import { PromoCodeFormProps, PromoCodePayload } from 'types/promo';
import { DiscountType, PromoApplicability, PromoTag } from 'enums/promo-code.enum';
import { promoValidationSchema } from '../../../validations/promo';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useLazyGetproductsQuery } from 'redux/app/products/productApiSlice';
import { useEffect, useMemo, useState } from 'react';
import { AutoCompleteOption } from 'types/common';
import { Product } from 'types/product';
import { useLazyGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { Collection } from 'types/collection';

function PromoCodeCreate({ isEdit = false, promoCodeId, initialValues, refetch }: PromoCodeFormProps) {
  const router = useRouter();
  const [createPromoCode] = useCreatePromoCodeMutation();
  const [updatePromoCode] = useUpdatePromoCodeMutation();
  const [productInput, setProductInput] = useState('');
  const [getProducts, { data: products = [], isLoading: productsLoading }] = useLazyGetproductsQuery();
  const [collectionInput, setCollectionInput] = useState('');
  const [getCollections, { data: collections = [], isLoading: collectionsLoading }] = useLazyGetCollectionsQuery();

  const handleFormSubmit = async (values: Partial<PromoCodePayload>) => {
    let allCategoryIds: string[] = [];

    if (values.appliesTo === PromoApplicability.ALL_PRODUCTS) {
      allCategoryIds = Array.from(new Set(collections.map((collection) => collection.id)));
    }
    const payload = {
      code: values.code,
      type: values.type,
      value: values.value !== undefined ? Number(values.value) : undefined,
      minCartValue: values.minCartValue !== undefined ? Number(values.minCartValue) : undefined,
      maxDiscountCap: values.maxDiscountCap !== undefined ? Number(values.maxDiscountCap) : undefined,
      usageLimitPerUser: values.usageLimitPerUser ?? 0,
      usageLimitTotal: values.usageLimitTotal ?? 0,
      isActive: values.isActive ?? true,
      appliesTo: values.appliesTo,
      validFrom: values.validFrom,
      validTill: values.validTill,
      visibility: values.visibility,
      productIds: values.productIds ?? [],
      collectionIds: values.appliesTo === PromoApplicability.ALL_PRODUCTS ? allCategoryIds : (values.categoryIds ?? []),
      isFirstTimeUserOnly: values.isFirstTimeUserOnly,
      isRepeatUserOnly: values.isRepeatUserOnly
    };

    if (isEdit && promoCodeId) {
      await updatePromoCode({ id: promoCodeId, data: payload }).unwrap();
      openSnackbar({
        open: true,
        message: 'Promo Code updated successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
    } else {
      await createPromoCode(payload).unwrap();
      openSnackbar({
        open: true,
        message: 'Promo Code created successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
    }
    refetch?.();
    router.push('/promo-code');
  };

  const formik = useFormik<PromoCodePayload>({
    initialValues: {
      code: initialValues?.code || '',
      type: initialValues?.type || DiscountType.FLAT,
      value: initialValues?.value ? Number(initialValues.value) : 0,
      minCartValue: initialValues?.minCartValue ? Number(initialValues.minCartValue) : 0,
      maxDiscountCap: initialValues?.maxDiscountCap ? Number(initialValues.maxDiscountCap) : undefined,
      usageLimitPerUser: initialValues?.usageLimitPerUser ?? 0,
      usageLimitTotal: initialValues?.usageLimitTotal ?? 0,
      isFirstTimeUserOnly: initialValues?.isFirstTimeUserOnly ?? false,
      isRepeatUserOnly: initialValues?.isRepeatUserOnly ?? false,
      validFrom: initialValues?.validFrom || '',
      validTill: initialValues?.validTill || '',
      appliesTo: initialValues?.appliesTo || PromoApplicability.ALL_PRODUCTS,
      visibility: initialValues?.visibility || PromoTag.PUBLIC,
      productIds: initialValues?.promoCodeProducts?.map((p) => p.productId) ?? initialValues?.productIds ?? [],
      categoryIds: initialValues?.promoCodeCollections?.map((c) => c.collectionId) ?? initialValues?.categoryIds ?? [],
      isActive: initialValues?.isActive ?? true
    },
    validationSchema: promoValidationSchema,
    onSubmit: handleFormSubmit
  });

  useEffect(() => {
    getProducts({ limit: 100 }).then((result) => {});
  }, []);

  const productOptions: AutoCompleteOption[] = useMemo(() => {
    return products.map((item: Product) => ({
      label: item.name,
      value: item.id
    }));
  }, [products]);

  const searchProducts = async (query: string) => {
    const result: Product[] = await getProducts({
      where: {
        name: { ilike: `%${query}%` }
      }
    }).unwrap();
    return result;
  };

  useEffect(() => {
    if (productInput.length >= 2) {
      const timer = setTimeout(() => {
        searchProducts(productInput);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [productInput]);

  useEffect(() => {
    getCollections({ limit: 100 }).then(() => {});
  }, []);

  const collectionOptions: AutoCompleteOption[] = useMemo(() => {
    return collections.map((item: Collection) => ({
      label: item.name,
      value: item.id
    }));
  }, [collections]);

  const searchCollections = async (query: string) => {
    const result: Collection[] = await getCollections({
      where: {
        name: { ilike: `%${query}%` }
      }
    }).unwrap();
    return result;
  };

  useEffect(() => {
    if (collectionInput.length >= 2) {
      const timer = setTimeout(() => {
        searchCollections(collectionInput);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [collectionInput]);

  return (
    <Card sx={{ maxWidth: '100%', mx: 'auto', mt: 2, p: 2, boxShadow: 3 }}>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            Create Promo Code
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary" sx={{ marginBottom: 1 }}>
                Promo Code
              </Typography>
              <TextField
                fullWidth
                placeholder="Enter Promo Code"
                name="code"
                value={formik.values.code}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.code && Boolean(formik.errors.code)}
                helperText={formik.touched.code && formik.errors.code}
                InputProps={{ style: { borderRadius: '8px' } }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary" sx={{ marginBottom: 1 }}>
                Discount Type
              </Typography>
              <TextField
                fullWidth
                select
                name="type"
                value={formik.values.type}
                onChange={formik.handleChange}
                InputProps={{ style: { borderRadius: '8px' } }}
              >
                {Object.values(DiscountType).map((value) => (
                  <MenuItem key={value} value={value}>
                    {value.charAt(0) + value.slice(1).toLowerCase()} {/* Capitalize the first letter */}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                Discount Value
              </Typography>
              <TextField
                fullWidth
                name="value"
                placeholder="Enter discount value"
                value={formik.values.value === 0 ? '' : formik.values.value}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^[1-9]\d*$/.test(value)) {
                    formik.setFieldValue('value', value === '' ? 0 : parseFloat(value));
                  }
                }}
                onBlur={formik.handleBlur}
                error={!!formik.touched.value && !!formik.errors.value}
                helperText={formik.touched.value && formik.errors.value}
                margin="dense"
                inputProps={{
                  min: 0,
                  step: 0.1
                }}
                InputProps={{
                  style: { borderRadius: '8px' }
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                Maximum Discount
              </Typography>
              <TextField
                fullWidth
                name="maxDiscountCap"
                placeholder="Enter maximum discount"
                value={formik.values.maxDiscountCap === 0 ? '' : formik.values.maxDiscountCap}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^[1-9]\d*$/.test(value)) {
                    formik.setFieldValue('maxDiscountCap', value === '' ? 0 : parseFloat(value));
                  }
                }}
                onBlur={formik.handleBlur}
                error={!!formik.touched.maxDiscountCap && !!formik.errors.maxDiscountCap}
                helperText={formik.touched.maxDiscountCap && formik.errors.maxDiscountCap}
                margin="dense"
                inputProps={{
                  min: 0,
                  step: 0.1
                }}
                InputProps={{
                  style: { borderRadius: '8px' }
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                Minimum Cart Value
              </Typography>
              <TextField
                fullWidth
                name="minCartValue"
                placeholder="Enter minimum cart value"
                value={formik.values.minCartValue === 0 ? '' : formik.values.minCartValue}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^[1-9]\d*$/.test(value)) {
                    formik.setFieldValue('minCartValue', value === '' ? 0 : parseFloat(value));
                  }
                }}
                onBlur={formik.handleBlur}
                error={!!formik.touched.minCartValue && !!formik.errors.minCartValue}
                helperText={formik.touched.minCartValue && formik.errors.minCartValue}
                margin="dense"
                inputProps={{
                  min: 0,
                  step: 0.1
                }}
                InputProps={{
                  style: { borderRadius: '8px' }
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary" sx={{ marginBottom: 1 }}>
                Usage Limit Per User
              </Typography>
              <TextField
                fullWidth
                name="usageLimitPerUser"
                placeholder="Enter usage limit per user"
                value={formik.values.usageLimitPerUser === 0 ? '' : formik.values.usageLimitPerUser}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^[1-9]\d*$/.test(value)) {
                    formik.setFieldValue('usageLimitPerUser', value === '' ? 0 : parseInt(value));
                  }
                }}
                onBlur={formik.handleBlur}
                error={!!formik.touched.usageLimitPerUser && !!formik.errors.usageLimitPerUser}
                helperText={formik.touched.usageLimitPerUser && formik.errors.usageLimitPerUser}
                inputProps={{
                  min: 1,
                  step: 1
                }}
                InputProps={{
                  style: { borderRadius: '8px' }
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                Total Usage Limit
              </Typography>
              <TextField
                fullWidth
                name="usageLimitTotal"
                placeholder="Enter total usage limit"
                value={formik.values.usageLimitTotal === 0 ? '' : formik.values.usageLimitTotal}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^[1-9]\d*$/.test(value)) {
                    formik.setFieldValue('usageLimitTotal', value === '' ? 0 : parseInt(value, 10));
                  }
                }}
                onBlur={formik.handleBlur}
                error={!!formik.touched.usageLimitTotal && !!formik.errors.usageLimitTotal}
                helperText={formik.touched.usageLimitTotal && formik.errors.usageLimitTotal}
                margin="dense"
                InputProps={{
                  style: { borderRadius: '8px' },
                  inputProps: {
                    min: 1
                  }
                }}
                InputLabelProps={{
                  shrink: true
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary" sx={{ marginBottom: 1 }}>
                Promo Tag
              </Typography>
              <TextField
                fullWidth
                select
                name="visibility"
                value={formik.values.visibility}
                onChange={formik.handleChange}
                InputProps={{ style: { borderRadius: '8px' } }}
              >
                {Object.values(PromoTag).map((value) => (
                  <MenuItem key={value} value={value}>
                    {value.replace('_', ' ')}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary" sx={{ marginBottom: 1 }}>
                Applies To
              </Typography>
              <TextField
                fullWidth
                select
                name="appliesTo"
                value={formik.values.appliesTo}
                onChange={formik.handleChange}
                InputProps={{ style: { borderRadius: '8px' } }}
              >
                {Object.values(PromoApplicability).map((value) => (
                  <MenuItem key={value} value={value}>
                    {value.replace('_', ' ')}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {formik.values.appliesTo === PromoApplicability.SELECTED_PRODUCTS && (
              <Grid item xs={12} md={6}>
                <InputLabel sx={{ mb: 1 }}>Applicable Products</InputLabel>
                <Autocomplete
                  freeSolo
                  multiple
                  options={productOptions}
                  getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                  loading={productsLoading}
                  onInputChange={(event, newInputValue) => {
                    setProductInput(newInputValue);
                  }}
                  onChange={(event, newValue: (string | AutoCompleteOption)[]) => {
                    formik.setFieldValue(
                      'productIds',
                      newValue.map((item) => (typeof item === 'string' ? item : item.value))
                    );
                  }}
                  value={
                    formik.values.productIds?.map(
                      (value) => productOptions.find((option) => option.value === value) || { label: value, value }
                    ) ?? []
                  }
                  filterSelectedOptions
                  renderTags={(value, getTagProps) => {
                    const maxToShow = 3;
                    return [
                      ...value
                        .slice(0, maxToShow)
                        .map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={typeof option === 'string' ? option : option.label}
                            {...getTagProps({ index })}
                            key={index}
                          />
                        )),
                      value.length > maxToShow && <Chip key="more" label={`+${value.length - maxToShow} more`} disabled />
                    ];
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Search Products"
                      placeholder="Type at least 3 characters..."
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {productsLoading && <CircularProgress color="inherit" size={24} />}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                        style: { borderRadius: 8 }
                      }}
                      onBlur={formik.handleBlur}
                      error={formik.touched.productIds && Boolean(formik.errors.productIds)}
                      name="productIds"
                    />
                  )}
                />

                {formik.touched.productIds && formik.errors.productIds && <FormHelperText error>{formik.errors.productIds}</FormHelperText>}
              </Grid>
            )}

            {formik.values.appliesTo === PromoApplicability.SELECTED_CATEGORIES && (
              <Grid item xs={12} md={6}>
                <InputLabel sx={{ mb: 1 }}>Applicable Categories</InputLabel>
                <Autocomplete
                  freeSolo
                  multiple
                  options={collectionOptions}
                  getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                  loading={collectionsLoading}
                  onInputChange={(event, newInputValue) => {
                    setCollectionInput(newInputValue);
                  }}
                  onChange={(event, newValue: (string | AutoCompleteOption)[]) => {
                    formik.setFieldValue(
                      'categoryIds',
                      newValue.map((item) => (typeof item === 'string' ? item : item.value))
                    );
                  }}
                  value={
                    formik.values.categoryIds?.map((value) => collectionOptions.find((option) => option.value === value) || value) ?? []
                  }
                  filterSelectedOptions
                  renderTags={(value, getTagProps) => {
                    const maxToShow = 3;
                    return [
                      ...value
                        .slice(0, maxToShow)
                        .map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={typeof option === 'string' ? option : option.label}
                            {...getTagProps({ index })}
                            key={index}
                          />
                        )),
                      value.length > maxToShow && <Chip key="more" label={`+${value.length - maxToShow} more`} disabled />
                    ];
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Search Categories"
                      placeholder="Type at least 2 characters..."
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {collectionsLoading && <CircularProgress color="inherit" size={20} />}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                        style: { borderRadius: 8 }
                      }}
                      onBlur={formik.handleBlur}
                      error={formik.touched.categoryIds && Boolean(formik.errors.categoryIds)}
                      name="categoryIds"
                    />
                  )}
                />

                {formik.touched.categoryIds && formik.errors.categoryIds && (
                  <FormHelperText error>{formik.errors.categoryIds}</FormHelperText>
                )}
              </Grid>
            )}

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                Start Date
              </Typography>
              <Grid sx={{ display: 'flex' }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DemoContainer components={['DatePicker']} sx={{ width: '100%', flexDirection: 'column !important' }}>
                    <DatePicker
                      name="validFrom"
                      value={formik.values.validFrom ? dayjs(formik.values.validFrom) : null}
                      onChange={(newValue) => {
                        formik.setFieldValue('validFrom', newValue);
                      }}
                      format="DD/MM/YYYY"
                      minDate={dayjs()}
                      slotProps={{
                        textField: {
                          placeholder: 'DD/MM/YYYY',
                          InputProps: {
                            style: { borderRadius: '8px' }
                          }
                        }
                      }}
                    />
                  </DemoContainer>
                </LocalizationProvider>
              </Grid>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                End Date
              </Typography>
              <Grid sx={{ display: 'flex' }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DemoContainer components={['DatePicker']} sx={{ width: '100%', flexDirection: 'column !important' }}>
                    <DatePicker
                      name="validTill"
                      value={formik.values.validTill ? dayjs(formik.values.validTill) : null}
                      onChange={(newValue) => {
                        formik.setFieldValue('validTill', newValue);
                      }}
                      format="DD/MM/YYYY"
                      minDate={dayjs()}
                      slotProps={{
                        textField: {
                          placeholder: 'DD/MM/YYYY',
                          InputProps: {
                            style: { borderRadius: '8px' }
                          }
                        }
                      }}
                    />
                  </DemoContainer>
                </LocalizationProvider>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={<Checkbox name="isFirstTimeUserOnly" checked={formik.values.isFirstTimeUserOnly} onChange={formik.handleChange} />}
                label="Applicable to first-time users only"
              />
              <FormControlLabel
                control={<Checkbox name="isRepeatUserOnly" checked={formik.values.isRepeatUserOnly} onChange={formik.handleChange} />}
                label="Applicable to repeat users only"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formik.values.isActive}
                    onChange={(e) => formik.setFieldValue('isActive', e.target.checked)}
                    name="isActive"
                  />
                }
                label="Is Active"
              />
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button type="submit" variant="contained" color="primary">
              {isEdit ? 'Update' : 'Create'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

export default PromoCodeCreate;
