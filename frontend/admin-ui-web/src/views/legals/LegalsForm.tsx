'use client';

import { useFormik } from 'formik';
import dynamic from 'next/dynamic';
import { EditorProps } from 'react-draft-wysiwyg';
import { convertToRaw, EditorState, convertFromRaw } from 'draft-js';
import { useEffect } from 'react';
import { Box, Button, Card, CardContent, MenuItem, Select, Typography } from '@mui/material';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useRouter } from 'next/navigation';
import { LegalCategory, Legals, LegalVisibility } from 'enums/legal-category.enum';
import { legalsValidationSchema } from '../../../validations/legals';
import { useCreateLegalMutation, useUpdateLegalMutation } from 'redux/app/contents/legals/legalsApiSlice';

const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as any), {
  ssr: false
});

interface LegalFormProps {
  initialValues?: {
    data: string;
    category: string;
    type?: string;
    visibility?: number;
  };
  isEdit?: boolean;
  legalsId?: string;
  refetch?: () => Promise<void> | any;
}

function LegalsForm({ initialValues, isEdit = false, legalsId, refetch }: LegalFormProps) {
  const [createLegals] = useCreateLegalMutation();
  const [updateLegals] = useUpdateLegalMutation();
  const router = useRouter();
  const initialEditorState =
    initialValues?.data && typeof initialValues.data === 'string'
      ? (() => {
          try {
            return EditorState.createWithContent(convertFromRaw(JSON.parse(initialValues.data)));
          } catch {
            return EditorState.createEmpty();
          }
        })()
      : EditorState.createEmpty();

  const formik = useFormik({
    initialValues: {
      data: initialEditorState,
      category: initialValues?.category || '',
      type: initialValues?.type || '',
      visibility: initialValues?.visibility || ''
    },

    validationSchema: legalsValidationSchema,
    onSubmit: async (values) => {
      if (values.data instanceof EditorState) {
        const content = JSON.stringify(convertToRaw(values.data.getCurrentContent()));

        const payload = {
          data: content,
          category: values.category as LegalCategory,
          type: values.type as Legals,
          visibility: values.visibility as LegalVisibility
        };

        if (isEdit && legalsId) {
          await updateLegals({ id: legalsId, body: payload }).unwrap();
          openSnackbar({
            open: true,
            message: 'Legals Section updated successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
        } else {
          await createLegals(payload).unwrap();
          openSnackbar({
            open: true,
            message: 'Legals Section created successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
        }
        refetch;
        router.push('/contents/legals');
      }
    }
  });

  useEffect(() => {
    if (refetch) refetch();
  }, [refetch]);

  return (
    <Card sx={{ maxWidth: '100%', mx: 'auto', mt: 2, p: 2, boxShadow: 3 }}>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {isEdit ? 'Edit Legal Section' : 'Create Legal Section'}
          </Typography>

          <Typography variant="h6" sx={{ mb: 1 }}>
            Content
          </Typography>
          <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 300 }}>
            <Editor
              editorState={formik.values.data}
              onEditorStateChange={(editorState) => formik.setFieldValue('data', editorState)}
              placeholder="Enter the content"
            />
          </Box>
          {formik.touched.data && formik.errors.data && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {String(formik.errors.data)}
            </Typography>
          )}

          <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
            Category
          </Typography>
          <Select fullWidth name="category" value={formik.values.category} onChange={formik.handleChange} displayEmpty>
            <MenuItem value="" disabled>
              Select Category
            </MenuItem>
            {Object.entries(LegalCategory).map(([key, value]) => (
              <MenuItem key={key} value={value}>
                {value}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.category && formik.errors.category && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {formik.errors.category}
            </Typography>
          )}
          <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
            Type
          </Typography>
          <Select fullWidth name="type" value={formik.values.type} onChange={formik.handleChange} displayEmpty>
            <MenuItem value="" disabled>
              Select Type
            </MenuItem>
            {Object.entries(Legals).map(([key, value]) => (
              <MenuItem key={key} value={value}>
                {value}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.type && formik.errors.type && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {formik.errors.type}
            </Typography>
          )}

          <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
            Visibility
          </Typography>
          <Select fullWidth name="visibility" value={formik.values.visibility} onChange={formik.handleChange} displayEmpty>
            <MenuItem value="" disabled>
              Select Visibility
            </MenuItem>
            {Object.keys(LegalVisibility)
              .filter((key) => isNaN(Number(key)))
              .map((key) => (
                <MenuItem key={key} value={LegalVisibility[key as keyof typeof LegalVisibility]}>
                  {key}
                </MenuItem>
              ))}
          </Select>
          {formik.touched.visibility && formik.errors.visibility && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {formik.errors.visibility}
            </Typography>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button type="submit" variant="contained" color="primary">
              {isEdit ? 'Update' : 'Create'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

export default LegalsForm;
