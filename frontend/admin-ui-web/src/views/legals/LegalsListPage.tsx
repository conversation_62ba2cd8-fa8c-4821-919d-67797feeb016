'use client';

import { useMemo, useState, useCallback, useEffect, MouseEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import IconButton from 'components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import moment from 'moment-timezone';
import { SortingState, ColumnFiltersState, PaginationState, ColumnDef } from '@tanstack/react-table';
import Loader from 'components/Loader';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import withPermission from 'hoc/withPermission';
import { useAuth } from 'contexts/AuthContext';
import { PermissionKeys } from 'enums/permission-keys.enum';
import draftToHtml from 'draftjs-to-html';
import { ThemeMode } from 'config';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import LegalsTable from './LegalsTable';
import AlertLegalsDelete from './AlertLegalsDelete';
import { LegalVisibility } from 'enums/legal-category.enum';
import { useGetLegalCountQuery, useGetLegalsQuery } from 'redux/app/contents/legals/legalsApiSlice';
import { LegalType } from 'enums/legal.enum';

const LegalsListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [deleteId, setDeleteId] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const getVisibilityLabel = (value?: LegalVisibility): string => {
    if (value === undefined || value === null) return 'Unknown';
    return LegalVisibility[value];
  };

  const canCreate = hasPermission(PermissionKeys.CreateLegals);
  const canEdit = hasPermission(PermissionKeys.UpdateLegals);
  const canDelete = hasPermission(PermissionKeys.DeleteLegals);

  const {
    data: legals,
    isLoading,
    refetch
  } = useGetLegalsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['category', 'data']),
    ...convertPaginationToLoopback(pagination)
  });

  const { data: countData, isLoading: isCountLoading } = useGetLegalCountQuery();

  const handleClose = useCallback(() => setOpen(false), []);
  const handleOpen = useCallback(() => setOpen(true), []);

  useEffect(() => {
    if (refetch) refetch();
  }, [refetch]);

  const columns = useMemo<ColumnDef<LegalType>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Category',
        accessorKey: 'category',
        cell: ({ row }) => <Typography>{row.original.category}</Typography>
      },
      {
        header: 'Visibility',
        accessorKey: 'visibility',
        cell: ({ row }) => {
          const visibility = row.original.visibility;
          return <Typography>{getVisibilityLabel(visibility)}</Typography>;
        }
      },
      {
        header: 'Legals Type',
        accessorKey: 'type',
        cell: ({ row }) => <Typography>{row.original.type}</Typography>
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format('DD-MM-YYYY') ?? '-'}</Typography>
      },
      {
        header: 'Data',
        accessorKey: 'data',
        cell: ({ row }) => {
          const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
          const stripHtml = (html: string) => html.replace(/<[^>]+>/g, '');

          const truncateWords = (text: string, wordLimit = 20) => {
            const words = text.split(/\s+/);
            return words.length > wordLimit ? words.slice(0, wordLimit).join(' ') + '...' : text;
          };

          const value = row.original.data;
          let content = '';

          if (typeof value === 'string') {
            if (isHtml(value)) {
              content = value;
            } else {
              try {
                const raw = JSON.parse(value);
                content = draftToHtml(raw);
              } catch {
                content = value;
              }
            }
          }

          const plainText = stripHtml(content);
          const preview = truncateWords(plainText, 20);

          return (
            <div
              style={{
                maxWidth: 250,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
              title={plainText}
            >
              {preview}
            </div>
          );
        }
      },

      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add style={{ color: theme.palette.error.main, transform: 'rotate(45deg)' }} />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/contents/legals/edit/${row.original.id}`);
                  }}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                  disabled={!canEdit}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    setDeleteId(row.original.id || '');
                    handleOpen();
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [router, canEdit, canDelete, theme]
  );

  return (
    <>
      {isLoading || isCountLoading ? (
        <Loader />
      ) : (
        <LegalsTable
          data={legals ?? []}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          pagination={pagination}
          setPagination={setPagination}
          totalRows={countData?.count ?? 0}
          canCreate={canCreate}
          loading={isLoading}
          refetch={refetch}
        />
      )}

      <AlertLegalsDelete id={deleteId} title="Legals" open={open} handleClose={handleClose} refetch={refetch} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewLegals)(LegalsListPage);
