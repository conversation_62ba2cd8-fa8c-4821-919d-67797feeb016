import React from 'react';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import { EditorState, convertFromRaw } from 'draft-js';
import dynamic from 'next/dynamic';
import { EditorProps } from 'react-draft-wysiwyg';
import { LegalVisibility } from 'enums/legal-category.enum';
import { useGetLegalByIdQuery } from 'redux/app/contents/legals/legalsApiSlice';

const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as any), {
  ssr: false,
  loading: () => <p>Loading...</p>
});

function ViewLegals({ legalsId }: { legalsId: string }) {
  const { data: legalsData, isLoading, error } = useGetLegalByIdQuery(legalsId);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (error || !legalsData) return <Typography>Error fetching legals</Typography>;

  let editorState = EditorState.createEmpty();
  const contentState = convertFromRaw(JSON.parse(legalsData.data));
  editorState = EditorState.createWithContent(contentState);

  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <Card sx={{ p: 1, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" mb={2}>
              Terms and Conditions Details
            </Typography>
            <Typography display="flex" flexDirection="column">
              <strong>Category:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1,
                  mb: 3
                }}
              >
                {legalsData.category}
              </Box>
            </Typography>
            <Typography display="flex" flexDirection="column">
              <strong>Visibility:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1,
                  mb: 3
                }}
              >
                {typeof legalsData.visibility === 'number' ? LegalVisibility[legalsData.visibility] : ''}{' '}
              </Box>
            </Typography>
            <Typography display="flex" flexDirection="column">
              <strong>Type:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1,
                  mb: 3
                }}
              >
                {legalsData.type}
              </Box>
            </Typography>
            <strong>Content:</strong>
            <Box
              sx={{
                p: 2,
                border: '1px solid #ccc',
                borderRadius: 2,
                minHeight: 150,
                maxHeight: 300,
                overflow: 'auto',
                mt: 1
              }}
            >
              <Editor editorState={editorState} toolbarHidden readOnly />
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

export default ViewLegals;
