'use client';

import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ColumnDef, ColumnFiltersState, SortingState, PaginationState } from '@tanstack/react-table';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import IconButton from 'components/@extended/IconButton';
import { Add, Eye } from 'iconsax-react';
import { useTheme } from '@mui/material/styles';

import Loader from 'components/Loader';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import ServiceRequestTable from './ServiceRequestTable';
import { DEFAULT_DATE_FORMAT, ThemeMode } from 'config';

import moment from 'moment-timezone';
import withPermission from 'hoc/withPermission';
import { useAuth } from 'contexts/AuthContext';
import { PermissionKeys } from 'enums/permission-keys.enum';

import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';

import { EcomDukeServiceRequest } from 'types/service';
import { useGetServiceRequestCountQuery, useGetServiceRequestsQuery } from 'redux/app/service/serviceRequestApiSlice';

const ServiceRequestListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const canCreate = hasPermission(PermissionKeys.CreateServiceRequest);
  const canEdit = hasPermission(PermissionKeys.UpdateServiceRequest);

  const {
    data: requestList,
    isLoading: listLoading,
    refetch
  } = useGetServiceRequestsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['status', 'notes', 'paymentReference']),
    ...convertPaginationToLoopback(pagination)
  });

  const { data: requestCount, isLoading: countLoading } = useGetServiceRequestCountQuery({});

  useEffect(() => {
    refetch?.();
  }, [refetch]);

  const columns = useMemo<ColumnDef<EcomDukeServiceRequest>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Seller',
        accessorKey: 'sellerId',
        cell: ({ row }) => (
          <Typography>
            {row.original.seller?.userTenant?.user
              ? `${row.original.seller.userTenant.user.firstName} ${row.original.seller.userTenant.user.lastName}`
              : '-'}
          </Typography>
        )
      },
      {
        header: 'Service',
        accessorKey: 'ecomdukeserviceId',
        cell: ({ row }) => <Typography>{row.original.ecomdukeservice?.name ?? '-'}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ row }) => <Typography>{row.original.status ?? '-'}</Typography>
      },
      {
        header: 'Paid Amount',
        accessorKey: 'paidAmount',
        cell: ({ row }) => <Typography>{row.original.paidAmount ?? '-'}</Typography>
      },
      {
        header: 'Paid On',
        accessorKey: 'paidOn',
        cell: ({ row }) => <Typography>{row.original.paidOn ? moment(row.original.paidOn).format(DEFAULT_DATE_FORMAT) : '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );
          return (
            <Stack direction="row" alignItems="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, canEdit, router]
  );

  return (
    <>
      {listLoading || countLoading ? (
        <Loader />
      ) : (
        <ServiceRequestTable
          {...{
            data: requestList ?? [],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: listLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: requestCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
    </>
  );
};

export default withPermission(PermissionKeys.ViewServiceRequest)(ServiceRequestListPage);
