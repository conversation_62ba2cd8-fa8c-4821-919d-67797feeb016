'use client';

import React, { useEffect } from 'react';
import { Grid, Typography, <PERSON>, Card, CardContent, Button, Stack } from '@mui/material';

import { useGetServiceRequestByIdQuery, useUpdateServiceRequestMutation } from 'redux/app/service/serviceRequestApiSlice';
import { useGetServicesQuery } from 'redux/app/service/serviceApiSlice';

interface ViewServiceRequestProps {
  requestId: string;
}

const ViewServiceRequest = ({ requestId }: ViewServiceRequestProps) => {
  const {
    data: requestData,
    isLoading,
    error,
    refetch
  } = useGetServiceRequestByIdQuery({
    id: requestId,
    filter: {
      include: [
        {
          relation: 'seller',
          scope: {
            include: [
              {
                relation: 'userTenant',
                scope: { include: [{ relation: 'user' }] }
              }
            ]
          }
        },
        { relation: 'ecomdukeservice' }
      ]
    }
  });

  const { data: allServices = [] } = useGetServicesQuery();
  const [updateServiceRequestStatus, { isLoading: isUpdating }] = useUpdateServiceRequestMutation();

  const handleStatusUpdate = async (newStatus: 'APPROVED' | 'REJECTED') => {
    if (!requestData?.id) return;
    await updateServiceRequestStatus({
      id: requestData.id,
      data: { status: newStatus }
    }).unwrap();
    refetch();
  };

  useEffect(() => {
    refetch();
  }, [refetch]);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (error || !requestData) return <Typography color="error">Error fetching service request</Typography>;

  const currentStatus = requestData.status?.toUpperCase();
  const isPaid = currentStatus === 'PAID';

  const service = allServices.find((s) => s.id === requestData.ecomdukeserviceId);
  const serviceName = service?.name ?? 'N/A';

  return (
    <Grid container spacing={2} sx={{ mt: 1 }}>
      <Grid item xs={12}>
        <Card sx={{ p: 2, boxShadow: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Service Request Details
            </Typography>

            {/* Show Approve/Reject buttons only if status is PAID */}
            {isPaid && (
              <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                <Button variant="contained" color="success" onClick={() => handleStatusUpdate('APPROVED')} disabled={isUpdating}>
                  Approve
                </Button>
                <Button variant="contained" color="error" onClick={() => handleStatusUpdate('REJECTED')} disabled={isUpdating}>
                  Reject
                </Button>
              </Stack>
            )}

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <DisplayItem label="Service Name" value={serviceName} />
              </Grid>

              <Grid item xs={12} md={6}>
                <DisplayItem
                  label="Seller"
                  value={
                    requestData.seller?.userTenant?.user
                      ? `${requestData.seller.userTenant.user.firstName} ${requestData.seller.userTenant.user.lastName}`
                      : '-'
                  }
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <DisplayItem label="Status" value={requestData.status} />
              </Grid>

              {requestData.paidAmount !== undefined && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid Amount" value={requestData.paidAmount} />
                </Grid>
              )}

              {requestData.paymentReference && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Payment Reference" value={requestData.paymentReference} />
                </Grid>
              )}

              {requestData.paidOn && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Paid On" value={new Date(requestData.paidOn).toLocaleString()} />
                </Grid>
              )}

              {requestData.notes && (
                <Grid item xs={12}>
                  <DisplayItem label="Notes" value={requestData.notes} isMultiline />
                </Grid>
              )}

              {requestData.createdOn && (
                <Grid item xs={12} md={6}>
                  <DisplayItem label="Created On" value={new Date(requestData.createdOn).toLocaleString()} />
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const DisplayItem = ({ label, value, isMultiline = false }: { label: string; value: string | number; isMultiline?: boolean }) => (
  <Box sx={{ mb: 1.5 }}>
    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
      {label}:
    </Typography>
    <Box
      sx={{
        backgroundColor: '#f5f5f5',
        px: 2,
        py: 1.2,
        borderRadius: 2,
        fontSize: '0.95rem',
        color: 'text.primary',
        whiteSpace: isMultiline ? 'pre-line' : 'nowrap'
      }}
    >
      {value}
    </Box>
  </Box>
);

export default ViewServiceRequest;
