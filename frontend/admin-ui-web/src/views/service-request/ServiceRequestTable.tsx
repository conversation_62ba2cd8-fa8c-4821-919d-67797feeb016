'use client';

import React, { Fragment } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  PaginationState
} from '@tanstack/react-table';
import { Stack, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button } from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { useRouter } from 'next/navigation';
import { Refresh } from 'iconsax-react';

import DebouncedInput from 'components/third-party/react-table/DebouncedInput';
import HeaderSort from 'components/third-party/react-table/HeaderSort';
import TablePagination from 'components/third-party/react-table/TablePagination';
import SelectColumnVisibility from 'components/third-party/react-table/SelectColumnVisibility';
import IconButton from 'components/@extended/IconButton';
import ServiceRequestView from './ServiceRequestView'; // ← You'll implement this like ServiceView
import { EcomDukeServiceRequest } from 'types/service';

interface Props {
  data: EcomDukeServiceRequest[];
  columns: ColumnDef<any>[];
  sorting: SortingState;
  setSorting: React.Dispatch<React.SetStateAction<SortingState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  totalRows: number;
  loading: boolean;
  canCreate: boolean;
  refetch: () => Promise<void> | any;
}

const ServiceRequestTable = ({
  data,
  columns,
  sorting,
  setSorting,
  columnFilters,
  setColumnFilters,
  globalFilter,
  setGlobalFilter,
  pagination,
  setPagination,
  totalRows,
  loading,
  canCreate,
  refetch
}: Props) => {
  const router = useRouter();
  const theme = useTheme();
  const backColor = alpha(theme.palette.primary.light, 0.1);

  const table = useReactTable({
    data,
    columns,
    state: { sorting, columnFilters, globalFilter, pagination },
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
    rowCount: totalRows,
    getRowCanExpand: () => true
  });

  const handleCreate = () => {
    router.push('/ecomduke-service/create');
  };

  return (
    <Paper sx={{ p: 2, mt: 4 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
        <DebouncedInput
          value={globalFilter ?? ''}
          onFilterChange={(value) => setGlobalFilter(String(value))}
          placeholder={`Search ${totalRows} requests...`}
        />
        <Stack direction="row" alignItems="center" spacing={2}>
          <SelectColumnVisibility
            {...{
              getVisibleLeafColumns: table.getVisibleLeafColumns,
              getIsAllColumnsVisible: table.getIsAllColumnsVisible,
              getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
              getAllColumns: table.getAllColumns
            }}
          />
          <IconButton onClick={refetch} size="large">
            <Refresh />
          </IconButton>
          <Button variant="contained" onClick={handleCreate} disabled={!canCreate} size="large">
            Create Request
          </Button>
        </Stack>
      </Stack>

      <TableContainer sx={{ border: '1px solid #ddd', borderRadius: 1 }}>
        <Table>
          <TableHead>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableCell key={header.id} onClick={header.column.getToggleSortingHandler()}>
                    <Stack direction="row" alignItems="center">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getCanSort() && <HeaderSort column={header.column} />}
                    </Stack>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length || 1} align="center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : data?.length > 0 ? (
              table.getRowModel().rows.map((row) => (
                <Fragment key={row.id}>
                  <TableRow>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                  {row.getIsExpanded() && (
                    <TableRow
                      sx={{
                        bgcolor: backColor,
                        '&:hover': { bgcolor: `${backColor} !important` }
                      }}
                    >
                      <TableCell colSpan={row.getVisibleCells().length} sx={{ p: 2.5 }}>
                        <ServiceRequestView requestId={row.original.id} />
                      </TableCell>
                    </TableRow>
                  )}
                </Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length || 1} align="center">
                  No service requests found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {columns.length > 0 && (
        <TablePagination
          {...{
            setPageSize: table.setPageSize,
            setPageIndex: table.setPageIndex,
            getState: table.getState,
            getPageCount: table.getPageCount
          }}
        />
      )}
    </Paper>
  );
};

export default ServiceRequestTable;
