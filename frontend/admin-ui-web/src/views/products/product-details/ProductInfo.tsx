import { Box } from '@mui/material';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// types
import { Product } from 'types/product';
import { ProductStatus } from 'enums/product-status.enum';

// ==============================|| PRODUCT DETAILS - INFORMATION ||============================== //

export default function ProductInfo({ product }: { product: Product }) {
  return (
    <Stack spacing={1}>
      <Typography variant="h5">{product.name}</Typography>
      <Chip
        size="small"
        label={product.slug ? 'In Stock' : 'Out of Stock'}
        sx={{
          width: 'fit-content',
          borderRadius: '4px',
          color: product.slug ? 'success.main' : 'error.main',
          bgcolor: product.slug ? 'success.lighter' : 'error.lighter'
        }}
      />

      {/* Product Status */}
      {product.status && (
        <Chip
          size="small"
          label={
            product.status === ProductStatus.APPROVED ? 'Approved' : product.status === ProductStatus.REJECTED ? 'Rejected' : 'Pending'
          }
          sx={{
            width: 'fit-content',
            borderRadius: '4px',
            color:
              product.status === ProductStatus.APPROVED
                ? 'success.main'
                : product.status === ProductStatus.REJECTED
                  ? 'error.main'
                  : 'warning.main',
            bgcolor:
              product.status === ProductStatus.APPROVED
                ? 'success.lighter'
                : product.status === ProductStatus.REJECTED
                  ? 'error.lighter'
                  : 'warning.lighter'
          }}
        />
      )}

      {/* Rejected Reason */}
      {product.status === ProductStatus.REJECTED && product.rejectedReason && (
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'error.main',
            borderRadius: 1,
            p: 1.5,
            backgroundColor: 'error.lighter',
            mt: 1
          }}
        >
          <Typography variant="body2" color="error.main" fontWeight="medium">
            Rejection Reason:
          </Typography>
          <Typography variant="body2" color="text.primary" sx={{ mt: 0.5 }}>
            {product.rejectedReason}
          </Typography>
        </Box>
      )}
      <Box
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1,
          p: 2,
          my: 2,
          backgroundColor: 'background.paper',
          maxHeight: 86,
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 6
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'text.secondary',
            borderRadius: 3
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'divider'
          }
        }}
      >
        <Typography
          color="text.secondary"
          sx={{
            whiteSpace: 'pre-line',
            lineHeight: 1.6,
            pr: 1
          }}
        >
          {product.description}
        </Typography>
      </Box>

      <Typography color="text.secondary">{product.deleted}</Typography>
    </Stack>
  );
}
