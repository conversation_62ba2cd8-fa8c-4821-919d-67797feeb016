'use client';

import { useMemo, useState, MouseEvent, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { DEFAULT_DATE_FORMAT, ThemeMode } from 'config';
import { Edit, Eye, Trash, ArrowLeft, Add } from 'iconsax-react';
import moment from 'moment-timezone';
import IndeterminateCheckbox from '../../components/third-party/react-table/IndeterminateCheckbox';
import Loader from 'components/Loader';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import { useGetproductCountQuery, useGetproductsQuery } from 'redux/app/products/productApiSlice';
import ProductTable from './ProductTable';
import { Product } from 'types/product';
import AlertProductDelete from './AlertproductDelete';
import { IFilter } from 'redux/app/types/filter';
import { ProductStatus } from 'enums/product-status.enum';
import { Chip } from '@mui/material';

const ProductListing = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [idTobeDeleted, setIdTobedeleted] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);

  const canCreate = hasPermission(PermissionKeys.CreateProduct);
  const canEdit = hasPermission(PermissionKeys.UpdateProduct);
  const canDelete = hasPermission(PermissionKeys.DeleteProduct);

  const handleClose = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const loopbackFilter: IFilter = {
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'slug']),
    ...convertPaginationToLoopback(pagination),
    include: [
      {
        relation: 'productAssets'
      },
      {
        relation: 'featuredAsset'
      }
    ]
  };

  const { data: productList, isLoading: productListLoading, refetch } = useGetproductsQuery(loopbackFilter);

  const {
    data: productCount,
    isLoading: countLoading,
    refetch: countRefetch
  } = useGetproductCountQuery({
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name', 'slug']),
    include: []
  });

  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  useEffect(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    countRefetch();
  }, [countRefetch]);

  const columns = useMemo<ColumnDef<Product>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Product ID',
        accessorKey: 'featuredAsset',
        cell: ({ row }) => {
          const imageUrl = row.original?.featuredAsset?.previewUrl;
          const productId = row.original?.productId;

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt="Product"
                  style={{
                    width: 40,
                    height: 40,
                    objectFit: 'cover',
                    borderRadius: 4,
                    marginRight: 26
                  }}
                />
              ) : (
                <span>-</span>
              )}
              <span>{productId}</span>
            </div>
          );
        },
        meta: { className: 'cell-center' }
      },

      {
        header: 'Name',
        accessorKey: 'name'
      },
      {
        header: 'Slug',
        accessorKey: 'slug'
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ row }) => {
          const status = row.original?.status;
          if (!status) return <span>-</span>;

          const getStatusColor = (status: ProductStatus) => {
            switch (status) {
              case ProductStatus.APPROVED:
                return 'success';
              case ProductStatus.REJECTED:
                return 'error';
              case ProductStatus.PENDING:
              default:
                return 'warning';
            }
          };

          const getStatusLabel = (status: ProductStatus) => {
            switch (status) {
              case ProductStatus.APPROVED:
                return 'Approved';
              case ProductStatus.REJECTED:
                return 'Rejected';
              case ProductStatus.PENDING:
              default:
                return 'Pending';
            }
          };

          return <Chip label={getStatusLabel(status)} color={getStatusColor(status)} size="small" variant="outlined" />;
        },
        meta: { className: 'cell-center' }
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format(DEFAULT_DATE_FORMAT) ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        meta: {
          className: 'cell-center'
        },
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  color="primary"
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/products/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    setOpen(true);
                    setIdTobedeleted(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, canDelete, canEdit, router]
  );
  return (
    <>
      {selectedProduct ? (
        <>
          <IconButton onClick={() => setSelectedProduct(null)} sx={{ mb: 2 }}>
            <ArrowLeft />
          </IconButton>
          <p>Coming Soon</p>
        </>
      ) : countLoading || productListLoading ? (
        <Loader />
      ) : (
        <ProductTable
          {...{
            data: productList || [],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: productListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: productCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertProductDelete
        refetch={refetch}
        id={idTobeDeleted}
        title={productList?.find((product) => product.id === idTobeDeleted)?.name ?? idTobeDeleted}
        open={open}
        handleClose={handleClose}
      />
    </>
  );
};

export default withPermission(PermissionKeys.ViewProduct)(ProductListing);
