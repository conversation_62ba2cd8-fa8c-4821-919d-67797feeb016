'use client';

import React, { Fragment } from 'react';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableCell from '@mui/material/TableCell';
import TableBody from '@mui/material/TableBody';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableContainer from '@mui/material/TableContainer';
import Paper from '@mui/material/Paper';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  PaginationState
} from '@tanstack/react-table';
import DebouncedInput from '../../components/third-party/react-table/DebouncedInput';
import HeaderSort from '../../components/third-party/react-table/HeaderSort';
import TablePagination from '../../components/third-party/react-table/TablePagination';
import SelectColumnVisibility from 'components/third-party/react-table/SelectColumnVisibility';
import {
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { alpha, useTheme } from '@mui/material/styles';
import { Product } from 'types/product';
import ProductView from './ProductView';
import { useBulkStatusUpdateMutation } from 'redux/app/products/productApiSlice';
import { openSnackbar } from 'api/snackbar';
import { useState } from 'react';

interface Props {
  data: Product[];
  columns: ColumnDef<any>[];
  sorting: SortingState;
  setSorting: React.Dispatch<React.SetStateAction<SortingState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  loading: boolean;
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  totalRows: number;
  canCreate: boolean;
  refetch: () => void;
}

const ProductTable = ({
  data,
  columns,
  sorting,
  setSorting,
  columnFilters,
  setColumnFilters,
  globalFilter,
  setGlobalFilter,
  pagination,
  setPagination,
  totalRows,
  canCreate,
  loading,
  refetch
}: Props) => {
  const table = useReactTable({
    data,
    columns,
    state: { sorting, columnFilters, globalFilter, pagination },
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
    rowCount: totalRows,
    getRowCanExpand: () => true
  });
  const router = useRouter();
  const theme = useTheme();
  const backColor = alpha(theme.palette.primary.light, 0.1);

  // Bulk action state
  const [bulkStatusUpdateMutation] = useBulkStatusUpdateMutation();
  const [bulkActionDialogOpen, setBulkActionDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [rejectedReason, setRejectedReason] = useState('');

  const handleCreateProduct = () => {
    router.push('products/create');
  };

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectedProductIds = selectedRows.map((row) => row.original.id);

  const handleBulkStatusUpdate = async () => {
    if (selectedProductIds.length === 0) {
      openSnackbar({
        message: 'Please select products to update',
        open: true,
        variant: 'alert',
        alert: { color: 'warning' }
      });
      return;
    }

    if (!selectedStatus) {
      openSnackbar({
        message: 'Please select a status',
        open: true,
        variant: 'alert',
        alert: { color: 'warning' }
      });
      return;
    }

    try {
      await bulkStatusUpdateMutation({
        productIds: selectedProductIds,
        status: selectedStatus,
        rejectedReason: selectedStatus === 'REJECTED' ? rejectedReason : undefined
      }).unwrap();

      openSnackbar({
        message: `Successfully updated ${selectedProductIds.length} products`,
        open: true,
        variant: 'alert',
        alert: { color: 'success' }
      });

      setBulkActionDialogOpen(false);
      setSelectedStatus('');
      setRejectedReason('');
      table.resetRowSelection();
      refetch();
    } catch (error) {
      openSnackbar({
        message: 'Failed to update products',
        open: true,
        variant: 'alert',
        alert: { color: 'error' }
      });
    }
  };

  return (
    <Paper sx={{ p: 2, mt: 4 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
        <DebouncedInput
          value={globalFilter ?? ''}
          onFilterChange={(value) => setGlobalFilter(String(value))}
          placeholder={`Search ${totalRows} records...`}
        />
        <Stack direction="row" alignItems="center" spacing={2}>
          <SelectColumnVisibility
            {...{
              getVisibleLeafColumns: table.getVisibleLeafColumns,
              getIsAllColumnsVisible: table.getIsAllColumnsVisible,
              getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
              getAllColumns: table.getAllColumns
            }}
          />
          <Button variant="contained" onClick={handleCreateProduct} size="large" disabled={!canCreate}>
            Create Product
          </Button>
        </Stack>
      </Stack>
      <TableContainer sx={{ border: '1px solid #ddd', borderRadius: 1 }}>
        <Table>
          <TableHead>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableCell key={header.id} onClick={header.column.getToggleSortingHandler()}>
                    <Stack direction="row" alignItems="center">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getCanSort() && <HeaderSort column={header.column} />}
                    </Stack>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length || 1} align="center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : data?.length > 0 ? (
              table.getRowModel().rows.map((row) => (
                <Fragment key={row.id}>
                  <TableRow>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                  {row.getIsExpanded() && (
                    <TableRow
                      sx={{
                        bgcolor: backColor,
                        '&:hover': { bgcolor: `${backColor} !important` },
                        overflow: 'hidden'
                      }}
                    >
                      <TableCell colSpan={row.getVisibleCells().length} sx={{ p: 2.5, overflow: 'hidden' }}>
                        <ProductView id={row.original.id ?? ''} />
                      </TableCell>
                    </TableRow>
                  )}
                </Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length || 1} align="center">
                  No FAQs available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {columns.length > 0 && (
        <TablePagination
          {...{
            setPageSize: table.setPageSize,
            setPageIndex: table.setPageIndex,
            getState: table.getState,
            getPageCount: table.getPageCount
          }}
        />
      )}
    </Paper>
  );
};

export default ProductTable;
