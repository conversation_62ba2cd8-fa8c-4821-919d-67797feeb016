export enum PermissionKeys {
  CreateHomePage = 'CreateHomePage',

  CreateSeller = 'CreateSeller',
  ViewSeller = 'ViewSeller',
  UpdateSeller = 'UpdateSeller',
  DeleteSeller = 'DeleteSeller',

  // Customer permissions
  CreateCustomer = 'CreateCustomer',
  ViewCustomer = 'ViewCustomer',
  UpdateCustomer = 'UpdateCustomer',
  DeleteCustomer = 'DeleteCustomer',

  // Audit permissions
  CreateAudit = 'CreateAudit',
  ViewAudit = 'ViewAudit',
  UpdateAudit = 'UpdateAudit',
  DeleteAudit = 'DeleteAudit',

  // Cart permissions
  CreateCart = 'CreateCart',
  ViewCart = 'ViewCart',
  UpdateCart = 'UpdateCart',
  DeleteCart = 'DeleteCart',

  // Payment permissions
  CreatePayment = 'CreatePayment',
  ViewPayment = 'ViewPayment',
  UpdatePayment = 'UpdatePayment',
  DeletePayment = 'DeletePayment',

  // Product permissions
  CreateProduct = 'CreateProduct',
  ViewProduct = 'ViewProduct',
  UpdateProduct = 'UpdateProduct',
  DeleteProduct = 'DeleteProduct',

  // Category permissions
  CreateCategory = 'CreateCategory',
  ViewCategory = 'ViewCategory',
  UpdateCategory = 'UpdateCategory',
  DeleteCategory = 'DeleteCategory',

  // Facet permissions
  CreateFacet = 'CreateFacet',
  ViewFacet = 'ViewFacet',
  UpdateFacet = 'UpdateFacet',
  DeleteFacet = 'DeleteFacet',

  // Faq permissions
  CreateFaq = 'CreateFaq',
  ViewFaq = 'ViewFaq',
  UpdateFaq = 'UpdateFaq',
  DeleteFaq = 'DeleteFaq',

  //Notification Permissions
  ViewNotificationNum = 'ViewNotificationNum',
  ViewNotification = 'ViewNotification',
  UpdateNotification = 'UpdateNotification',
  DeleteNotification = 'DeleteNotification',

  CreateSubAdmin = 'CreateSubAdmin',
  UpdateSubAdmin = 'UpdateSubAdmin',
  ViewSubAdmin = 'ViewSubAdmin',
  DeleteSubAdmin = 'DeleteSubAdmin',
  // Configuration permissions
  CreateConfiguration = 'CreateConfiguration',
  ViewConfiguration = 'ViewConfiguration',
  UpdateConfiguration = 'UpdateConfiguration',
  DeleteConfiguration = 'DeleteConfiguration',

  // Plan Permissions
  CreatePlan = 'CreatePlan',
  UpdatePlan = 'UpdatePlan',
  ViewPlan = 'ViewPlan',
  DeletePlan = 'DeletePlan',

  // Feature Permissions
  CreateFeature = 'CreateFeature',
  UpdateFeature = 'UpdateFeature',
  ViewFeature = 'ViewFeature',
  DeleteFeature = 'DeleteFeature',
  // Feature Permissions
  CreateOrder = 'CreateOrder',
  UpdateOrder = 'UpdateOrder',
  ViewOrder = 'ViewOrder',
  DeleteOrder = 'DeleteOrder',

  // FeatureValue Permissions
  CreateFeatureValue = 'CreateFeatureValue',
  UpdateFeatureValue = 'UpdateFeatureValue',
  ViewFeatureValue = 'ViewFeatureValue',
  DeleteFeatureValue = 'DeleteFeatureValue',

  // PlanFeatureValue Permissions
  CreatePlanFeatureValue = 'CreatePlanFeatureValue',
  UpdatePlanFeatureValue = 'UpdatePlanFeatureValue',
  ViewPlanFeatureValue = 'ViewPlanFeatureValue',
  DeletePlanFeatureValue = 'DeletePlanFeatureValue',

  CreateCollection = 'CreateCollection',
  ViewCollection = 'ViewCollection',
  UpdateCollection = 'UpdateCollection',
  DeleteCollection = 'DeleteCollection',

  CreateAsset = 'CreateAsset',
  ViewAsset = 'ViewAsset',
  UpdateAsset = 'UpdateAsset',
  DeleteAsset = 'DeleteAsset',

  // TermsAndCondition Permissions
  CreateTermsAndCondition = 'CreateTermsAndCondition',
  ViewTermsAndCondition = 'ViewTermsAndCondition',
  UpdateTermsAndCondition = 'UpdateTermsAndCondition',
  DeleteTermsAndCondition = 'DeleteTermsAndCondition',

  // PrivacyPolicy Permissions
  CreatePrivacyPolicy = 'CreatePrivacyPolicy',
  ViewPrivacyPolicy = 'ViewPrivacyPolicy',
  UpdatePrivacyPolicy = 'UpdatePrivacyPolicy',
  DeletePrivacyPolicy = 'DeletePrivacyPolicy',

  // TaxCategory Permissions
  CreateTaxCategory = 'CreateTaxCategory',
  ViewTaxCategory = 'ViewTaxCategory',
  UpdateTaxCategory = 'UpdateTaxCategory',
  DeleteTaxCategory = 'DeleteTaxCategory',

  // PromoCode Permissions
  CreatePromoCode = 'CreatePromoCode',
  ViewPromoCode = 'ViewPromoCode',
  UpdatePromoCode = 'UpdatePromoCode',
  DeletePromoCode = 'DeletePromoCode',
  //review

  ViewReview = 'ViewReview',
  UpdateReview = 'UpdateReview',
  DeleteReview = 'DeleteReview',

  CreateCampaign = 'CreateCampaign',
  ViewCampaign = 'ViewCampaign',
  UpdateCampaign = 'UpdateCampaign',
  DeleteCampaign = 'DeleteCampaign'
}
