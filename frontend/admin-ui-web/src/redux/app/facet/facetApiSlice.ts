import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { Facet, FacetValue } from 'types/facet';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { Count } from 'types/api';

export const facetApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getFacets: builder.query<Facet[], IFilter | void>({
      query: (filter) => ({
        url: '/facets',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),

    createFacet: builder.mutation<void, Partial<Facet>>({
      query: (newFacet) => ({
        url: '/facets',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: newFacet
      })
    }),

    getFacetById: builder.query<Facet, { id: string; include?: Array<Record<string, unknown> | string> }>({
      query: ({ id, include }) => ({
        url: `/facets/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: include ?? [{ relation: 'facetValues' }]
          })
        }
      })
    }),

    removeFacet: builder.mutation<void, string>({
      query: (id) => ({
        url: `/facets/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    updateFacet: builder.mutation<void, { id: string; data: Partial<Facet> }>({
      query: ({ id, data }) => ({
        url: `/facets/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    getFacetsCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/facets/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),

    getFacetValues: builder.query<FacetValue[], IFilter | void>({
      query: (filter) => ({
        url: '/facet-values',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    })
  })
});

export const {
  useGetFacetsQuery,
  useLazyGetFacetsQuery,
  useGetFacetsCountQuery,
  useRemoveFacetMutation,
  useCreateFacetMutation,
  useUpdateFacetMutation,
  useGetFacetByIdQuery,
  useLazyGetFacetValuesQuery
} = facetApiSlice;
