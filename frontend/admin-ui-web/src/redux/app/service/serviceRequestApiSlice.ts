import { apiSlice } from '../../apiSlice';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { Count } from 'types/api';
import { IFilter } from '../types/filter';
import { EcomDukeServiceRequest } from 'types/service';

export const ecomServiceRequestApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getServiceRequests: builder.query<EcomDukeServiceRequest[], IFilter | void>({
      query: (filter) => ({
        url: '/ecomdukeservice-requests',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'seller',
                scope: {
                  include: [
                    {
                      relation: 'userTenant',

                      scope: {
                        include: [{ relation: 'user' }]
                      }
                    }
                  ]
                }
              },
              {
                relation: 'ecomdukeservice' // <-- this matches the `@belongsTo` name in the model
              }
            ]
          })
        }
      })
    }),

    getServiceRequestById: builder.query<EcomDukeServiceRequest, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => {
        const defaultInclude = [
          {
            relation: 'seller',
            scope: {
              include: [
                {
                  relation: 'userTenant',
                  scope: {
                    include: [{ relation: 'user' }]
                  }
                }
              ]
            }
          },
          {
            relation: 'ecomdukeservice'
          }
        ];

        return {
          url: `/ecomdukeservice-requests/${id}`,
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            filter: JSON.stringify({
              ...filter,
              include: defaultInclude
            })
          }
        };
      }
    }),

    getServiceRequestCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/ecomdukeservice-requests/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({ where, include })
        }
      })
    }),
    createServiceRequest: builder.mutation<void, Partial<EcomDukeServiceRequest>>({
      query: (data) => ({
        url: '/ecomdukeservice-requests',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    updateServiceRequest: builder.mutation<void, { id: string; data: Partial<EcomDukeServiceRequest> }>({
      query: ({ id, data }) => ({
        url: `/ecomdukeservice-requests/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    deleteServiceRequest: builder.mutation<void, string>({
      query: (id) => ({
        url: `/ecomdukeservice-requests/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const {
  useGetServiceRequestsQuery,
  useLazyGetServiceRequestsQuery,
  useGetServiceRequestByIdQuery,
  useGetServiceRequestCountQuery,
  useCreateServiceRequestMutation,
  useUpdateServiceRequestMutation,
  useDeleteServiceRequestMutation
} = ecomServiceRequestApiSlice;
