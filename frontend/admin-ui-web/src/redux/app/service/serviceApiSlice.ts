import { apiSlice } from '../../apiSlice';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { Count } from 'types/api';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { EcomDukeService } from 'types/service';

export const ecomServiceApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getServices: builder.query<EcomDukeService[], IFilter | void>({
      query: (filter) => ({
        url: '/ecomdukeservices',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getServiceById: builder.query<EcomDukeService, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/ecomdukeservices/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getServiceCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/ecomdukeservices/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),
    createEcomService: builder.mutation<void, Partial<EcomDukeService>>({
      query: (data) => ({
        url: '/ecomdukeservices',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    updateEcomService: builder.mutation<void, { id: string; data: Partial<EcomDukeService> }>({
      query: ({ id, data }) => ({
        url: `/ecomdukeservices/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    deleteEcomService: builder.mutation<void, string>({
      query: (id) => ({
        url: `/ecomdukeservices/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const {
  useGetServicesQuery,
  useLazyGetServicesQuery,
  useGetServiceByIdQuery,
  useGetServiceCountQuery,
  useCreateEcomServiceMutation,
  useUpdateEcomServiceMutation,
  useDeleteEcomServiceMutation
} = ecomServiceApiSlice;
