import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from 'redux/apiSlice';
import { buildFilterParams } from 'utils/buildFilterParams';
import { IFilter } from '../types/filter';
import { KanbanBoard, KanbanItem } from 'types/kanban';

export const kanbanApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getTickets: builder.query<KanbanBoard, { filter?: IFilter }>({
      query: ({ filter }) => ({
        url: '/tickets',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter)
        }
      })
    }),

    updateTicket: builder.mutation<KanbanItem, { id: string; data: Partial<KanbanItem> }>({
      query: ({ id, data }) => ({
        url: `/tickets/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    })
  })
});

export const { useGetTicketsQuery, useUpdateTicketMutation } = kanbanApiSlice;
