import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { Count } from 'types/api';
import { Campaign } from 'types/campaign';

export const campaignApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCampaigns: builder.query<Campaign[], IFilter | void>({
      query: (filter) => ({
        url: '/campaigns',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),

    createCampaign: builder.mutation<void, Partial<Campaign>>({
      query: (newCampaign) => ({
        url: '/campaigns',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: newCampaign
      })
    }),

    getCampaignById: builder.query<Campaign, { id: string }>({
      query: ({ id }) => ({
        url: `/campaigns/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    updateCampaign: builder.mutation<void, { id: string; data: Partial<Campaign> }>({
      query: ({ id, data }) => ({
        url: `/campaigns/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    deleteCampaign: builder.mutation<void, string>({
      query: (id) => ({
        url: `/campaigns/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    getCampaignsCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/campaigns/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),

    sendCampaign: builder.mutation<void, { campaignKey: string }>({
      query: ({ campaignKey }) => ({
        url: '/campaigns/send',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: { campaignKey }
      })
    })
  })
});

export const {
  useGetCampaignsQuery,
  useCreateCampaignMutation,
  useGetCampaignByIdQuery,
  useUpdateCampaignMutation,
  useDeleteCampaignMutation,
  useGetCampaignsCountQuery,
  useSendCampaignMutation
} = campaignApiSlice;
