import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { Asset, Product, ProductVariant } from 'types/product';
import { Count } from 'types/api';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { ProductDto } from 'types/product-dto';
import { ProductStatus } from 'enums/product-status.enum';

export const productApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getproducts: builder.query<Product[], IFilter | void>({
      query: (filter) => ({
        url: '/products',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getproductById: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getproductCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/products/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),
    getAssets: builder.query<Asset[], IFilter | void>({
      query: (filter) => ({
        url: '/assets',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    createAsset: builder.mutation<void, FormData>({
      query: (formData) => ({
        url: `/assets`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: formData,
        formData: true
      })
    }),
    createProduct: builder.mutation<void, ProductDto>({
      query: (formData) => ({
        url: `/products`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: formData
      })
    }),
    deleteProduct: builder.mutation<void, string>({
      query: (id) => ({
        url: `/products/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateProductById: builder.mutation<void, { id: string; body: Partial<ProductDto> }>({
      query: ({ id, body }) => ({
        url: `/products/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),
    getAssetsCount: builder.query<Count, IFilter | void>({
      query: (filter) => ({
        url: '/assets/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getProductVariants: builder.query<ProductVariant[], IFilter | void>({
      query: (filter) => ({
        url: '/product-variants',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    bulkStatusUpdate: builder.mutation<void, { productIds: string[]; status: ProductStatus; rejectedReason?: string }>({
      query: (body) => ({
        url: '/products/bulk-status-update',
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),
    updateProductStatus: builder.mutation<void, { id: string; status: ProductStatus; rejectedReason?: string }>({
      query: ({ id, ...body }) => ({
        url: `/products/${id}/status`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    })
  })
});

export const {
  useGetproductsQuery,
  useGetproductCountQuery,
  useGetAssetsQuery,
  useCreateAssetMutation,
  useCreateProductMutation,
  useGetproductByIdQuery,
  useDeleteProductMutation,
  useUpdateProductByIdMutation,
  useLazyGetproductsQuery,
  useGetAssetsCountQuery,
  useLazyGetProductVariantsQuery,
  useBulkStatusUpdateMutation,
  useUpdateProductStatusMutation
} = productApiSlice;
