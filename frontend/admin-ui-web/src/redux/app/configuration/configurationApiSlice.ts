// src/redux/app/configuration/configurationApiSlice.ts

import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { buildFilterParams } from 'utils/buildFilterParams';
import { IFilter } from '../types/filter';
import { Configuration } from 'types/configuration';

export const configurationApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getConfigurations: builder.query<Configuration[], IFilter | void>({
      query: (filter) => ({
        url: '/configurations',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),

    updateConfiguration: builder.mutation<void, { id: string; data: Partial<Configuration> }>({
      query: ({ id, data }) => ({
        url: `/configurations/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    })
  })
});

export const { useGetConfigurationsQuery, useUpdateConfigurationMutation, useLazyGetConfigurationsQuery } = configurationApiSlice;
