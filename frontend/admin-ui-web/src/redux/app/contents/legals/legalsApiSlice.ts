import { ApiSliceIdentifier } from 'enums/api.enum';
import { LegalType } from 'enums/legal.enum';
import { apiSlice } from 'redux/apiSlice';

export const legalsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getLegals: builder.query<
      void,
      {
        limit: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, order, where, fields, include }) => ({
        url: '/legals',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            order,
            where,
            fields,
            include
          })
        }
      })
    }),
    getLegalById: builder.query<LegalType, string>({
      query: (id) => ({
        url: `/legals/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    createLegal: builder.mutation<LegalType, Partial<LegalType>>({
      query: (body) => ({
        url: '/legals',
        method: 'POST',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateLegal: builder.mutation<void, { id: string; body: Partial<LegalType> }>({
      query: ({ id, body }) => ({
        url: `/legals/${id}`,
        method: 'PATCH',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getLegalCount: builder.query<{ count: number }, void>({
      query: () => ({
        url: '/legals/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    removeLegal: builder.mutation<void, string>({
      query: (id) => ({
        url: `/legals/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const {
  useCreateLegalMutation,
  useGetLegalByIdQuery,
  useGetLegalsQuery,
  useRemoveLegalMutation,
  useUpdateLegalMutation,
  useLazyGetLegalByIdQuery,
  useLazyGetLegalsQuery,
  useGetLegalCountQuery
} = legalsApiSlice;
