// third-party
import { FormattedMessage } from 'react-intl';

// assets
import {
  Activity,
  Additem,
  Book1,
  ColorSwatch,
  Convert3DCube,
  Data,
  DollarSquare,
  Gift,
  Grid1,
  I24Support,
  MessageProgramming,
  People,
  ProfileCircle,
  Receipt1,
  SecurityUser,
  Setting2,
  Setting5,
  ShieldTick,
  ShoppingBag,
  TableDocument,
  UserSquare
} from 'iconsax-react';

// types
import { NavItemType } from 'types/menu';
import { PermissionKeys } from 'enums/permission-keys.enum';

// icons
const icons = {
  page: Book1,
  maintenance: MessageProgramming,
  contactus: I24Support,
  profile: ProfileCircle,
  usermanagement: UserSquare,
  admin: SecurityUser,
  ecommerce: ShoppingBag,
  product: Additem,
  facet: Book1,
  market: Activity,
  collection: ColorSwatch,
  plan: DollarSquare,
  settings: Setting2,
  feature: Data,
  subscription: ShieldTick,
  promo: Gift,
  customer: People,
  order: Grid1,
  review: TableDocument,
  tax: Receipt1,
  configure: Setting5,
  service: Convert3DCube
};

// ==============================|| MENU ITEMS - PAGES ||============================== //

const pages: NavItemType = {
  id: 'group-pages',
  title: <FormattedMessage id="pages" />,
  type: 'group',
  icon: icons.ecommerce,
  children: [
    {
      id: 'E-commerce',
      title: <FormattedMessage id="E-commerce" />,
      type: 'collapse',
      icon: icons.usermanagement,
      children: [
        {
          id: 'Products',
          title: <FormattedMessage id="Products" />,
          type: 'item',
          url: '/products',
          icon: icons.product,
          requiredPermission: PermissionKeys.ViewSubAdmin
        },
        {
          id: 'Orders',
          title: <FormattedMessage id="Orders" />,
          type: 'item',
          url: '/orders',
          icon: icons.order,
          requiredPermission: PermissionKeys.ViewOrder
        },
        {
          id: 'Tags',
          title: <FormattedMessage id="Tags" />,
          type: 'item',
          url: '/facets',
          icon: icons.facet,
          requiredPermission: PermissionKeys.ViewFacet
        },
        {
          id: 'Category',
          title: <FormattedMessage id="Category" />,
          type: 'item',
          url: '/collections',
          icon: icons.collection,
          requiredPermission: PermissionKeys.ViewCollection
        },
        {
          id: 'Promo Code',
          title: <FormattedMessage id="Promo Code" />,
          type: 'item',
          url: '/promo-code',
          icon: icons.promo,
          requiredPermission: PermissionKeys.ViewPromoCode
        },
        {
          id: 'Reviews',
          title: <FormattedMessage id="Reviews" />,
          type: 'item',
          url: '/reviews',
          icon: icons.review,
          requiredPermission: PermissionKeys.ViewReview
        }
      ]
    },
    {
      id: 'User Management',
      title: <FormattedMessage id="User Management" />,
      type: 'collapse',
      icon: icons.usermanagement,
      children: [
        {
          id: 'Sub Admins',
          title: <FormattedMessage id="Sub Admins" />,
          type: 'item',
          url: '/sub-admins',
          icon: icons.admin,
          requiredPermission: PermissionKeys.ViewSubAdmin
        },
        {
          id: 'Seller',
          title: <FormattedMessage id="Seller" />,
          type: 'item',
          url: '/seller',
          icon: icons.profile,
          requiredPermission: PermissionKeys.ViewSeller
        },
        {
          id: 'Customer',
          title: <FormattedMessage id="Customer" />,
          type: 'item',
          url: '/customers',
          icon: icons.customer,
          requiredPermission: PermissionKeys.ViewCustomer
        }
      ]
    },
    {
      id: 'Subscription',
      title: <FormattedMessage id="Subscription" />,
      type: 'collapse',
      icon: icons.subscription,
      children: [
        {
          id: 'Plan',
          title: <FormattedMessage id="Plan" />,
          type: 'item',
          url: '/plan',
          icon: icons.plan,
          requiredPermission: PermissionKeys.ViewPlan
        },
        {
          id: 'Features',
          title: <FormattedMessage id="Features" />,
          type: 'item',
          url: '/features',
          icon: icons.feature,
          requiredPermission: PermissionKeys.ViewPlan
        }
      ]
    },
    {
      id: 'Contents',
      title: <FormattedMessage id="Contents" />,
      type: 'collapse',
      children: [
        {
          id: 'Home',
          title: <FormattedMessage id="Home" />,
          type: 'item',
          url: '/contents/home-page',
          requiredPermission: PermissionKeys.ViewFaq
        },
        {
          id: 'FAQ',
          title: <FormattedMessage id="FAQ" />,
          type: 'item',
          url: '/contents/faq',
          requiredPermission: PermissionKeys.ViewFaq
        },
        {
          id: 'Legals',
          title: <FormattedMessage id="Legals" />,
          type: 'item',
          url: '/contents/legals',
          requiredPermission: PermissionKeys.ViewLegals
        }
      ]
    },
    {
      id: 'Marketing',
      title: <FormattedMessage id="Marketing" />,
      type: 'collapse',
      icon: icons.market,
      children: [
        {
          id: 'Campaigns',
          title: <FormattedMessage id="Campaigns" />,
          type: 'item',
          url: '/marketing/campaigns',
          requiredPermission: PermissionKeys.ViewCampaign
        },
        {
          id: 'Groups',
          title: <FormattedMessage id="Groups" />,
          type: 'item',
          url: '/marketing/groups',
          requiredPermission: PermissionKeys.ViewCampaign
        }
      ]
    },
    {
      id: 'Services',
      title: <FormattedMessage id="Services" />,
      type: 'collapse',
      icon: icons.service,
      children: [
        {
          id: 'Ecomduke Service',
          title: <FormattedMessage id="Ecomduke Service" />,
          type: 'item',
          url: '/ecomduke-service',
          requiredPermission: PermissionKeys.ViewEcomdukeService
        },
        {
          id: 'Ecomduke Service Request',
          title: <FormattedMessage id="Ecomduke Service Request" />,
          type: 'item',
          url: '/service-request',
          requiredPermission: PermissionKeys.ViewServiceRequest
        }
      ]
    },
    {
      id: 'Settings',
      title: <FormattedMessage id="Settings" />,
      type: 'collapse',
      icon: icons.settings,
      children: [
        {
          id: 'Tax Category',
          title: <FormattedMessage id="Tax Category" />,
          type: 'item',
          url: '/settings/tax-category',
          icon: icons.tax,
          requiredPermission: PermissionKeys.ViewFaq
        },
        {
          id: 'Configuration',
          title: <FormattedMessage id="Configuration" />,
          type: 'item',
          url: '/settings/configuration',
          icon: icons.configure,
          requiredPermission: PermissionKeys.ViewConfiguration
        }
      ]
    }
  ]
};

export default pages;
