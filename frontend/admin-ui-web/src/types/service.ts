export interface EcomDukeService {
  id?: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  taxCategoryId: string;
  isActive: boolean;
  createdOn?: string;
  updatedOn?: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface EcomDukeServiceRequest {
  id?: string;
  sellerId: string;
  ecomdukeserviceId: string;
  status: string;
  paymentReference?: number;
  paidAmount?: number | string; // Since API sometimes returns string
  paidOn?: string;
  notes?: string;
  createdOn?: string;
  updatedOn?: string;
  createdBy?: string;
  updatedBy?: string;

  // Related entities
  seller?: {
    id: string;
    sellerId: string;
    userTenant?: {
      id: string;
      user?: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        photoUrl?: string;
      };
    };
  };

  ecomdukeservice?: {
    id: string;
    name: string;
    description?: string;
    price: number | string;
    currency: string;
  };
}
