// ==============================|| KANBAN TYPES ||============================== //

export interface KanbanColumn {
  id: string;
  title: string;
  itemIds: string[];
}

export interface KanbanItem {
  id: string;
  title: string;
  description: string;
  image?: string;
  priority?: number;
  dueDate?: string;
  assignedTo?: string;
  comments?: KanbanComment[];
}

export interface KanbanComment {
  id: string;
  comment: string;
  profileId: string;
  time: string;
}

export interface KanbanProfile {
  id: string;
  avatar: string;
  name: string;
  time: string;
}

export interface KanbanUserStory {
  id: string;
  title: string;
  itemIds?: string[];
}

export interface KanbanBoard {
  columns: KanbanColumn[];
  columnsOrder: string[];
  items: KanbanItem[];
  profiles: KanbanProfile[];
  userStory: KanbanUserStory[];
}

export interface KanbanMaster {
  selectedItem: string | false;
}

export interface KanbanState {
  board: KanbanBoard;
  master: KanbanMaster;
  isLoading: boolean;
  error: string | null;
}
