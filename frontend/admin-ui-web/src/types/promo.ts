import { DiscountType, PromoApplicability, PromoTag } from 'enums/promo-code.enum';

export interface PromoCode {
  id: string;
  code: string;
  type: DiscountType;
  value: number;
  appliesTo: PromoApplicability;
  minCartValue?: number;
  maxDiscountCap?: number;
  usageLimitPerUser?: number;
  usageLimitTotal?: number;
  isFirstTimeUserOnly?: boolean;
  isRepeatUserOnly?: boolean;
  validFrom: string;
  validTill: string;
  visibility: PromoTag;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  products?: Product[];
  categories?: Collection[];
  categoryIds?: string[];
  productIds?: string[];
  promoCodeProducts?: PromoProduct[];
  promoCodeCollections?: PromoCollection[];
}

export interface Product {
  id?: string;
  name?: string;
}

export interface Collection {
  id?: string;
  name?: string;
}

export interface PromoCodeFormProps {
  isEdit?: boolean;
  promoCodeId?: string;
  initialValues?: {
    code: string;
    type: DiscountType;
    value: number;
    appliesTo: PromoApplicability;
    minCartValue?: number;
    maxDiscountCap?: number;
    usageLimitPerUser?: number;
    usageLimitTotal?: number;
    isFirstTimeUserOnly?: boolean;
    isRepeatUserOnly?: boolean;
    validFrom: string;
    validTill: string;
    visibility: PromoTag;
    isActive?: boolean;
    productIds?: string[];
    categoryIds?: string[];
    promoCodeProducts?: PromoProduct[];
    promoCodeCollections?: PromoCollection[];
  };
  refetch: () => void;
}

export interface PromoCodePayload {
  code: string;
  type: DiscountType;
  value: number;
  appliesTo: PromoApplicability;
  productIds?: string[];
  categoryIds?: string[];
  minCartValue?: number;
  maxDiscountCap?: number;
  usageLimitPerUser?: number;
  usageLimitTotal?: number;
  isFirstTimeUserOnly?: boolean;
  isRepeatUserOnly?: boolean;
  validFrom: string;
  validTill: string;
  visibility: PromoTag;
  isActive?: boolean;
  promoCodeProducts?: PromoProduct[];
  promoCodeCollections?: PromoCollection[];
}

export interface PromoProduct {
  id: string;
  promoCodeId: string;
  productId: string;
  product?: Product;
  deleted?: boolean;
}

export interface PromoCollection {
  id: string;
  promoCodeId: string;
  collectionId: string;
  collection?: Collection;
  deleted?: boolean;
}
