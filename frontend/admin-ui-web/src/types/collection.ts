import { Asset } from './product';

export interface Collection {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  isRoot: boolean;
  position: number;
  isPrivate: boolean;
  filters: Record<string, unknown> | null;
  inheritFilters: boolean;
  parentId: string | null;
  featuredAssetId: string;
  featuredAsset: Asset;
  childrens: Collection[];
  parent: Collection;
  taxCategory: Collection;
  status: string;
}
