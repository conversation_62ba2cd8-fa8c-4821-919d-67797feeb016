export enum SectionType {
  CAROUSEL = 'carousel',
  FEATURED_PRODUCTS = 'featured_products',
  BANNER = 'banner',
  CATEGORY_GRID = 'category_grid',
  TEXT_BLOCK = 'text_block',
  OCCASION_CARDS = 'occasion-cards',
  FEATURED_COLLECTION = 'featured-collection',
  PRODUCT_CARDS = 'product-cards',
  RECENTLY_VIEWED = 'recently-viewed',
  MOST_VIEWED = 'most-viewed',
  PRODUCT_LIST = 'product-list',
  CATEGORY_LIST = 'category-list',
  IMAGE_CARDS = 'image-cards',
  OFFER_CARDS = 'offer-cards',
  CATEGORY_CARDS = 'category-cards',
  TOP_SELLING = 'top-selling',
  PRODUCT_FILTER = 'product-filter',
  ALL_CATEGORIES = 'all-categories',
  FACETS = 'facets',
  GIFT_PRODUCTS = 'gift-products'
}

export enum SectionItemType {
  PRODUCT = 'product',
  CATEGORY = 'category',
  IMAGE = 'image',
  CUSTOM_CARD = 'custom_card',
  COLLECTION = 'collection',
  TEXT = 'text',
  FACET_VALUES = 'facet-values'
}

export enum CardStyle {
  BASIC = 'basic',
  IMAGE_ONLY = 'image-only',
  IMAGE_TITLE = 'image-title',
  IMAGE_TITLE_SUBTITLE = 'image-title-subtitle',
  OFFER_CARD = 'offer-card',
  OFFER_CARD_INVERSE = 'offer-card-inverse',
  OCCASION_CARD = 'occasion-card',
  COLLECTION_CARD = 'collection-card',
  PRODUCT_CARD = 'product-card',
  CATEGORY_CARD = 'category-card'
}

export enum PageType {
  HOME = 'home',
  SELL_ON_ECOMDUKES = 'sell-on-ecomdukes',
  ABOUT_US = 'about-us',
  CONTACT_US = 'contact-us'
}

export interface PageSectionDto extends PageSection {
  items?: SectionItem[];
}

export interface BulkSection {
  sections: PageSectionDto[];
}

export interface PageSection {
  id: string;
  type: SectionType;
  title: string;
  metadata?: object;
  cardStyle?: CardStyle;
  displayOrder: number;
  isActive: boolean;
  sectionItems?: SectionItem[];
  pageType: string;
}
export interface CarouselMetadata {
  redirectUrl?: string;
}

export interface FeaturedProductsMetadata {
  productIds?: string[];
}

export interface OccasionCardsMetadata {
  maxItems?: number;
  viewAllLink?: string;
}

export interface SectionItem {
  id: string;
  entityType: SectionItemType;
  entityId?: string;
  imageUrl?: string;
  title: string;
  subtitle?: string;
  content?: string;
  metadata?: SectionItemMetadata;
  displayOrder: number;
}

export interface BannerMetadata {
  redirectUrl?: string;
}

export interface CategoryGridMetadata {
  categoryId?: string;
}

export interface SectionItemMetadata {
  redirectUrl?: string;
  productIds?: string[];
  maxItems?: number;
  viewAllLink?: string;
  categoryId?: string;
  showRating?: boolean;
  showPrice?: boolean;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  richText?: string;
  filter?: object;
  facetValueIds?: string[];
}
