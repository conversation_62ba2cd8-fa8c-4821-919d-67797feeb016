export enum ApiTagTypes {
  User = 'User',
  Product = 'Product',
  Order = 'Order',
  ShippingProfiles = 'ShippingProfiles',
  ShippingMethods = 'ShippingMethods',
}
export enum OrderItemStatus {
  New = 'new',
  Accepted = 'accepted',
  Processing = 'processing',
  Dispatched = 'dispatched',
  Rejected = 'rejected',
  ReturnRefund = 'return_refund',
  Delivered = 'delivered',
  RefundCompleted = 'refund_completed',
  Pending = 'pending',
}
export const StatusLabelMap: Record<string, OrderItemStatus[]> = {
  New: [OrderItemStatus.New],
  Accepted: [OrderItemStatus.Accepted],
  Processing: [OrderItemStatus.Processing],
  Dispatched: [OrderItemStatus.Dispatched],
  Rejected: [OrderItemStatus.Rejected],
  ReturnRefund: [OrderItemStatus.ReturnRefund],
  Delivered: [OrderItemStatus.Delivered],
  RefundCompleted: [OrderItemStatus.RefundCompleted],
};
