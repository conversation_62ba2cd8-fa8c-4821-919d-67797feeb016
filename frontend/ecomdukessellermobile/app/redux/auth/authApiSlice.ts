import {ApiSliceIdentifier} from '../../constants/enums';
import {
  BankDetailsType,
  CashFreeVendor,
  ChangePasswordRequest,
} from '../../types/auth';
import {Gender} from '../../types/profile';
import {apiSlice} from '../apiSlice';
import {getItem} from '../mmkvStorage';
import {User} from './user.model';

export interface ILoginForm {
  username: string;
  password: string;
}

export interface ICredentials extends ILoginForm {
  client_id: string;
}

export interface IKeycloakAuthRedirectUrl {
  redirectUrl: string;
}
export interface ISignupForm {
  firstName: string;
  lastName: string;
  password: string;
  email: string;
  phoneNumber: string;
}
interface IEmailVerificationForm {
  key: string;
  otp: string;
}
interface ISMSVerificationForm {
  key: string;
  otp: string;
}
interface IVerificationResponse {
  message: string;
}
export interface ITokenResponse {
  accessToken: string;
  refreshToken: string;
  expires: number;
}

export interface ITokenExchangeRequest {
  code: string;
}
type ImageFile = {
  path: string;
  mime: string;
  filename: string;
};

interface ProductImage {
  name: string;
  thumbnail: ImageFile[] | string;
}
interface IsellerOnboardForm {
  storeName: string;
  website: string;
  fbId: string;
  instaId: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  sampleProductImages?: ProductImage[];
}
export interface ISendOtpForm {
  client_id: string;
  client_secret: string;
  key: string;
}
export interface Seller {
  id: string;
  sellerId: string;
  userTenantId: string;
  createdBy: string;
  createdOn: string;
  modifiedBy: string;
  modifiedOn: string;
  deleted: boolean;
  deletedBy: string | null;
  deletedOn: string | null;
  emailVerified: boolean;
  phoneVerified: boolean;
  verificationCode: string;
  status: string;
  rejectionReason: string;
}
export type FirmDetailsType = {
  isPanNumberAvailable: string;
  isGstNumberAvailable: string;
  isEnrollmentNumberAvailable: string;
  panNumber: string;
  gstNumber: string;
  typeOfFirm: string;
  sellerId: string;
  gstName: string;
  enrollmentNumber: string;
  firmDocuments: {[key: string]: File[]} | null;
};
type ImageValue = string | {path: string; mime: string; filename: string};
export interface ISellerStore {
  id?: string;
  storeName: string;
  website?: string;
  signature?: ImageValue;
  description?: string;
  fbId?: string;
  instaId?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  dp?: ImageValue;
  banner?: ImageValue;
  logo?: ImageValue;
  sellerId: string;
  storeDescription?: string;
}
export interface FirmDocument {
  id?: string;
  documentName: string;
  documentValue: string;
}
export interface IFirmDetails {
  id?: string;
  gstNumber?: string;
  gstName: string;
  website?: string;
  panNumber?: string;
  typeOfFirm?: string;
  firmDocuments?: {documentName: string; documentValue: string}[];
  firmDocumentsRelation?: FirmDocument[];
  enrollmentNumber?: string;
}
export interface Faq {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string;
  createdBy: string | null;
  modifiedBy: string | null;
  id: string;
  question: string;
  answer: string;
  category: string;
  status: number;
  priority: number;
  visibility: number;
}
export interface Profile {
  firstName: string;
  lastName: string;
  designation: string;
  email: string;
  phone: string;
  gender: Gender | null;
  dob: string;
  sellerId: string;
}

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    login: builder.mutation({
      query: (body: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {
          ...body,
          client_id: 'email_password_client',
          client_secret:
            '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    exchangeToken: builder.mutation<ITokenResponse, ITokenExchangeRequest>({
      query: body => ({
        url: '/auth/token',
        method: 'POST',
        body: {
          clientId: 'email_password_client',
          code: body.code,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        headers: {
          Authorization: `Bearer ${body.code}`,
        },
      }),
    }),
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/auth/logout',
        method: 'POST',
        body: {refreshToken},
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getUser: builder.query<User, void>({
      query: () => ({
        url: '/auth/me',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    signUp: builder.mutation({
      query: ({
        token,
        signupData,
      }: {
        token: string;
        signupData: ISignupForm;
      }) => ({
        url: '/auth/sign-up/create-user',
        method: 'POST',
        body: {
          firstName: signupData.firstName,
          lastName: signupData.lastName,
          password: signupData.password,
          email: signupData.email,
          phoneNumber: signupData.phoneNumber,
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    createSignUpToken: builder.mutation({
      query: (email: string) => ({
        url: '/auth/sign-up/create-token',
        method: 'POST',
        cache: 'no-cache',
        body: {
          email,
          data: {
            client_id: 'email_password_client',
            client_secret:
              '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
          },
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    verifyEmail: builder.mutation<
      IVerificationResponse,
      IEmailVerificationForm
    >({
      query: (body: IEmailVerificationForm) => ({
        url: '/auth/verify-otp?type=1',
        method: 'POST',
        body: {
          ...body,
          clientId: 'email_password_client',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    verifySMS: builder.mutation<IVerificationResponse, ISMSVerificationForm>({
      query: (body: ISMSVerificationForm) => ({
        url: '/auth/verify-otp?type=2',
        method: 'POST',
        body: {
          ...body,
          clientId: 'email_password_client',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),

    sendOtpEmail: builder.mutation({
      query: (email: string) => ({
        url: '/auth/send-otp?type=1',
        method: 'POST',
        body: {
          key: email,
          client_id: 'email_password_client',
          client_secret:
            '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    sendOtpSMS: builder.mutation({
      query: (phone: string) => ({
        url: '/auth/send-otp?type=2',
        method: 'POST',
        body: {
          key: phone,
          client_id: 'email_password_client',
          client_secret:
            '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),
    getSeller: builder.query<Seller[], {userTenantId: string}>({
      query: ({userTenantId}) => ({
        url: `/sellers?filter=${encodeURIComponent(
          JSON.stringify({where: {userTenantId}, limit: 1}),
        )}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    getSellerById: builder.query<Seller, string>({
      query: sellerId => ({
        url: `/sellers/${sellerId}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    forgotPassword: builder.mutation({
      query: (email: string) => ({
        url: '/auth/forget-password',
        method: 'POST',
        body: {
          username: email,
          client_id: 'email_password_client',
          client_secret:
            '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
      }),
    }),

    googleAuthCallback: builder.mutation<ITokenResponse, {code: string}>({
      query: ({code}) => ({
        url: '/auth/google/callback',
        method: 'POST',
        params: {
          state: 'client_id=google_seller_client',
        },
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        headers: {
          Authorization: `Bearer ${code}`,
        },
      }),
    }),
    getFaqs: builder.query<Faq[], void>({
      query: () => ({
        url: '/faqs',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: {inq: [2, 0]},
            },
            order: ['priority DESC'],
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    updateUser: builder.mutation<void, {userId: string; body: Partial<User>}>({
      query: ({userId, body}) => {
        const formData = new FormData();
        Object.entries(body).forEach(([key, value]) => {
          if (key !== 'photoUrl' && value !== undefined) {
            formData.append(key, String(value));
          }
        });
        if (body?.photoUrl && typeof body?.photoUrl === 'object') {
          formData.append('photoUrl', {
            uri: body?.photoUrl.path,
            type: body?.photoUrl.mime,
            name: body?.photoUrl.filename,
          });
        }
        return {
          url: `/profile/${userId}`,
          method: 'PATCH',
          body: formData,
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          formData: true,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
    }),
    addBankDetails: builder.mutation<
      void,
      {sellerId: string; body: BankDetailsType}
    >({
      query: ({sellerId, body}) => ({
        url: `/sellers/${sellerId}/bank-details`,
        method: 'POST',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    getVendor: builder.query<CashFreeVendor, void>({
      query: () => ({
        url: '/sellers/bank-details',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    updatePassword: builder.mutation<
      void,
      Omit<ChangePasswordRequest, 'refreshToken'>
    >({
      query: body => {
        const refreshToken = getItem('refreshToken');
        return {
          url: '/auth/change-password',
          method: 'PATCH',
          body: {
            ...body,
            refreshToken,
          },
          apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        };
      },
    }),
  }),
});

export const {
  useLoginMutation,
  useExchangeTokenMutation,
  useLogoutMutation,
  useGetUserQuery,
  useLazyGetUserQuery,
  useSignUpMutation,
  useCreateSignUpTokenMutation,
  useVerifyEmailMutation,
  useVerifySMSMutation,
  useSendOtpEmailMutation,
  useSendOtpSMSMutation,
  useGetSellerQuery,
  useLazyGetSellerQuery,
  useGetSellerByIdQuery,
  useLazyGetSellerByIdQuery,
  useForgotPasswordMutation,
  useGoogleAuthCallbackMutation,
  useGetFaqsQuery,
  useUpdateUserMutation,
  useAddBankDetailsMutation,
  useGetVendorQuery,
  useUpdatePasswordMutation,
} = authApiSlice;
