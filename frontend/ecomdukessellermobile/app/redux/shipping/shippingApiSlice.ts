import {ApiSliceIdentifier} from '../../constants/enums';
import {IFilter} from '../../types/filter';
import {ShippingMethod, ShippingProfile} from '../../types/shipping';
import {buildFilterParams} from '../../utils/buildFilterParams';
import {apiSlice} from '../apiSlice';
import {ApiTagTypes} from '../types';

export const shippingApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getShippingProfiles: builder.query<
      ShippingProfile[],
      {sellerId: string; filter?: IFilter}
    >({
      query: ({sellerId, filter}) => ({
        url: `/seller-shipping-profiles/seller/${sellerId}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined),
        },
      }),
      providesTags: [ApiTagTypes.ShippingProfiles],
    }),

    getShippingProfile: builder.query<ShippingProfile, string>({
      query: profileId => ({
        url: `/seller-shipping-profiles/${profileId}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      providesTags: [ApiTagTypes.ShippingProfiles],
    }),

    createShippingProfile: builder.mutation<
      ShippingProfile,
      Partial<ShippingProfile>
    >({
      query: data => ({
        url: '/seller-shipping-profiles',
        method: 'POST',
        body: data,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      invalidatesTags: [ApiTagTypes.ShippingProfiles],
    }),

    updateShippingProfile: builder.mutation<
      ShippingProfile,
      {id: string; body: Partial<ShippingProfile>}
    >({
      query: ({id, body}) => ({
        url: `/seller-shipping-profiles/${id}`,
        method: 'PATCH',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      invalidatesTags: [ApiTagTypes.ShippingProfiles],
    }),

    deleteShippingProfile: builder.mutation<void, string>({
      query: id => ({
        url: `/seller-shipping-profiles/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      invalidatesTags: [ApiTagTypes.ShippingProfiles],
    }),

    getShippingMethods: builder.query<ShippingMethod[], void>({
      query: () => ({
        url: '/shipping-methods',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
      providesTags: [ApiTagTypes.ShippingMethods],
    }),
  }),
});

export const {
  useGetShippingProfilesQuery,
  useGetShippingProfileQuery,
  useCreateShippingProfileMutation,
  useUpdateShippingProfileMutation,
  useDeleteShippingProfileMutation,
  useGetShippingMethodsQuery,
} = shippingApiSlice;
