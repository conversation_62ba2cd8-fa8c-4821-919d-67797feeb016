import type {BaseQueryFn, FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {AuthResData, setCredentials, unsetCredentials} from './auth/authSlice';
import type {RootState} from './appstore';
import {getItem, setItem} from './mmkvStorage';
import {getBaseUrl} from './redux.helper';
import {ApiSliceIdentifier} from '../constants/enums';
import {handleApiError} from '../hooks/useApiErrorHandler';

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const authEndpoints = [
  'verifyOtp',
  'login',
  'sendOtp',
  'signUp',
  'createSignUpToken',
  'verifyEmail',
  'sendOtpEmail',
  'verifySMS',
];
const baseQueryWithReauth: BaseQueryFn<
  {
    url: string;
    method?: string;
    body?: any;
    apiSliceIdentifier?: ApiSliceIdentifier;
    formData?: boolean;
  },
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;

  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers) {
      const token = getItem('accessToken');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('x-origin', 'ecomdukes-seller');
      if (args.formData) {
        headers.set('Content-Type', 'multipart/form-data');
      } else {
        headers.set('Content-Type', 'application/json');
      }
      return headers;
    },
  });
  console.log({args, api, extraOptions});
  // Log cURL
  const curlParts = [
    `curl -X ${args.method || 'GET'}`,
    `'${baseUrl}${args.url}'`,
  ];

  const token = getItem('accessToken');
  const headers: Record<string, string> = {
    'x-origin': 'ecomdukes-seller',
    Authorization: token ? `Bearer ${token}` : '',
    'Content-Type': args.formData ? 'multipart/form-data' : 'application/json',
  };

  Object.entries(headers).forEach(([key, value]) => {
    if (value) curlParts.push(`-H '${key}: ${value}'`);
  });

  if (args.body && typeof args.body === 'object') {
    curlParts.push(`-d '${JSON.stringify(args.body)}'`);
  }

  console.log('\n🌐 cURL Request:\n', curlParts.join(' \\\n'), '\n');
  let result = await baseQuery(args, api, extraOptions);

  if (
    result.error?.status === RESULT_ERROR_STATUS &&
    !authEndpoints.includes(api.endpoint)
  ) {
    // try to get a new token
    const refreshResult = await baseQuery(
      {
        url: getBaseUrl(state) + '/auth/token-refresh',
        method: 'POST',
        body: {refreshToken: getItem('refreshToken')},
      },
      api,
      extraOptions,
    );

    if (refreshResult.data) {
      api.dispatch(setCredentials(refreshResult.data as AuthResData));
      result = await baseQuery(args, api, extraOptions);
    } else {
      api.dispatch(unsetCredentials());
    }
  }
  if (result?.error) {
    handleApiError(result.error);
  }
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: builder => ({}),
});
