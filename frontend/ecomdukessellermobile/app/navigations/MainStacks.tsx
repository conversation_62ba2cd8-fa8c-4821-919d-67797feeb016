import {Image, TouchableOpacity} from 'react-native';
import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {Images} from '../assets/images';
import CompanyUpdateScreen from '../screens/main/Company/CompanyUpdateScreen';
import FaqScreen from '../screens/main/FAQ/FaqScreen';
import ProfileUpdateScreen from '../screens/main/Profile/ProfileUpdateScreen';
import FirmUpdateScreen from '../screens/main/Firm/FirmUpdate';
import PlansSectionScreen from '../screens/main/Plans/PlansSectionScreen';
import {SCREEN_NAME} from '../constants';
import SellerFormScreen from '../screens/onboard/StoreCreateScreen';
import CreateProductScreen from '../screens/main/CreateProduct/CreateProductScreen';
import BottomTabNavigation from './BottomTab';
import {Icon} from 'react-native-paper';
import {colors} from '../theme/colors';
import {Drawer} from 'react-native-drawer-layout';
import Sidemenu from '../components/SideMenu/Sidemenu';
import {MainStackParamList} from './types';
import {
  AddWarehouseScreen,
  WarehouseListScreen,
} from '../screens/main/Warehouse';
import WarehouseDetailScreen from '../screens/main/Warehouse/WarehouseDetailScreen';
import {
  PromoCodeCreateScreen,
  PromoCodeListScreen,
} from '../screens/main/Promocode';
import ProductDetailScreen from '../screens/main/ProductDetails/ProductDetailsScreen';
import BankDetailsScreen from '../screens/main/BankDetails/BankDetailsScreen';
import ChangePasswordScreen from '../screens/main/ChangePassword/ChangePasswordScreen';
import ShippingCreateScreen from '../screens/main/Shipping/ShippingCreateScreen';
import ShippingDetailsScreen from '../screens/main/Shipping/ShippingDetailsScreen';
import OrderDetailScreen from '../screens/main/Orders/OrderDetailScreen';
const MainStack = createNativeStackNavigator<MainStackParamList>();
const MainStacks = () => {
  const [open, setOpen] = React.useState(false);

  return (
    <Drawer
      drawerPosition="right"
      open={open}
      onOpen={() => {}}
      onClose={() => {
        setOpen(false);
      }}
      renderDrawerContent={() => {
        return (
          <Sidemenu
            closeDrawer={() => {
              setOpen(false);
            }}
          />
        );
      }}>
      <MainStack.Navigator
        screenOptions={{
          headerShown: true,
          headerBackVisible: true,
          headerTintColor: '#5847F9',
          headerBackTitle: '',
          headerBackButtonDisplayMode: 'minimal',
          headerRight: () => (
            <TouchableOpacity
              onPress={() => {
                setOpen(prevOpen => !prevOpen);
              }}>
              <Icon source="menu" color={colors.primary} size={25} />
            </TouchableOpacity>
          ),
          headerTitle: () => (
            <Image
              source={Images.logo} // Add your logo
              style={{width: 140, height: 80}} // Adjust size
            />
          ),
          headerTitleAlign: 'center',
        }}
        initialRouteName={SCREEN_NAME.MAIN_HOME}>
        <MainStack.Screen
          name={SCREEN_NAME.MAIN_HOME}
          component={BottomTabNavigation}
        />
        <MainStack.Screen
          name={SCREEN_NAME.COMPANY_UPDATE}
          component={CompanyUpdateScreen}
        />
        <MainStack.Screen name={SCREEN_NAME.FAQ} component={FaqScreen} />
        <MainStack.Screen
          name={SCREEN_NAME.PROFILE_UPDATE}
          component={ProfileUpdateScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.FIRM_UPDATE}
          component={FirmUpdateScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.SUBSCRIPTION}
          component={PlansSectionScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.SELLER_FORM}
          component={SellerFormScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.CREATE_PRODUCT}
          component={CreateProductScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.ADD_WAREHOUSE}
          component={AddWarehouseScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.WAREHOUSE_LIST}
          component={WarehouseListScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.WAREHOUSE_DETAIL}
          component={WarehouseDetailScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.PROMOCODE_LIST}
          component={PromoCodeListScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.PROMOCODE_CREATE}
          component={PromoCodeCreateScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.PRODUCT_DETAILS}
          component={ProductDetailScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.BANK_DETAILS}
          component={BankDetailsScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.CHANGE_PASSWORD}
          component={ChangePasswordScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.SHIPPING_CREATE}
          component={ShippingCreateScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.SHIPPING_DETAIL}
          component={ShippingDetailsScreen}
        />
        <MainStack.Screen
          name={SCREEN_NAME.ORDER_DETAIL}
          component={OrderDetailScreen}
        />
      </MainStack.Navigator>
    </Drawer>
  );
};

export default MainStacks;
