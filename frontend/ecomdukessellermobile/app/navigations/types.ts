import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Warehouse} from '../types/warehouse';
import {RouteProp} from '@react-navigation/native';
import {PromoCode} from '../types/promo';

export type AuthStackParamList = {
  login: undefined;
  register: undefined;
  forgotpassword: undefined;
  checkmail: undefined;
  termsAndCondition: undefined;
  privacyPolicy: undefined;
  registrationSuccessFul: {
    username: string;
    password: string;
  };
};
export type OnboardStackParamList = {
  emailCodeVerification: undefined;
  smsCodeVerification: undefined;
  sellerForm: undefined;
  preVerify: undefined;
  postVerify: undefined;
  forgotpassword: undefined;
  firmDetails: undefined;
  storeDetails: undefined;
};

export type MainStackParamList = {
  dashboard: undefined;
  firmUpdate: undefined;
  companyUpdate: undefined;
  faq: undefined;
  plansSection: undefined;
  mainHome: undefined;
  profileUpdate: undefined;
  subscription: undefined;
  sellerForm: undefined;
  createProduct: undefined;
  addWarehouse: {
    editData?: Partial<Warehouse | null | undefined>;
    isEditMode?: boolean;
  };
  wareHouseList: undefined;
  warehouseDetail: {
    warehouseDetailFromList?: Partial<Warehouse>;
  };
  promocodeList: undefined;
  promocodeCreate: {
    editData?: Partial<PromoCode | null | undefined>;
    isEditMode?: boolean;
  };
  productsTab: {productId?: string};
  productDetails: {productId: string};
  bankDetails: undefined;
  changePassword: undefined;
  shippingCreate: undefined;
  shippingDetail: undefined;
  orderListing: undefined;
  orderDetail: undefined;
};

export type LoginScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'login'
>;
export type RegistrationSuccessFulScreenNavigationProp =
  NativeStackNavigationProp<AuthStackParamList, 'registrationSuccessFul'>;
export type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'forgotpassword'
>;
export type CheckMailScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'checkmail'
>;
export type RegisterScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'register'
>;
export type TermsAndConditionNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'termsAndCondition'
>;
export type PrivacyPolicyNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'privacyPolicy'
>;
export type EmailCodeVerificationScreenNavigationProp =
  NativeStackNavigationProp<OnboardStackParamList, 'emailCodeVerification'>;
export type SMSCodeVerificationScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'smsCodeVerification'
>;
export type SellerFormScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'sellerForm'
>;
export type PreVerifyScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'preVerify'
>;
export type PostVerifyScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'postVerify'
>;
export type StoreDetailsScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'storeDetails'
>;
export type FirmDetailsScreenNavigationProp = NativeStackNavigationProp<
  OnboardStackParamList,
  'firmDetails'
>;
export type DashboardScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'dashboard'
>;
export type CompanyUpdateScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'companyUpdate'
>;
export type FirmUpdateScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'firmUpdate'
>;
export type FAQScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'faq'
>;
export type PlansSectionScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'plansSection'
>;
export type AddWarehouseScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'addWarehouse'
>;
export type AddPromoCodeScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'promocodeCreate'
>;
export type AddWarehouseScreenRouteProp = RouteProp<
  MainStackParamList,
  'addWarehouse'
>;
export type RegistrationSuccessFulScreenRouteProp = RouteProp<
  AuthStackParamList,
  'registrationSuccessFul'
>;
export type AddPromoCodeScreenRouteProp = RouteProp<
  MainStackParamList,
  'promocodeCreate'
>;

export type WarehouseDetailScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'warehouseDetail'
>;

export type WarehouseDetailScreenRouteProp = RouteProp<
  MainStackParamList,
  'warehouseDetail'
>;
export type WarehouseListScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'wareHouseList'
>;
export type PromocodeListScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'promocodeList'
>;
export type ProductScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'productsTab'
>;
export type ProductDetailsScreenNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'productDetails'
>;
