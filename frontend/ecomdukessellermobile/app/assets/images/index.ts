export const icon = require('./icon.png');
export const logo = require('./logo.png');
export const logo2 = require('./logo2.png');
export const logowithouttag = require('./logowithouttag.png');
export const logowithtag = require('./logowithtag.png');
export const typo = require('./typo.png');
export const Images = {
  icon: require('./icon.png'),
  logo: require('./logo.png'),
  logo2: require('./logo2.png'),
  logowithouttag: require('./logowithouttag.png'),
  logowithtag: require('./logowithtag.png'),
  typo: require('./typo.png'),
  animationLoader: require('./AnimationLoader.gif'),
  celebration: require('./confetti.png'),
  google: require('./google.png'),
  facebook: require('./facebook.png'),
  order: require('./orderIcon.png'),
  product: require('./productIcon.png'),
  sales: require('./salesIcon.png'),
  chart: require('./chart.png'),
  box: require('./box.png'),
  orders: require('./orders.png'),
  placeholder: require('./placeholder.png'),
  bottomBar_home: require('./home.png'),
  bottomBar_orders: require('./order.png'),
  bottomBarProducts: require('./product.png'),
  bottomBar_notification: require('./notification.png'),
};
