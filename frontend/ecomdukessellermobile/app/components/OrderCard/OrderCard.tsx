import {
  Image,
  Modal,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {use, useCallback, useState} from 'react';
import customColors from '../../theme/customColors';
import {colors} from '../../theme/colors';
import {Icon, MD3Colors, Text} from 'react-native-paper';
import {OrderLineItem} from '../../types/order';
import {
  useLazyGetOrderDetailsByIdQuery,
  useUpdateOrderMutation,
} from '../../redux/order/orderApiSlice';
import CustomButton from '../CustomButton/ContainedButton';
import {OrderItemStatus} from '../../redux/types';
import Toast from 'react-native-toast-message';
import CustomTextInput from '../InputFields/Textinput';

type Props = {
  item: OrderLineItem;
  index: number;
  loadList: () => void;
};
const filter = {
  include: [
    {
      relation: 'order',
      required: true,
      scope: {
        include: [
          {
            relation: 'promoCode',
          },
          {relation: 'shippingAddress'},
        ],
      },
    },
    {
      relation: 'productVariant',
      required: true,
      scope: {
        include: [
          {
            relation: 'product',
          },
          {
            relation: 'featuredAsset',
          },
        ],
      },
    },
    {
      relation: 'customizationValues',
      scope: {
        include: [
          {
            relation: 'customizationField',
          },
        ],
      },
    },
  ],
};
const Details = ({
  label1,
  value1,
}: {
  label1: string;
  value1: string | undefined;
}) => {
  return (
    <View style={styles.detailRowContainer}>
      <Text variant="bodyMedium" style={styles.labelText}>
        {label1}
      </Text>
      <Text variant="labelLarge" style={styles.valueText}>
        {value1 ? ': ' + value1 : ': -'}
      </Text>
    </View>
  );
};
const OrderCard = ({item, index, loadList}: Props) => {
  const [orderDetails, setOrderDetails] = useState<OrderLineItem | null>(null);
  const [getOrderDetails, {loading: isLoading}] =
    useLazyGetOrderDetailsByIdQuery();
  const [updateSellerStatus, {error: updateError, reset: updateReset}] =
    useUpdateOrderMutation();
  const [rejectionReason, setRejectionReason] = React.useState('');
  const [isOrderDetailsVisible, setIsOrderDetailsVisible] = useState(false);
  const [isREjectModalVisible, setIsRejectModalVisible] = useState(false);
  const [reasonText, setReasonText] = useState('');

  const getOrderDetailsById = (id: string) => {
    getOrderDetails({
      id: id,
      filter,
    })
      .unwrap()
      .then(newData => {
        setOrderDetails(newData);
        if (newData?.id) {
          setIsOrderDetailsVisible(true);
        }
        console.log('Order details fetched successfully:', newData);
      });
  };
  const handleUpdateStatus = useCallback(
    async (status: OrderItemStatus) => {
      if (!orderDetails?.id) return;
      await updateSellerStatus({
        id: orderDetails.id,
        rejectionReason: rejectionReason ?? '',
        data: {status},
      }).unwrap();
      setIsRejectModalVisible(false);
      setRejectionReason('');
      loadList();
      getOrderDetailsById(item.id);
      Toast.show({
        type: 'success',
        text1: 'Order status updated successfully',
      });
    },
    [rejectionReason, orderDetails],
  );

  return (
    <View style={styles.card}>
      <View style={styles.headerContainer}>
        <Image
          source={{
            uri: item?.productVariant?.featuredAsset?.previewUrl,
          }}
          style={styles.image}
        />
        <Text
          style={{
            flex: 1,
          }}
          variant="labelLarge">
          {item.productVariant.name}
        </Text>
        <TouchableOpacity
          onPress={() => {
            if (isOrderDetailsVisible) {
              setIsOrderDetailsVisible(false);
              return;
            }
            getOrderDetailsById(item?.id);
          }}>
          <Icon source="chevron-right" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>
      <View
        style={{
          padding: 12,
          gap: 10,
        }}>
        <Details label1="Status" value1={item.status.toUpperCase()} />
        <Details label1="Quantity" value1={item?.quantity?.toString()} />
        {orderDetails?.id && isOrderDetailsVisible && (
          <View
            style={{
              gap: 10,
            }}>
            <Details
              label1="Product ID"
              value1={orderDetails?.productVariant?.product?.productId}
            />
            <Details label1="Product price" value1={orderDetails?.unitPrice} />
            <Details
              label1="Delivery Details"
              value1={`${orderDetails.order?.shippingAddress?.addressLine1 + ', ' ?? ''} ${orderDetails.order?.shippingAddress?.addressLine2 + ', ' ?? ''}\n${orderDetails?.order?.shippingAddress?.locality + ', ' ?? ''}${orderDetails?.order?.shippingAddress?.city + ', ' ?? ''}${orderDetails?.order?.shippingAddress?.state + ' - ' ?? ''}${orderDetails?.order?.shippingAddress?.zipCode + ', ' ?? ''} ${orderDetails?.order?.shippingAddress?.country ?? ''}`}
            />
            <Details
              label1="Offers Applied"
              value1={orderDetails.order?.promoCode?.code ?? '---'}
            />
            <Details
              label1="Customize"
              value1={orderDetails.order?.promoCode?.code ?? '---'}
            />
            <Details label1="Note" value1={'---'} />

            <Details
              label1="Wrapping"
              value1={
                orderDetails.productVariant?.product?.isGiftWrapAvailable ===
                true
                  ? 'Yes'
                  : orderDetails.productVariant?.product
                        ?.isGiftWrapAvailable === false
                    ? 'No'
                    : '---'
              }
            />
            <Details label1="Payment" value1={orderDetails.totalPrice} />
            {orderDetails.status === 'rejected' && (
              <Details
                label1="Reason for Reject"
                value1={orderDetails.rejectionReason ?? 'No reason provided'}
              />
            )}
            {orderDetails.status === OrderItemStatus.Pending && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <CustomButton
                  title="Accept Order"
                  onPress={() => {
                    handleUpdateStatus(OrderItemStatus.Accepted);
                  }}
                />
                <CustomButton
                  title="Reject Order"
                  buttonColor={MD3Colors.error50}
                  onPress={() => {
                    setIsRejectModalVisible(true);
                  }}
                />
              </View>
            )}
          </View>
        )}
      </View>
      <Modal
        animationType="slide"
        transparent={true}
        visible={isREjectModalVisible}
        onRequestClose={() => {
          setIsRejectModalVisible(false);
        }}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <View>
              <Pressable
                style={{
                  position: 'absolute',
                  right: 10,
                }}
                onPress={() => {
                  setIsRejectModalVisible(false);
                  setRejectionReason('');
                }}>
                <Icon source="close" size={24} color={colors.primary} />
              </Pressable>
            </View>
            <Text
              style={{
                marginBottom: 20,
                textAlign: 'center',
              }}
              variant="titleMedium">
              Reject Order
            </Text>

            <CustomTextInput
              title="Reject Reason"
              placeholder="Enter reason for rejection"
              value={rejectionReason}
              onChangeText={text => setRejectionReason(text)}
              multiline={true}
              textInputStyle={{
                height: 150,
              }}
              containerStyle={{
                height: 150,
                width: '100%',
                marginBottom: 20,
              }}
            />
            <CustomButton
              title="Reject Order"
              //   buttonColor={MD3Colors.error50}
              onPress={() => {
                if (!rejectionReason) {
                  Toast.show({
                    type: 'error',
                    text1: 'Please provide a reason for rejection',
                  });
                  return;
                }
                handleUpdateStatus(OrderItemStatus.Rejected);
              }}
            />
            {/* <Pressable
              onPress={() => {
                setIsRejectModalVisible(false);
              }}>
              <Text>Hide Modal</Text>
            </Pressable> */}
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default OrderCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    // padding: 12,
    marginBottom: 16,
    // iOS shadow
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    // Android shadow
    elevation: 5,
  },
  headerContainer: {
    flexDirection: 'row',
    gap: 10,
    width: '100%',
    padding: 12,
  },
  image: {
    height: 100,
    width: 100,
    borderRadius: 10,
  },
  detailRowContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  detailRow: {gap: 5, width: '48%', marginTop: 20},

  labelText: {
    color: customColors.textLightGrey,
    width: '35%',
    fontWeight: 400,
  },
  valueText: {
    color: customColors.appBlue,
    fontSize: 16,
    flex: 1,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    // alignItems: 'center',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 15,
    // alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    paddingBottom: 20,
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});
