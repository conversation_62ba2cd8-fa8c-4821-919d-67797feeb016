import {configureFonts} from 'react-native-paper';
import {
  MD3Type,
  MD3TypescaleKey,
} from 'react-native-paper/lib/typescript/types';

export const FONTS = {
  regular: 'Roboto-Regular',
  medium: 'Roboto-Medium',
  bold: 'Roboto-Bold',
  thin: 'Roboto-thin',
  SemiBold: 'Roboto-SemiBold',
};

const fontConfig: Partial<Record<MD3TypescaleKey, Partial<MD3Type>>> = {
  displaySmall: {
    fontFamily: 'Roboto-Regular',
    fontSize: 36,
    fontWeight: '400',
  },
  displayMedium: {
    fontFamily: 'Roboto-Regular',
    fontSize: 45,
    fontWeight: '400',
  },
  displayLarge: {
    fontFamily: 'Roboto-Regular',
    fontSize: 57,
    fontWeight: '400',
  },
  headlineSmall: {
    fontFamily: 'Roboto-Regular',
    fontSize: 24,
    fontWeight: '400',
  },
  headlineMedium: {
    fontFamily: 'Roboto-Regular',
    fontSize: 28,
    fontWeight: '400',
  },
  headlineLarge: {
    fontFamily: 'Roboto-Regular',
    fontSize: 32,
    fontWeight: '400',
  },
  titleSmall: {
    fontFamily: 'Roboto-Bold',
    fontSize: 14,
    fontWeight: '500',
  },
  titleMedium: {
    fontFamily: 'Roboto-Bold',
    fontSize: 16,
    fontWeight: '500',
  },
  titleLarge: {
    fontFamily: 'Roboto-Regular',
    fontSize: 22,
    fontWeight: '400',
  },
  labelSmall: {
    fontFamily: 'Roboto-Bold',
    fontSize: 11,
    fontWeight: '500',
  },
  labelMedium: {
    fontFamily: 'Roboto-Bold',
    fontSize: 12,
    fontWeight: '500',
  },
  labelLarge: {
    fontFamily: 'Roboto-Bold',
    fontSize: 14,
    fontWeight: '500',
  },
  bodySmall: {
    fontFamily: 'Roboto-Regular',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 18,
  },
  bodyMedium: {
    fontFamily: 'Roboto-Regular',
    fontSize: 14,
    fontWeight: '400',
  },
  bodyLarge: {
    fontFamily: 'Roboto-Regular',
    fontSize: 16,
    fontWeight: '400',
  },
};
export const fonts = {
  fonts: configureFonts({config: fontConfig}),
};
