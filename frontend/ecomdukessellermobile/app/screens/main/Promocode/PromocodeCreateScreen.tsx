import {SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import customColors from '../../../theme/customColors';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import CustomTextInput from '../../../components/InputFields/Textinput';
import {
  DiscountType,
  PromoApplicability,
  PromoCode,
  PromoCodePayload,
  PromoTag,
} from '../../../types/promo';
import {useFormik} from 'formik';
import {promoValidationSchema} from '../../../validations/promo';
import styleConstants from '../../../theme/styleConstants';
import CustomDropDown from '../../../components/InputFields/Dropdown';
import {useLazyGetproductsQuery} from '../../../redux/products/productApiSlice';
import MultiSelectDropDown from '../../../components/InputFields/MultiSelectDropDown';
import {useLazyGetCollectionsQuery} from '../../../redux/collections/collectionApiSlice';
import DatePicker from 'react-native-date-picker';
import {Checkbox, HelperText, Text} from 'react-native-paper';
import moment from 'moment';
import {FONTS} from '../../../theme/fonts';
import {colors} from '../../../theme/colors';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import {
  AddPromoCodeScreenNavigationProp,
  AddPromoCodeScreenRouteProp,
} from '../../../navigations/types';
import Toast from 'react-native-toast-message';
import {
  useCreatePromoCodeMutation,
  useUpdatePromoCodeMutation,
} from '../../../redux/promo-code/promoApiSlice';

type Props = {};
const typeOptions = [{label: 'flat'}, {label: 'percentage'}];

const promoTagOptions = [
  {label: 'public'},
  {label: 'invite-only'},
  {label: 'exclusive'},
];

const appliesToOptions = [
  {label: 'All', value: 'all'},
  {label: 'Products', value: 'products'},
  {label: 'Categories', value: 'categories'},
];

interface ScreenProps {
  navigation: AddPromoCodeScreenNavigationProp;
  route: AddPromoCodeScreenRouteProp;
}
export const PromoCodeCreateScreen: React.FC<ScreenProps> = ({
  navigation,
  route,
}) => {
  const editData = route?.params?.editData;
  const isEditMode = route?.params?.isEditMode;
  const promoCodeId = route?.params?.editData?.id;

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerType, setDatePickerType] = useState<null | string>(null);

  const [createPromoCode, {isLoading: isCreateLoading}] =
    useCreatePromoCodeMutation();
  const [updatePromoCode, {isLoading: isUpdateLoading}] =
    useUpdatePromoCodeMutation();

  const [getProducts, {data: products = [], isLoading: productsLoading}] =
    useLazyGetproductsQuery();
  const [
    getCollections,
    {data: collections = [], isLoading: collectionsLoading},
  ] = useLazyGetCollectionsQuery();

  const handleFormSubmit = async (values: Partial<PromoCodePayload>) => {
    // let allCategoryIds: string[] = [];
    // if (values.appliesTo === PromoApplicability.ALL_PRODUCTS) {
    //   allCategoryIds = Array.from(
    //     new Set(collections.map(collection => collection.id)),
    //   );
    // }
    const payload = {
      code: values.code,
      type: values.type,
      value: values.value !== undefined ? Number(values.value) : undefined,
      minCartValue:
        values.minCartValue !== undefined
          ? Number(values.minCartValue)
          : undefined,
      maxDiscountCap:
        values.maxDiscountCap !== undefined
          ? Number(values.maxDiscountCap)
          : undefined,
      usageLimitPerUser: values.usageLimitPerUser
        ? Number(values.usageLimitPerUser)
        : 0,
      usageLimitTotal: values.usageLimitTotal
        ? Number(values.usageLimitTotal)
        : 0,
      isActive: values.isActive ?? true,
      appliesTo: values.appliesTo,
      validFrom: values.validFrom,
      validTill: values.validTill,
      visibility: values.visibility,
      productIds: values.productIds ?? [],
      collectionIds: values?.categoryIds ?? [],
      isFirstTimeUserOnly: values.isFirstTimeUserOnly,
      isRepeatUserOnly: values.isRepeatUserOnly,
    };

    if (isEditMode && promoCodeId) {
      await updatePromoCode({id: promoCodeId, data: payload}).unwrap();

      Toast.show({
        type: 'success',
        text1: 'Promo Code updated successfully',
      });
    } else {
      await createPromoCode(payload).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Promo Code created successfully',
      });
    }
    navigation.pop();
  };
  const formik = useFormik<PromoCodePayload>({
    initialValues: {
      code: editData?.code || '',
      type: editData?.type || DiscountType.FLAT,
      value: editData?.value ? Number(editData.value) : 0,
      minCartValue: editData?.minCartValue ? Number(editData.minCartValue) : 0,
      maxDiscountCap: editData?.maxDiscountCap
        ? Number(editData.maxDiscountCap)
        : undefined,
      usageLimitPerUser: editData?.usageLimitPerUser ?? 0,
      usageLimitTotal: editData?.usageLimitTotal ?? 0,
      isFirstTimeUserOnly: editData?.isFirstTimeUserOnly ?? false,
      isRepeatUserOnly: editData?.isRepeatUserOnly ?? false,
      validFrom: editData?.validFrom || '',
      validTill: editData?.validTill || '',
      appliesTo: editData?.appliesTo || PromoApplicability.ALL_PRODUCTS,
      visibility: editData?.visibility || PromoTag.PUBLIC,
      productIds:
        editData?.promoCodeProducts?.map(p => p.productId) ??
        editData?.productIds ??
        [],
      categoryIds:
        editData?.promoCodeCollections?.map(c => c.collectionId) ??
        editData?.categoryIds ??
        [],
      isActive: editData?.isActive ?? true,
    },
    validationSchema: promoValidationSchema,
    onSubmit: values => {
      handleFormSubmit(values);
      console.log('values', values);
    },
  });
  useEffect(() => {
    getProducts({}).then(result => {});
    getCollections({}).then(() => {});
  }, []);

  const onConfirmDate = useCallback(
    (date: any) => {
      if (datePickerType) {
        formik.setFieldValue(datePickerType, moment(date).format());
        setShowDatePicker(false);
        setDatePickerType(null);
      }
    },
    [showDatePicker, datePickerType],
  );
  console.log('formi', formik?.errors);

  return (
    <SafeAreaView style={styles.screenContainer}>
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollViewContainer}>
        <CustomTextInput
          title={'Promo Code'}
          placeholder="Enter Promo Code"
          onChangeText={formik.handleChange('code')}
          value={formik.values.code}
          onBlur={formik.handleBlur('code')}
          touched={formik.touched.code}
          errors={formik.errors.code}
        />
        <CustomDropDown
          title={'Discount Type'}
          value={formik.values.type}
          touched={formik.touched.type}
          errors={formik.errors.type}
          data={Array.isArray(typeOptions) ? typeOptions : []}
          labelField="label"
          placeholder="Choose Discount Type"
          valueField="label"
          onChange={(item: any) => {
            formik.setFieldValue('type', item?.label);
          }}
          onRemove={() => {
            formik.setFieldValue('type', '');
          }}
          onDropdownFocus={() => {
            formik.setFieldTouched('type');
          }}
          onDropdownBlur={() => {
            formik.handleBlur('type');
          }}
        />
        <CustomTextInput
          title={'Discount Value'}
          placeholder="Enter Discount Value"
          onChangeText={formik.handleChange('value')}
          value={formik.values.value?.toString()}
          onBlur={formik.handleBlur('value')}
          touched={formik.touched.value}
          errors={formik.errors.value}
        />
        <CustomTextInput
          title={'Maximum Discount'}
          placeholder="Enter Maximum Discount"
          onChangeText={formik.handleChange('maxDiscountCap')}
          value={formik.values.maxDiscountCap?.toString()}
          onBlur={formik.handleBlur('maxDiscountCap')}
          touched={formik.touched.maxDiscountCap}
          errors={formik.errors.maxDiscountCap}
        />
        <CustomTextInput
          title={'Minimum Cart Value'}
          placeholder="Enter Minimum Cart Value"
          onChangeText={formik.handleChange('minCartValue')}
          value={formik.values.minCartValue?.toString()}
          onBlur={formik.handleBlur('minCartValue')}
          touched={formik.touched.minCartValue}
          errors={formik.errors.minCartValue}
        />
        <CustomTextInput
          title={'Usage Limit Per User'}
          placeholder="Enter Usage Limit Per User"
          onChangeText={formik.handleChange('usageLimitPerUser')}
          value={formik.values.usageLimitPerUser?.toString()}
          onBlur={formik.handleBlur('usageLimitPerUser')}
          touched={formik.touched.usageLimitPerUser}
          errors={formik.errors.usageLimitPerUser}
        />
        <CustomTextInput
          title={'Total Usage Limit'}
          placeholder="Enter Total Usage Limit"
          onChangeText={formik.handleChange('usageLimitTotal')}
          value={formik.values.usageLimitTotal?.toString()}
          onBlur={formik.handleBlur('usageLimitTotal')}
          touched={formik.touched.usageLimitTotal}
          errors={formik.errors.usageLimitTotal}
        />
        <CustomDropDown
          title={'Promo Tag'}
          value={formik.values.visibility}
          touched={formik.touched.visibility}
          errors={formik.errors.visibility}
          data={Array.isArray(promoTagOptions) ? promoTagOptions : []}
          labelField="label"
          placeholder="Choose Promo Tag"
          valueField="label"
          onChange={(item: any) => {
            formik.setFieldValue('visibility', item?.label);
          }}
          onRemove={() => {
            formik.setFieldValue('visibility', '');
          }}
          onDropdownFocus={() => {
            formik.setFieldTouched('visibility');
          }}
          onDropdownBlur={() => {
            formik.handleBlur('visibility');
          }}
        />
        <CustomDropDown
          search={false}
          title={'Applies To'}
          value={formik.values.appliesTo}
          touched={formik.touched.appliesTo}
          errors={formik.errors.appliesTo}
          data={Array.isArray(appliesToOptions) ? appliesToOptions : []}
          labelField="label"
          placeholder="Choose Applies To"
          valueField="value"
          onChange={(item: any) => {
            if (item?.value !== formik.values.appliesTo) {
              formik.setFieldValue('productIds', []);
              formik.setFieldValue('categoryIds', []);
            }
            formik.setFieldValue('appliesTo', item?.value);
          }}
          onRemove={() => {
            formik.setFieldValue('appliesTo', '');
          }}
          onDropdownFocus={() => {
            formik.setFieldTouched('appliesTo');
          }}
          onDropdownBlur={() => {
            formik.handleBlur('appliesTo');
          }}
        />
        {formik.values.appliesTo === PromoApplicability.SELECTED_PRODUCTS && (
          <MultiSelectDropDown
            mode="modal"
            title={'Applicable Products'}
            value={formik.values.productIds}
            touched={formik.touched.productIds}
            errors={formik.errors.productIds}
            data={Array.isArray(products) ? products : []}
            labelField="name"
            placeholder="Choose Products"
            valueField="id"
            onChange={(item: any) => {
              formik.setFieldValue('productIds', item);
            }}
            onDropdownFocus={() => {
              formik.setFieldTouched('productIds');
            }}
            onDropdownBlur={() => {
              formik.handleBlur('productIds');
            }}
          />
        )}
        {formik.values.appliesTo === PromoApplicability.SELECTED_CATEGORIES && (
          <MultiSelectDropDown
            mode="modal"
            title={'Applicable Categories'}
            value={formik.values.categoryIds}
            touched={formik.touched.categoryIds}
            errors={formik.errors.categoryIds}
            data={Array.isArray(collections) ? collections : []}
            labelField="name"
            placeholder="Choose Categories"
            valueField="id"
            onChange={(item: any) => {
              formik.setFieldValue('categoryIds', item);
            }}
            onDropdownFocus={() => {
              formik.setFieldTouched('categoryIds');
            }}
            onDropdownBlur={() => {
              formik.handleBlur('categoryIds');
            }}
          />
        )}
        <TouchableOpacity
          onPress={() => {
            setShowDatePicker(true);
            setDatePickerType('validFrom');
          }}
          style={styles.datePickerContainer}>
          <Text variant="bodyMedium" style={styles.title}>
            {'Start Date'}
          </Text>
          <View style={styles.dateTimeContainer}>
            <Text variant="bodyMedium" style={styles.dateText}>
              {formik?.values?.validFrom
                ? moment(formik?.values?.validFrom).format('DD/MM/YYYY')
                : 'DD/MM/YYYY'}
            </Text>
          </View>
        </TouchableOpacity>
        <HelperText
          numberOfLines={1}
          style={styles.helperText}
          type="error"
          padding="none"
          visible={
            formik?.touched?.validFrom && formik?.errors?.validFrom
              ? true
              : false
          }>
          {formik?.errors?.validFrom}
        </HelperText>
        <TouchableOpacity
          onPress={() => {
            setShowDatePicker(true);
            setDatePickerType('validTill');
          }}
          style={styles.datePickerContainer}>
          <Text variant="bodyMedium" style={styles.title}>
            {'End Date'}
          </Text>
          <View style={styles.dateTimeContainer}>
            <Text variant="bodyMedium" style={styles.dateText}>
              {formik?.values?.validTill
                ? moment(formik?.values?.validTill).format('DD/MM/YYYY')
                : 'DD/MM/YYYY'}
            </Text>
          </View>
        </TouchableOpacity>
        <HelperText
          numberOfLines={1}
          style={styles.helperText}
          type="error"
          padding="none"
          visible={
            formik?.touched?.validTill && formik?.errors?.validTill
              ? true
              : false
          }>
          {formik?.errors?.validTill}
        </HelperText>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Checkbox.Android
            status={
              formik?.values?.isFirstTimeUserOnly ? 'checked' : 'unchecked'
            }
            onPress={() => {
              formik.setFieldValue(
                'isFirstTimeUserOnly',
                !formik?.values?.isFirstTimeUserOnly,
              );
            }}
          />
          <Text variant="bodyMedium">Applicable to first-time users only</Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Checkbox.Android
            status={formik?.values?.isRepeatUserOnly ? 'checked' : 'unchecked'}
            onPress={() => {
              formik.setFieldValue(
                'isRepeatUserOnly',
                !formik?.values?.isRepeatUserOnly,
              );
            }}
          />
          <Text variant="bodyMedium">Applicable to repeat users only</Text>
        </View>
        <CustomButton
          title={isEditMode ? 'Update' : 'Create'}
          disabled={isCreateLoading || isUpdateLoading}
          loading={isCreateLoading || isUpdateLoading}
          onPress={() => formik.handleSubmit()}
        />
      </KeyboardAwareScrollView>
      <DatePicker
        modal
        mode="date"
        title={
          datePickerType == 'validTill'
            ? 'Select Start Date'
            : 'Select End Date'
        }
        open={showDatePicker}
        date={new Date()}
        onConfirm={date => {
          onConfirmDate(date);
        }}
        onCancel={() => {
          setShowDatePicker(false);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  scrollViewContainer: {
    backgroundColor: customColors.white,
    padding: styleConstants.spacing.x20,
    paddingBottom: 100,
  },
  datePickerCloseButton: {
    alignSelf: 'flex-end',
    marginBottom: 15,
  },
  datePickerContainer: {
    marginBottom: 10,
  },
  dateText: {
    fontFamily: FONTS.regular,
    fontSize: 16,
  },
  dateTimeContainer: {
    borderColor: colors.gray.dark,
    borderRadius: 35,
    borderWidth: 1,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  title: {marginBottom: 5, marginLeft: 20},
  helperText: {
    marginLeft: 20,
  },
});
