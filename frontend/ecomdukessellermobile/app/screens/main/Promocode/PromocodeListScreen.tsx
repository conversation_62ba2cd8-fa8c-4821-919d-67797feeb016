import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useState} from 'react';
import {PromocodeListScreenNavigationProp} from '../../../navigations/types';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import styleConstants from '../../../theme/styleConstants';
import {SCREEN_NAME} from '../../../constants';
import {ActivityIndicator, Icon, MD3Colors, Text} from 'react-native-paper';
import {
  useGetPromoCodeCountQuery,
  useLazyGetPromoCodesQuery,
  useRemovePromoCodeMutation,
} from '../../../redux/promo-code/promoApiSlice';
import {PromoCode} from '../../../types/promo';
import {useFocusEffect} from '@react-navigation/native';
import moment from 'moment';

interface ScreenProps {
  navigation: PromocodeListScreenNavigationProp;
}
const ds = {
  limit: 10,
  offset: 0,
  order: ['createdOn+DESC'],
  include: [
    {relation: 'promoCodeProducts'},
    {relation: 'promoCodeCollections'},
  ],
};
export const PromoCodeListScreen: React.FC<ScreenProps> = ({navigation}) => {
  const [skip, setSkip] = useState(0);
  const [promoCodeList, setPromoCodeLists] = useState<PromoCode[]>([]);
  const [hasMore, setHasMore] = useState(true);

  const [removePromoCode] = useRemovePromoCodeMutation();

  const [getPromoCodeLists, {isLoading, isFetching}] =
    useLazyGetPromoCodesQuery();

  const {data: promoCount, refetch: refetchPromoCodeCount} =
    useGetPromoCodeCountQuery({
      include: [],
    });

  const handleLoadMore = useCallback(() => {
    if (!isFetching && hasMore) {
      const newSkip = skip + 10;
      getPromoCodeLists({
        limit: 10,
        offset: newSkip,
        order: ['createdOn DESC'],
        include: [
          {relation: 'promoCodeProducts'},
          {relation: 'promoCodeCollections'},
        ],
      })
        .unwrap()
        .then(newData => {
          setPromoCodeLists(prev => [...prev, ...newData]);
          setSkip(newSkip);
          // Check if we received fewer items than the limit (i.e., last page)
          if (newData.length < 10) {
            setHasMore(false);
          }
        });
    }
  }, [skip, isFetching, hasMore]);

  const loadInitialPromoCodes = useCallback(async () => {
    setSkip(0);
    setHasMore(true);
    const initialData = await getPromoCodeLists({
      limit: 10,
      offset: 0,
      order: ['createdOn DESC'],
      include: [
        {relation: 'promoCodeProducts'},
        {relation: 'promoCodeCollections'},
      ],
    }).unwrap();
    setPromoCodeLists(initialData);
    if (promoCount?.count) {
      if (initialData.length <= promoCount?.count) {
        setHasMore(false);
      }
    }

    refetchPromoCodeCount(); // Optional, to refresh warehouse count
  }, [getPromoCodeLists, refetchPromoCodeCount]);

  useFocusEffect(
    useCallback(() => {
      loadInitialPromoCodes();
    }, [loadInitialPromoCodes]),
  );
  const deletePromoCode = async (id: string) => {
    await removePromoCode(id).unwrap();
    loadInitialPromoCodes();
  };
  const onPressDelete = (item: PromoCode) => {
    Alert.alert(
      'Are you sure you want to delete?',
      `By deleting ${item?.code} this promo code will be permanently removed.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            deletePromoCode(item?.id);
          },
        },
      ],
    );
  };

  const Details = ({
    label1,
    label2,
    value1,
    value2,
  }: {
    label1: string;
    label2: string;
    value1: string | undefined;
    value2: string | undefined;
  }) => {
    return (
      <View style={styles.detailRowContainer}>
        <View style={styles.detailRow}>
          <Text variant="labelMedium" style={styles.labelText}>
            {label1}
          </Text>
          <Text variant="labelLarge" style={styles.valueText}>
            {value1 ? value1 : '-'}
          </Text>
        </View>
        <View style={styles.detailRow}>
          <Text variant="labelMedium" style={styles.labelText}>
            {label2}
          </Text>
          <Text variant="labelLarge" style={styles.valueText}>
            {value2 ? value2 : '-'}
          </Text>
        </View>
      </View>
    );
  };
  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.screenTitleContainer}>
        <Text
          style={styles.screenTitle}
          variant="labelLarge">{`Promo Codes`}</Text>
        <CustomButton
          icon={'plus'}
          buttonStyle={styles.customeButtonStyle}
          title="Create Promo Code"
          onPress={() => {
            navigation.push(SCREEN_NAME.PROMOCODE_CREATE, {
              isEditMode: false,
            });
          }}
        />
      </View>
      <FlatList
        contentContainerStyle={styles.flatListContainer}
        renderItem={({item, index}) => {
          return (
            <View style={styles.card}>
              <View style={styles.headerContainer}>
                <View style={styles.actionButtonRow}>
                  <Text variant="titleMedium" style={styles.cardTitleText}>
                    {item?.code}
                  </Text>
                  <View
                    style={
                      item?.isActive
                        ? styles.activeStatusContainer
                        : styles.inActiveStatusContainer
                    }>
                    <Text style={styles.statusText} variant="titleSmall">
                      {item?.isActive ? 'Active' : 'Inactive'}
                    </Text>
                  </View>
                </View>
                <View style={styles.actionButtonRow}>
                  <TouchableOpacity
                    onPress={() => {
                      navigation.push(SCREEN_NAME.PROMOCODE_CREATE, {
                        editData: item,
                        isEditMode: true,
                      });
                    }}
                    style={styles.editIcon}>
                    <Icon
                      source={'archive-edit'}
                      color={customColors.appBlue}
                      size={20}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      onPressDelete(item);
                    }}>
                    <Icon
                      source={'delete'}
                      color={MD3Colors.error50}
                      size={20}
                    />
                  </TouchableOpacity>
                </View>
              </View>
              <View
                style={{
                  gap: 10,
                  paddingHorizontal: 10,
                }}>
                <Details
                  label1="Type"
                  label2="Value"
                  value1={item?.type}
                  value2={item?.value?.toString()}
                />
                <Details
                  label1="Usage Limit Per User"
                  label2="Usage Limit Total"
                  value1={item?.usageLimitPerUser?.toString()}
                  value2={item?.usageLimitPerUser?.toString()}
                />
                <Details
                  label1="Minimum Cart Value"
                  label2="Max Discount Cap"
                  value1={item?.minCartValue?.toString()}
                  value2={item?.maxDiscountCap?.toString()}
                />
                <Details
                  label1="Valid From"
                  label2="Valid Till"
                  value1={moment(item?.validFrom).format('DD/MM/YYYY hh:mm A')}
                  value2={moment(item?.validTill).format('DD/MM/YYYY hh:mm A')}
                />
              </View>
            </View>
          );
        }}
        data={promoCodeList ?? []}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() => {
          if (
            hasMore &&
            isFetching &&
            promoCount?.count &&
            promoCount?.count > 0
          ) {
            return <ActivityIndicator size={20} color={colors.primary} />;
          }
        }}
        ListEmptyComponent={() => {
          return (
            <View style={styles.emptyContainer}>
              {isLoading && !promoCodeList ? (
                <ActivityIndicator size={20} color={customColors.appBlue} />
              ) : (
                <Text variant="bodyMedium" style={styles.emptyText}>
                  No warehouses found.
                </Text>
              )}
            </View>
          );
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  screenTitle: {
    fontSize: 16,
    color: customColors.appBlue,
  },
  customeButtonStyle: {
    marginTop: 0,
  },
  screenTitleContainer: {
    marginVertical: styleConstants.spacing.x20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: styleConstants.spacing.x20,
  },
  flatListContainer: {
    padding: styleConstants.spacing.x20,
    paddingTop: 0,
  },
  emptyContainer: {
    padding: styleConstants.spacing.x20,
    alignItems: 'center',
    paddingTop: '50%',
  },
  emptyText: {
    color: customColors.appBlue,
    fontSize: 16,
  },

  //

  card: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: 12,
    marginBottom: 16,
    // iOS shadow
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    // Android shadow
    elevation: 5,
  },
  cardTitleText: {
    fontSize: 18,
    color: customColors.appBlue,
  },
  actionButtonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editIcon: {
    marginRight: 10,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray.light,
    paddingBottom: 10,
    paddingHorizontal: 10,
  },
  detailRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailRow: {gap: 5, width: '48%', marginTop: 20},

  labelText: {
    color: customColors.textLightGrey,
  },
  valueText: {
    color: colors.gray.text,
    fontSize: 16,
  },
  activeStatusContainer: {
    backgroundColor: customColors.successGreen,
    paddingVertical: 2.5,
    paddingHorizontal: 15,
    borderRadius: 15,
    marginLeft: 10,
  },
  inActiveStatusContainer: {
    backgroundColor: MD3Colors.error50,
    paddingVertical: 2.5,
    paddingHorizontal: 15,
    borderRadius: 15,
    marginLeft: 10,
  },
  statusText: {
    color: customColors.white,
  },
});
