import {SafeAreaView, StyleSheet, View} from 'react-native';
import React, {useEffect} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {colors} from '../../../theme/colors';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {Text} from 'react-native-paper';
import {BankDetailsType} from '../../../types/auth';
import {useFormik} from 'formik';
import {bankDetailsValidationSchema} from '../../../validations/bank';
import {useTypedSelector} from '../../../redux/appstore';
import {encryptData} from '../../../utils/helpers';
import {
  useAddBankDetailsMutation,
  useGetVendorQuery,
} from '../../../redux/auth/authApiSlice';
import CustomTextInput from '../../../components/InputFields/Textinput';
import CustomDropDown from '../../../components/InputFields/Dropdown';
import {accountTypes, businessTypes} from '../../../constants/dropdownvalues';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import Toast from 'react-native-toast-message';

type Props = {};

const BankDetailsScreen = (props: Props) => {
  const _renderSeperator = () => {
    return (
      <View
        style={{
          borderBottomWidth: 1,
          borderBottomColor: customColors.borderGrey,
          marginBottom: styleConstants.spacing.l,
        }}
      />
    );
  };
  const [addBankDetails, {isLoading}] = useAddBankDetailsMutation();
  const {userDetails} = useTypedSelector(state => state.auth);
  const {
    data: vendor,
    isLoading: isVendorLoading,
    isFetching,
  } = useGetVendorQuery();
  const isBankValidationFailed = vendor?.status === 'BANK_VALIDATION_FAILED';

  // Check for KYC document issues
  const kycIssues =
    vendor?.relatedDocs?.filter(
      doc => doc.status === 'ACTION_REQUIRED' && doc.remarks,
    ) || [];

  const formik = useFormik<BankDetailsType>({
    initialValues: {
      accountNumber: '',
      accountHolder: '',
      ifsc: '',
      uidai: '',
      gst: '',
      pan: '',
      businessType: '',
    },
    validationSchema: bankDetailsValidationSchema,
    onSubmit: async values => {
      await addBankDetails({
        sellerId: userDetails?.profileId ?? '',
        body: {
          accountNumber: encryptData(values.accountNumber),
          accountHolder: encryptData(values.accountHolder),
          ifsc: encryptData(values.ifsc),
          uidai: values.uidai ? encryptData(values.uidai) : undefined,
          gst: values.gst ? encryptData(values.gst) : undefined,
          pan: values.pan ? encryptData(values.pan) : undefined,
          businessType: values.businessType,
          accountType: values.accountType,
        },
      }).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Banking Details Updated Successfully',
      });
    },
  });
  useEffect(() => {
    if (isVendorLoading || isFetching) return;
    if (vendor) {
      formik.setValues({
        accountNumber: vendor?.bank?.accountNumber ?? '',
        accountHolder: vendor?.bank?.accountHolder ?? '',
        ifsc: vendor?.bank?.ifsc ?? '',
        uidai:
          vendor?.relatedDocs?.find(doc => doc.docType === 'Aadhaar')
            ?.docValue ?? '',
        gst:
          vendor?.relatedDocs?.find(doc => doc.docType === 'GST')?.docValue ??
          '',
        pan:
          vendor?.relatedDocs?.find(doc => doc.docType === 'PAN')?.docValue ??
          '',
        businessType: vendor?.businessType ?? '',
        accountType: vendor?.accountType ?? '',
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVendorLoading, isFetching, vendor]);
  return (
    <SafeAreaView style={styles.mainContainer}>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={styles.scrolllViewStyle}
        contentContainerStyle={styles.scrollviewContainer}>
        {isBankValidationFailed && (
          <View
            style={{
              padding: 10,
              backgroundColor: 'rgb(252, 239, 239)',
              borderRadius: 10,
            }}>
            <Text variant="titleMedium">Bank Validation Failed</Text>
            <Text variant="bodyMedium">
              Your bank account details could not be validated. Please verify
              the information and try again.
            </Text>
            {vendor?.accountType === undefined && (
              <Text variant="bodyMedium">
                Account type could not be populated. Please select it manually.
              </Text>
            )}
          </View>
        )}
        <Text
          variant="titleMedium"
          style={{
            padding: styleConstants.spacing.x20,
            fontSize: 18,
          }}>
          {'Banking Information'}
        </Text>
        {_renderSeperator()}

        <CustomTextInput
          title="Account Holder Name*"
          value={formik.values.accountHolder}
          onChangeText={formik.handleChange('accountHolder')}
          onBlur={formik.handleBlur('firstName')}
          touched={formik.touched.accountHolder}
          errors={formik.errors.accountHolder}
        />
        <CustomTextInput
          title="Account Number*"
          value={formik.values.accountNumber}
          onChangeText={formik.handleChange('accountNumber')}
          onBlur={formik.handleBlur('accountNumber')}
          touched={formik.touched.accountNumber}
          errors={formik.errors.accountNumber}
        />
        <CustomTextInput
          title="IFSC Code*"
          value={formik.values.ifsc}
          onChangeText={formik.handleChange('ifsc')}
          onBlur={formik.handleBlur('ifsc')}
          touched={formik.touched.ifsc}
          errors={formik.errors.ifsc}
          isHelperTextVisible={!!formik.errors.ifsc}
        />
        <Text
          style={{
            marginBottom: styleConstants.spacing.xl,
          }}
          variant="bodySmall">
          IFSC code is an 11-character code that identifies the bank branch
          (e.g., SBIN0001234)
        </Text>
        <CustomDropDown
          title="Account Type*"
          value={formik.values.accountType}
          touched={formik.touched.accountType}
          errors={formik.errors.accountType}
          placeholder="Select Account Type"
          data={accountTypes}
          labelField="label"
          valueField="value"
          onChange={(item: any) =>
            formik.setFieldValue('accountType', item.value)
          }
          onDropdownFocus={() => formik.setFieldTouched('accountType', true)}
          onDropdownBlur={() => formik.handleBlur('accountType')}
          onRemove={() => {
            formik.setFieldValue('accountType', '');
          }}
        />
        <CustomDropDown
          title="Business Type*"
          value={formik.values.businessType}
          touched={formik.touched.businessType}
          errors={formik.errors.businessType}
          placeholder="Select Business Type"
          data={businessTypes}
          labelField="label"
          valueField="value"
          onChange={(item: any) =>
            formik.setFieldValue('businessType', item.value)
          }
          onDropdownFocus={() => formik.setFieldTouched('businessType', true)}
          onDropdownBlur={() => formik.handleBlur('businessType')}
          onRemove={() => {
            formik.setFieldValue('businessType', '');
          }}
        />

        <Text
          variant="titleMedium"
          style={{
            padding: styleConstants.spacing.x20,
            fontSize: 18,
          }}>
          {'KYC Information'}
        </Text>
        {_renderSeperator()}
        <View
          style={{
            backgroundColor: 'rgb(241, 251, 252)',
            padding: 10,
            marginBottom: styleConstants.spacing.l,
            borderRadius: 5,
          }}>
          <Text variant="bodySmall">
            At least one KYC document (Aadhaar, PAN, or GST) is required for
            verification.
          </Text>
        </View>
        {kycIssues?.length > 0 && (
          <View
            style={{
              backgroundColor: 'rgb(253, 246, 237)',
              padding: 10,
              borderRadius: 5,
            }}>
            <Text variant="bodyMedium">KYC Document Issues</Text>
            {kycIssues.map((doc, index) => (
              <Text
                key={index}
                variant="bodySmall"
                style={{
                  marginVertical: 5,
                }}>
                {doc.docName}: {doc.remarks}
              </Text>
            ))}
          </View>
        )}
        <CustomTextInput
          title="Aadhaar Number"
          value={formik.values.uidai}
          onChangeText={formik.handleChange('uidai')}
          onBlur={formik.handleBlur('uidai')}
          touched={formik.touched.uidai}
          errors={formik.errors.uidai}
          isHelperTextVisible={!!formik.errors.uidai}
        />
        <Text
          style={{
            marginBottom: styleConstants.spacing.xl,
          }}
          variant="bodySmall">
          12-digit Aadhaar number without spaces
        </Text>
        <CustomTextInput
          title="PAN"
          value={formik.values.pan}
          onChangeText={formik.handleChange('pan')}
          onBlur={formik.handleBlur('pan')}
          touched={formik.touched.pan}
          errors={formik.errors.pan}
          isHelperTextVisible={!!formik.errors.pan}
        />
        <Text
          style={{
            marginBottom: styleConstants.spacing.xl,
          }}
          variant="bodySmall">
          10-character PAN (e.g., **********)
        </Text>
        <CustomTextInput
          title="GST Number"
          value={formik.values.gst}
          onChangeText={formik.handleChange('gst')}
          onBlur={formik.handleBlur('gst')}
          touched={formik.touched.gst}
          errors={formik.errors.gst}
          isHelperTextVisible={!!formik.errors.gst}
        />
        <Text
          style={{
            marginBottom: styleConstants.spacing.xl,
          }}
          variant="bodySmall">
          15-character GST number (e.g., 22AAAAA0000A1Z5)
        </Text>
        <CustomButton
          title="Save Details"
          disabled={isLoading}
          loading={isLoading}
          onPress={() => formik.handleSubmit()}
        />
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default BankDetailsScreen;

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.surface,
    flex: 1,
    justifyContent: 'center',
  },
  scrolllViewStyle: {
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  scrollviewContainer: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    flexGrow: 1,
  },
});
