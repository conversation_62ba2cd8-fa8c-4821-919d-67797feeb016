import {SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useCallback} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {colors} from '../../../theme/colors';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {Icon, MD3Colors, Text} from 'react-native-paper';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import {SCREEN_NAME} from '../../../constants';
import {excludeMetaFields} from '../../../constants/filter';
import {
  useDeleteShippingProfileMutation,
  useGetShippingProfilesQuery,
} from '../../../redux/shipping/shippingApiSlice';
import {useTypedSelector} from '../../../redux/appstore';
import {useFocusEffect} from '@react-navigation/native';
import Toast from 'react-native-toast-message';

type Props = {
  navigation: any;
};

const ShippingDetailsScreen = ({navigation}: Props) => {
  const {userDetails} = useTypedSelector(state => state.auth);

  const {
    data: shippingProfiles,
    isLoading: isProfilesLoading,
    refetch: refetchProfiles,
  } = useGetShippingProfilesQuery(
    {
      sellerId: userDetails?.profileId ?? '',
      filter: {fields: excludeMetaFields},
    },
    {
      skip: !userDetails?.profileId,
    },
  );
  const [deleteShippingProfile, {isLoading: isDeleting}] =
    useDeleteShippingProfileMutation();

  useFocusEffect(
    useCallback(() => {
      refetchProfiles();
    }, []),
  );
  const _renderSeperator = () => {
    return (
      <View
        style={{
          borderBottomWidth: 1,
          borderBottomColor: customColors.borderGrey,
          marginVertical: styleConstants.spacing.s10,
        }}
      />
    );
  };
  console.log('shippingProfiles', shippingProfiles);
  const Details = ({
    label1,
    label2,
    value1,
    value2,
  }: {
    label1: string;
    label2: string;
    value1: string | undefined;
    value2: string | undefined;
  }) => {
    return (
      <View style={styles.detailRowContainer}>
        <View style={styles.detailRow}>
          <Text variant="labelMedium" style={styles.labelText}>
            {label1}
          </Text>
          <Text variant="labelLarge" style={styles.valueText}>
            {value1 ? value1 : '-'}
          </Text>
        </View>
        {label2 && (
          <View style={styles.detailRow}>
            <Text variant="labelMedium" style={styles.labelText}>
              {label2}
            </Text>
            <Text variant="labelLarge" style={styles.valueText}>
              {value2 ? value2 : '-'}
            </Text>
          </View>
        )}
      </View>
    );
  };
  const item = shippingProfiles?.length > 0 ? shippingProfiles[0] : {};
  const handleDeleteProfile = async (id: string) => {
    await deleteShippingProfile(id).unwrap();
    Toast.show({
      type: 'success',
      text1: 'Shipping profile deleted successfully',
    });
    await refetchProfiles();
  };
  return (
    <SafeAreaView style={styles.mainContainer}>
      <View style={styles.screenTitleContainer}>
        <Text
          style={styles.screenTitle}
          variant="labelLarge">{`Shipping Profile`}</Text>
        {!item?.id ? (
          <CustomButton
            icon={'plus'}
            buttonStyle={styles.customeButtonStyle}
            title="Add"
            onPress={() => {
              navigation.push(SCREEN_NAME.SHIPPING_CREATE);
            }}
          />
        ) : (
          <View style={styles.actionButtonRow}>
            <TouchableOpacity
              onPress={() => {
                navigation.push(SCREEN_NAME.SHIPPING_CREATE, {
                  editData: item,
                  isEditMode: true,
                });
              }}
              style={styles.editIcon}>
              <Icon
                source={'archive-edit'}
                color={customColors.appBlue}
                size={25}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                handleDeleteProfile(item?.id);
              }}>
              <Icon source={'delete'} color={MD3Colors.error50} size={25} />
            </TouchableOpacity>
          </View>
        )}
      </View>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={styles.scrolllViewStyle}
        contentContainerStyle={styles.scrollviewContainer}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
              }}>
              <Text variant="titleMedium">{item?.name ?? ''}</Text>
              {item?.isDefault && (
                <View
                  style={{
                    backgroundColor: customColors.backgroundGreen,
                    padding: 1,
                    borderRadius: 20,
                    paddingHorizontal: 20,
                  }}>
                  <Text
                    variant="labelMedium"
                    style={{
                      color: customColors.successGreen,
                    }}>
                    {'Default'}
                  </Text>
                </View>
              )}
            </View>
            <Text
              variant="titleSmall"
              style={{
                marginTop: styleConstants.spacing.s10,
              }}>
              {'Method  : ' + item?.shippingMethod?.name ?? ''}
            </Text>
          </View>

          <View
            style={
              item?.isActive
                ? {
                    backgroundColor: customColors.backgroundGreen,
                    padding: 10,
                    borderRadius: 10,
                    paddingHorizontal: 20,
                  }
                : {
                    backgroundColor: customColors.backgroundRed,
                    padding: 10,
                    borderRadius: 10,
                    paddingHorizontal: 20,
                  }
            }>
            <Text
              variant="labelMedium"
              style={{
                color: item?.isActive
                  ? customColors.successGreen
                  : customColors.red,
              }}>
              {item?.isActive ? 'Active' : 'InActive'}
            </Text>
          </View>
        </View>

        {_renderSeperator()}

        <Text variant="titleSmall">Description : </Text>
        <Text variant="bodySmall">{item?.description}</Text>

        <View
          style={{
            marginTop: 20,
          }}>
          <Text variant="titleMedium">{'Shipping Charges'}</Text>
          {_renderSeperator()}

          {item?.shippingCharges?.map((item, index) => {
            return (
              <View style={styles.shadow}>
                <Text
                  style={{
                    color: customColors.textLightGrey,
                  }}
                  variant="titleSmall">{`Shipping Charge #${index + 1}`}</Text>
                {_renderSeperator()}
                <Details
                  label1="State"
                  label2="Shipping Price"
                  value1={item?.stateCode}
                  value2={item?.baseCharge?.toString()}
                />
                <Details
                  label1="Min Delivery Days"
                  label2="Max Delivery Days"
                  value1={item?.estimatedDaysMin?.toString()}
                  value2={item?.estimatedDaysMax?.toString()}
                />
              </View>
            );
          })}
        </View>
        <View
          style={{
            marginTop: 20,
          }}>
          <Text variant="titleMedium">{'Weight-Based Shipping Rules'}</Text>
          {_renderSeperator()}

          {item?.weightBasedRules?.map((item, index) => {
            return (
              <View style={styles.shadow}>
                <Text
                  style={{
                    color: customColors.textLightGrey,
                  }}
                  variant="titleSmall">{`Weight Rule #${index + 1}`}</Text>
                {_renderSeperator()}
                <Details
                  label1="Min Weight (kg)"
                  label2="Max Weight (kg)"
                  value1={item?.minWeight}
                  value2={item?.maxWeight?.toString()}
                />
                <Details
                  label1="Shipping Price"
                  value1={item?.charge?.toString()}
                />
              </View>
            );
          })}
        </View>
        <View
          style={{
            marginTop: 20,
          }}>
          <Text variant="titleMedium">
            {'Product-Specific Shipping Charges'}
          </Text>
          {_renderSeperator()}

          {item?.productShippingCharges?.map((item, index) => {
            return (
              <View style={styles.shadow}>
                <Text
                  style={{
                    color: customColors.textLightGrey,
                  }}
                  variant="titleSmall">{`Product Charge #${index + 1}`}</Text>
                {_renderSeperator()}
                <Details
                  label1="Product"
                  label2="Shipping Price"
                  value1={item?.productId}
                  value2={item?.baseCharge?.toString()}
                />
              </View>
            );
          })}
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ShippingDetailsScreen;

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.surface,
    flex: 1,
    justifyContent: 'center',
  },
  screenTitleContainer: {
    marginVertical: styleConstants.spacing.x20,
    marginBottom: styleConstants.spacing.s10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: styleConstants.spacing.x20,
  },
  screenTitle: {
    fontSize: 16,
    color: customColors.appBlue,
  },
  actionButtonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editIcon: {
    marginRight: 10,
  },
  customeButtonStyle: {
    marginTop: 0,
  },
  scrolllViewStyle: {
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  scrollviewContainer: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    flexGrow: 1,
  },
  detailRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailRow: {gap: 5, width: '48%', marginTop: 20},

  labelText: {
    color: customColors.textLightGrey,
  },
  valueText: {
    color: colors.gray.text,
    fontSize: 16,
  },
  shadow: {
    backgroundColor: colors.background,
    borderRadius: 10,
    elevation: 2,
    marginVertical: 5,
    padding: 10,
    shadowColor: customColors.textBlack,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
});
