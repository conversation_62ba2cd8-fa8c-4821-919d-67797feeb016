import {StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Card, Icon, RadioButton, Text} from 'react-native-paper';
import styleConstants from '../../../../theme/styleConstants';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

type Props = {
  title: string;
  onValueChange: Function;
  selectedValue: string;
  value: string;
  subtitle: string;
  features: {name: string; isAvailable: boolean}[];
  active: boolean;
};

const PlansCard = ({
  title = '',
  onValueChange,
  selectedValue,
  value,
  subtitle = '',
  features = [],
  active = true,
}: Props) => {
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const visibleFeatures = showAllFeatures ? features : features.slice(0, 3);
  return (
    <Card
      contentStyle={styles.cardContentStyle}
      style={[
        styles.cardContainer,
        selectedValue === value && styles.selectedCard,
      ]}>
      <View style={styles.headerRow}>
        <View>
          <Text variant="labelLarge" style={styles.title}>
            {title}
          </Text>
          {/* <Text
            variant="labelLarge"
            style={{
              color: colors.primary,
              marginTop: styleConstants.spacing.s,
            }}>
            {subtitle}
          </Text> */}
        </View>
        <View style={styles.statusRow}>
          {active && (
            <Text
              style={[
                styles.statusText,
                {
                  backgroundColor: colors.green.success,
                },
              ]}>
              Active
            </Text>
          )}
          <RadioButton.Android
            hitSlop={{
              left: 15,
              right: 15,
              top: 15,
              bottom: 15,
            }}
            value={value}
            status={selectedValue === value ? 'checked' : 'unchecked'}
            onPress={() => onValueChange(value)}
          />
        </View>
      </View>
      <View>
        {visibleFeatures.map((feature, index) => (
          <View key={index} style={styles.featureRow}>
            <Icon
              source={
                feature.isAvailable
                  ? 'check-circle-outline'
                  : 'close-circle-outline'
              }
              size={18}
              color={
                feature.isAvailable ? colors.green.success : customColors.error
              }
            />
            <Text style={styles.featureText} variant="bodySmall">
              {feature.name}
            </Text>
          </View>
        ))}

        {features.length > 3 && (
          <TouchableOpacity
            onPress={() => setShowAllFeatures(!showAllFeatures)}>
            <Text style={styles.seeMoreText}>
              {showAllFeatures ? 'See Less' : 'See More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );
};

export default PlansCard;

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.background,
    borderRadius: 10,
    marginBottom: styleConstants.spacing.x20,
  },
  cardContentStyle: {
    padding: styleConstants.spacing.s10,
  },
  featureRow: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: styleConstants.spacing.s,
  },
  featureText: {
    flex: 1,
    marginLeft: styleConstants.spacing.s10,
  },
  featuresContainer: {
    marginTop: styleConstants.spacing.s10,
  },
  headerRow: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  seeMoreText: {
    color: colors.primary,
    fontWeight: 'bold',
    marginTop: styleConstants.spacing.s,
    textAlign: 'left',
  },
  selectedCard: {
    borderColor: colors.primary,
    borderWidth: 1.5,
  },
  statusRow: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  statusText: {
    borderRadius: 10,
    color: customColors.white,
    fontWeight: 'bold',
    paddingHorizontal: 10,
    paddingVertical: 2,
  },
  title: {
    fontWeight: 'bold',
  },
});
