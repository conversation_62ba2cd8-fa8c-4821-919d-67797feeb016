import {
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useState} from 'react';
import customColors from '../../../theme/customColors';
import {ActivityIndicator, Icon, Text, TextInput} from 'react-native-paper';
import CustomTextInput from '../../../components/InputFields/Textinput';
import styleConstants from '../../../theme/styleConstants';
import {
  useGetOrderCountQuery,
  useGetOrderQuery,
  useLazyGetOrderQuery,
} from '../../../redux/order/orderApiSlice';
import {OrderLineItem} from '../../../types/order';
import {useFocusEffect} from '@react-navigation/native';
import {colors} from '../../../theme/colors';
import OrderCard from '../../../components/OrderCard/OrderCard';

type Props = {};

const OrderScreen = (props: Props) => {
  const [skip, setSkip] = useState(0);
  const [orderList, setOrderList] = useState<OrderLineItem[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const {data: orderCountData, refetch: refetchOrderCount} =
    useGetOrderCountQuery();

  const [getOrdersList, {isLoading, isFetching}] = useLazyGetOrderQuery();
  const handleLoadMore = useCallback(() => {
    if (!isFetching && hasMore) {
      const newSkip = skip + 10;
      getOrdersList({
        filter: {
          where: {},
          include: [
            {
              relation: 'order',
              required: true,
              scope: {
                include: [
                  {relation: 'promoCode'},
                  {relation: 'shippingAddress'},
                  {relation: 'billingAddress'},
                ],
              },
            },
            {
              relation: 'productVariant',
              required: true,
              scope: {
                where: {name: {ilike: `%${debouncedSearch}%`}},
                include: [{relation: 'product'}, {relation: 'featuredAsset'}],
              },
            },
          ],
          order: ['createdOn DESC'],
          limit: 10,
          offset: newSkip,
        },
      })
        .unwrap()
        .then(newData => {
          setOrderList(prev => [...prev, ...newData]);
          setSkip(newSkip);
          // Check if we received fewer items than the limit (i.e., last page)
          if (newData.length < 10) {
            setHasMore(false);
          }
        });
    }
  }, [skip, isFetching, hasMore, debouncedSearch]);

  const loadInitialPromoCodes = useCallback(async () => {
    setSkip(0);
    setHasMore(true);
    const initialData = await getOrdersList({
      filter: {
        where: {},
        include: [
          {
            relation: 'order',
            required: true,
            scope: {
              include: [
                {relation: 'promoCode'},
                {relation: 'shippingAddress'},
                {relation: 'billingAddress'},
              ],
            },
          },
          {
            relation: 'productVariant',
            required: true,
            scope: {
              where: {name: {ilike: `%${debouncedSearch}%`}},
              include: [{relation: 'product'}, {relation: 'featuredAsset'}],
            },
          },
        ],
        order: ['createdOn DESC'],
        limit: 10,
        offset: 0,
      },
    }).unwrap();
    setOrderList(initialData);
    if (orderCountData?.count) {
      if (initialData.length <= orderCountData?.count) {
        setHasMore(false);
      }
    }

    refetchOrderCount(); // Optional, to refresh warehouse count
  }, [getOrdersList, refetchOrderCount, debouncedSearch]);

  useFocusEffect(
    useCallback(() => {
      loadInitialPromoCodes();
    }, [loadInitialPromoCodes]),
  );

  return (
    <SafeAreaView style={styles.container}>
      <CustomTextInput
        value={debouncedSearch}
        placeholder="Search Orders"
        onChangeText={text => {
          setDebouncedSearch(text);
        }}
        onSubmitEditing={() => {
          loadInitialPromoCodes();
        }}
      />
      <FlatList
        data={Array.isArray(orderList) ? orderList : []}
        renderItem={({item, index}) => {
          return (
            <OrderCard
              item={item}
              index={index}
              loadList={loadInitialPromoCodes}
            />
          );
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() => {
          if (
            hasMore &&
            isFetching &&
            orderCountData?.count &&
            orderCountData?.count > 0
          ) {
            return <ActivityIndicator size={20} color={colors.primary} />;
          }
        }}
        ListEmptyComponent={() => {
          return (
            <View style={styles.emptyContainer}>
              {isLoading && !orderList ? (
                <ActivityIndicator size={20} color={customColors.appBlue} />
              ) : (
                <Text variant="bodyMedium" style={styles.emptyText}>
                  No Orders found.
                </Text>
              )}
            </View>
          );
        }}
      />
    </SafeAreaView>
  );
};

export default OrderScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: customColors.white,
    padding: styleConstants.spacing.x20,
  },
  emptyContainer: {
    padding: styleConstants.spacing.x20,
    alignItems: 'center',
    paddingTop: '50%',
  },
  emptyText: {
    color: customColors.appBlue,
    fontSize: 16,
  },
});
