import React from 'react';
import {FlatList, Image, ScrollView, StyleSheet, View} from 'react-native';
import {DataTable, Text} from 'react-native-paper';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

interface ColumnDefinition {
  isImage?: any;
  label: string;
  key: string;
  width?: number;
}

interface GenericPaperTableProps {
  title?: string;
  columns: ColumnDefinition[];
  data: Record<string, any>[];
  numberOfLines: number;
  isImage?: boolean;
}

const GenericPaperTable: React.FC<GenericPaperTableProps> = ({
  title = 'Table',
  columns,
  data,
  numberOfLines,
}) => {
  const totalWidth = columns.reduce((sum, col) => sum + (col.width ?? 100), 0);
  const getNestedValue = (obj: any, key: string) => {
    return key.split('.').reduce((o, k) => (o ? o[k] : null), obj);
  };
  const renderRow = ({
    item,
    index,
  }: {
    item: Record<string, any>;
    index: number;
  }) => (
    <DataTable.Row
      key={`row-${index}`}
      style={[
        styles.dataRow,
        index === 0 && {
          borderTopWidth: 1,
          borderTopColor: 'black',
        },
      ]}>
      {columns.map((col, colIndex) => (
        <DataTable.Cell
          key={`cell-${index}-${colIndex}`}
          style={[
            styles.cell,
            {
              width: col.width ?? 100,
              borderRightWidth: colIndex !== columns.length - 1 ? 1 : 0,
              borderRightColor: 'black',
            },
          ]}>
          {col.isImage && getNestedValue(item, col.key) ? (
            <Image
              source={{uri: getNestedValue(item, col.key)}}
              style={styles.image}
              //   resizeMode="contain"
            />
          ) : col.key === 'isRequired' ? (
            <Text>{item[col.key] ? 'Yes' : 'No'}</Text>
          ) : (
            <Text numberOfLines={numberOfLines} ellipsizeMode="tail">
              {String(item[col.key] ?? '')}
            </Text>
          )}
        </DataTable.Cell>
      ))}
    </DataTable.Row>
  );

  return (
    <View style={{padding: 16}}>
      {title ? (
        <>
          <Text
            variant="titleMedium"
            style={{color: colors.tertiary, marginBottom: 10}}>
            {title}
          </Text>
        </>
      ) : null}

      {data && data.length > 0 ? (
        <ScrollView horizontal showsHorizontalScrollIndicator>
          <DataTable
            style={{
              minWidth: totalWidth,
              borderWidth: 1,
              borderColor: 'black',
            }}>
            <DataTable.Header>
              {columns.map((col, index) => (
                <DataTable.Title
                  key={`header-${index}`}
                  style={[
                    styles.cell,
                    {
                      width: col.width ?? 100,
                      borderRightWidth: index !== columns.length - 1 ? 1 : 0,
                      borderRightColor: 'black',
                    },
                  ]}>
                  {col.label}
                </DataTable.Title>
              ))}
            </DataTable.Header>

            <FlatList
              data={data}
              keyExtractor={(_, index) => `row-${index}`}
              renderItem={renderRow}
              nestedScrollEnabled
              scrollEnabled={false}
            />
          </DataTable>
        </ScrollView>
      ) : (
        <Text style={{flex: 1, textAlign: 'left', marginTop: 10, fontSize: 13}}>
          No data found
        </Text>
      )}
    </View>
  );
};

export default GenericPaperTable;
const styles = StyleSheet.create({
  image: {
    width: 60,
    height: 60,
    borderRadius: 4,
  },
  cell: {
    padding: 8,
    justifyContent: 'center',
  },
  dataRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderColor: colors.gray.light,
    backgroundColor: customColors.white,
  },
});
