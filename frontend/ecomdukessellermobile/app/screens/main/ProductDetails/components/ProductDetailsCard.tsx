import React from 'react';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';

interface DetailItem {
  label: string;
  value: string;
}

interface ProductDetailFlatListProps {
  collection?: string;
  tag?: string;
  details?: string;
  moreInfo?: string;
}

const ProductDetailFlatList: React.FC<ProductDetailFlatListProps> = ({
  collection,
  tag,
  details,
  moreInfo,
}) => {
  const detailData: DetailItem[] = [
    collection ? {label: 'Collection', value: collection} : null,
    tag ? {label: 'Tag', value: tag} : null,
    details ? {label: 'Details', value: details} : null,
    moreInfo ? {label: 'More Info', value: moreInfo} : null,
  ].filter(Boolean) as DetailItem[];

  const renderItem = ({item}: {item: DetailItem}) => (
    <View style={styles.row}>
      <Text style={styles.label}>{item.label}:</Text>
      <Text style={styles.value}>{item.value}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={detailData}
        keyExtractor={(item, index) => `${item.label}-${index}`}
        renderItem={renderItem}
        scrollEnabled={false}
      />
    </View>
  );
};

export default ProductDetailFlatList;

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: colors.gray.light,
    borderRadius: 8,
    padding: 15,
    marginVertical: 10,
    backgroundColor: customColors.white,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  label: {
    fontWeight: 'bold',
    color: customColors.textBlack,
    marginRight: 6,
    minWidth: 100,
  },
  value: {
    flex: 1,
    color: colors.gray.dark,
    fontSize: 14,
  },
});
