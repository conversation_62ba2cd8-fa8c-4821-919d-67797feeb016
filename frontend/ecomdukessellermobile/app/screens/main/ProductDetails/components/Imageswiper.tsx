import React, {useState} from 'react';
import {Dimensions, FlatList, Image, StyleSheet, View} from 'react-native';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

const ImageSwiper = ({images}: {images: string[]}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const {width} = Dimensions.get('window');

  const handleScroll = (e: any) => {
    const index = Math.round(e.nativeEvent.contentOffset.x / width);
    setCurrentIndex(index);
  };
  return (
    <View>
      <FlatList
        data={images}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        renderItem={({item}) => (
          <Image source={{uri: item}} style={styles.image} />
        )}
        keyExtractor={(_, index) => index.toString()}
      />
      <View style={styles.dotContainer}>
        {images.map((_, i) => (
          <View
            key={i}
            style={[styles.dot, currentIndex === i && styles.activeDot]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 10,
  },
  dot: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: colors.gray.medium,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: customColors.textBlack,
  },
  image: {
    width: 370,
    height: 350,
    objectFit: 'fill',
    borderRadius: 8,
  },
});
export default ImageSwiper;
