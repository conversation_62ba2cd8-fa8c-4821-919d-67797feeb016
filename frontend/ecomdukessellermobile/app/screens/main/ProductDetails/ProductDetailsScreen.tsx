import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {ActivityIndicator, Divider} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import ImageSwiper from './components/Imageswiper';
import {Images} from '../../../assets/images';
import GenericTable from './components/GenericTable';
import ProductDetailCard from './components/ProductDetailsCard';
import {useGetproductByIdQuery} from '../../../redux/products/productApiSlice';
import {customIncludes} from '../../../constants/product';
import {RouteProp, useRoute} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigations/types';
type RoutePropType = RouteProp<MainStackParamList, 'productDetails'>;
export interface ColumnDefinition {
  label: string;
  key: string;
  isImage?: boolean;
  width?: number;
}
const ProductDetailScreen = () => {
  const route = useRoute<RoutePropType>();
  const {productId} = route.params;

  const [expanded, setExpanded] = useState(false);
  const productImages = [Images.google, Images.google, Images.google];
  const {
    data: product,
    isLoading,
    isFetching,
  } = useGetproductByIdQuery(
    {id: productId, filter: {include: customIncludes}},
    {
      refetchOnMountOrArgChange: true,
    },
  );
  const isOutOfStock = product?.productVariants?.some(
    variant => variant.outOfStockThreshold < 1,
  );

  const stock = isOutOfStock ? 'Out of Stock' : 'In Stock';

  const previewUrls = product?.productAssets.map(pa => pa.asset.previewUrl);

  const customizationColumns: ColumnDefinition[] = [
    {
      label: 'Label',
      key: 'label',
      isImage: false,
      width: 100,
    },
    {
      label: 'Name',
      key: 'name',
      isImage: false,
      width: 70,
    },
    {
      label: 'Placeholder',
      key: 'placeholder',
      isImage: false,
      width: 140,
    },
    {
      label: 'Field Type',
      key: 'fieldType',
      isImage: false,
      width: 85,
    },
    {
      label: 'Required',
      key: 'isRequired',
      isImage: false,
      width: 70,
    },
  ];
  const productVariantColumns: ColumnDefinition[] = [
    {
      label: 'Image',
      key: 'featuredAsset.previewUrl',
      isImage: true,
      width: 80,
    },
    {
      label: 'Name',
      key: 'name',
      isImage: false,
      width: 200,
    },
  ];
  const productSpecificationColumns: ColumnDefinition[] = [
    {
      label: 'ID',
      key: 'id',
      isImage: false,
      width: 100,
    },
    {
      label: 'Name',
      key: 'name',
      isImage: false,
      width: 80,
    },
    {
      label: 'Values',
      key: 'value',
      isImage: false,
      width: 80,
    },
  ];
  const isJsonString = (str: string) => {
    return (
      typeof str === 'string' &&
      str.trim().startsWith('{') &&
      str.trim().endsWith('}')
    );
  };

  const extractPlainText = (raw: string): string => {
    if (!isJsonString(raw)) {
      if (raw.includes('<') && raw.includes('>')) {
        return raw.replace(/<[^>]*>/g, '').trim();
      }
      return raw;
    }

    const parsed = JSON.parse(raw);
    const blocks = parsed.blocks || [];
    return blocks
      .map((block: any) => block.text)
      .join('\n')
      .trim();
  };

  const ExpandableTextSection = ({
    title,
    content,
    fallback,
    previewCharLimit = 100,
  }: {
    title: string;
    content?: string;
    fallback: string;
    previewCharLimit?: number;
  }) => {
    const [expanded, setExpanded] = useState(false);

    const fullText = content ? extractPlainText(content) : fallback;
    const shouldShowToggle = fullText.length > previewCharLimit;

    const hasContent = !!content;
    return (
      <View style={{marginTop: 16, marginLeft: 16, marginRight: 16}}>
        <Text style={styles.heading}>{title}</Text>
        <Text
          style={[
            styles.paragraph,
            {color: hasContent ? customColors.textBlack : colors.gray.dark},
            {textAlign: 'left'},
            {fontSize: hasContent ? 15 : 13},
          ]}
          numberOfLines={expanded || !shouldShowToggle ? undefined : 3}>
          {hasContent ? extractPlainText(content) : fallback}
        </Text>
        {shouldShowToggle && (
          <TouchableOpacity onPress={() => setExpanded(!expanded)}>
            <Text style={styles.seeMoreView}>
              {expanded ? 'See Less' : 'See More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };
  return (
    <ScrollView contentContainerStyle={styles.container}>
      {isLoading && <ActivityIndicator />}
      <View>
        <ImageSwiper images={previewUrls || []} />
      </View>

      <Text style={styles.title}>{product?.name}</Text>

      <>
        <Text numberOfLines={expanded ? undefined : 3} style={[styles.review]}>
          {product?.description}
        </Text>
        <TouchableOpacity onPress={() => setExpanded(!expanded)}>
          <Text style={styles.seeMoreView}>
            {expanded ? 'See Less' : 'See More'}
          </Text>
        </TouchableOpacity>
        <View
          style={[
            styles.statusBadge,
            stock === 'In Stock' ? styles.inStock : styles.outOfStock,
          ]}>
          <Text
            style={[
              styles.statusText,
              stock === 'In Stock' ? styles.inStockText : styles.outOfStockText,
            ]}>
            {stock}
          </Text>
        </View>
      </>
      <ProductDetailCard
        collection={product?.collection.name}
        details={
          product?.productDetail?.details
            ? extractPlainText(product.productDetail.details)
            : undefined
        }
        moreInfo={
          product?.productMoreInfo?.info
            ? extractPlainText(product.productMoreInfo.info)
            : undefined
        }
      />
      {product?.productCustomizationFields && (
        <GenericTable
          title="Customization Fields"
          columns={customizationColumns}
          data={product.productCustomizationFields}
          numberOfLines={2}
        />
      )}
      {product?.productVariants && (
        <GenericTable
          title="Product Variants"
          columns={productVariantColumns}
          data={product.productVariants}
          numberOfLines={2}
        />
      )}
      {product?.productSpecifications && (
        <GenericTable
          title="Specifications"
          columns={productSpecificationColumns}
          data={product.productSpecifications}
          numberOfLines={2}
        />
      )}

      <ExpandableTextSection
        title="Return Policy"
        content={product?.productReturnPolicy?.returnPolicy}
        fallback="No data found"
      />

      <ExpandableTextSection
        title="Terms and Conditions"
        content={product?.productTermsAndCondition?.terms}
        fallback="No data found"
      />
      <ExpandableTextSection
        title="Disclaimer"
        content={product?.productDisclaimer?.disclaimer}
        fallback="No data found"
      />

      <Divider style={styles.divider} />
    </ScrollView>
  );
};

export default ProductDetailScreen;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: customColors.white,
  },

  productImage: {
    width: '100%',
    height: 250,
    borderRadius: 8,
    backgroundColor: colors.gray.light,
    objectFit: 'contain',
  },
  favoriteIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: customColors.white,
  },
  editIcon: {
    position: 'absolute',
    bottom: 35,
    right: 10,
    backgroundColor: customColors.white,
  },

  stockText: {
    color: customColors.primaryContainer,
    marginBottom: 4,
    marginTop: 10,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
  },
  review: {
    color: colors.gray.dark,
    marginBottom: 12,
  },
  priceSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  currentPrice: {
    fontWeight: 'bold',
    fontSize: 18,
    color: customColors.textBlack,
  },
  mrp: {
    textDecorationLine: 'line-through',
    color: colors.gray.medium,
  },
  deal: {
    color: customColors.primaryContainer,
    fontSize: 13,
  },
  ratingContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  originText: {
    fontSize: 12,
    color: colors.gray.dark,
    marginBottom: 12,
  },
  bold: {
    fontWeight: 'bold',
  },
  pincodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    borderRadius: 20,
    paddingHorizontal: 8,
    marginTop: 10,
  },
  pincodeInput: {
    flex: 1,
    marginLeft: 4,
    paddingVertical: 6,
  },
  deliveryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  green: {
    color: colors.green.success,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    marginTop: 10,
  },
  shareContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  shareText: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {width: 150},
  countryText: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray.light,
    marginTop: 10,
  },
  seeMoreView: {
    color: colors.tertiary,
    marginTop: 4,
    fontSize: 14,
  },

  statusBadge: {
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 10,
    alignSelf: 'flex-start',
    marginTop: 10,
  },
  inStock: {
    backgroundColor: customColors.backgroundGreen,
  },
  outOfStock: {
    backgroundColor: customColors.backgroundRed,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  inStockText: {
    color: colors.green.success,
  },
  outOfStockText: {
    color: customColors.red,
  },
  heading: {
    fontSize: 15,
    fontWeight: 'bold',
    marginBottom: 6,
    color: colors.tertiary,
  },
  paragraph: {
    fontSize: 14,
    color: colors.gray.dark,
    lineHeight: 22,
  },
});
