import {ProductView, ProductViewRelations, ProductVariant} from '../models';
import {PgDataSource} from '../datasources';
import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {ProductVariantRepository} from './product-variant.repository';

export class ProductViewRepository extends SequelizeUserModifyCrudRepositoryCore<
  ProductView,
  typeof ProductView.prototype.id,
  ProductViewRelations
> {

  public readonly productVariant: BelongsToAccessor<ProductVariant, typeof ProductView.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>, @repository.getter('ProductVariantRepository') protected productVariantRepositoryGetter: Getter<ProductVariantRepository>,
  ) {
    super(ProductView, dataSource, getCurrentUser);
    this.productVariant = this.createBelongsToAccessorFor('productVariant', productVariantRepositoryGetter,);
    this.registerInclusionResolver('productVariant', this.productVariant.inclusionResolver);
  }
}
