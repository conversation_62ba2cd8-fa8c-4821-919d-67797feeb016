import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PgDataSource} from '../datasources';
import {Ticket, TicketRelations} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';

export class TicketRepository extends SequelizeUserModifyCrudRepositoryCore<
  Ticket,
  typeof Ticket.prototype.id,
  TicketRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Ticket, dataSource, getCurrentUser);
  }
}
