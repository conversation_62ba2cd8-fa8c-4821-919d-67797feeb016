import {Getter, inject} from '@loopback/core';
import {
  BelongsToAccessor,
  HasManyRepositoryFactory,
  repository,
} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  SellerShippingProfile,
  SellerShippingProfileRelations,
  ShippingMethod,
  SellerShippingCharge,
  ProductShippingCharge,
  WeightBasedShippingRule,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {ShippingMethodRepository} from './shipping-method.repository';
import {SellerShippingChargeRepository} from './seller-shipping-charge.repository';
import {ProductShippingChargeRepository} from './product-shipping-charge.repository';
import {WeightBasedShippingRuleRepository} from './weight-based-shipping-rule.repository';

export class SellerShippingProfileRepository extends SequelizeUserModifyCrudRepositoryCore<
  SellerShippingProfile,
  typeof SellerShippingProfile.prototype.id,
  SellerShippingProfileRelations
> {
  public readonly shippingMethod: BelongsToAccessor<
    ShippingMethod,
    typeof SellerShippingProfile.prototype.id
  >;

  public readonly shippingCharges: HasManyRepositoryFactory<
    SellerShippingCharge,
    typeof SellerShippingProfile.prototype.id
  >;

  public readonly productShippingCharges: HasManyRepositoryFactory<
    ProductShippingCharge,
    typeof SellerShippingProfile.prototype.id
  >;

  public readonly weightBasedRules: HasManyRepositoryFactory<
    WeightBasedShippingRule,
    typeof SellerShippingProfile.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ShippingMethodRepository')
    protected shippingMethodRepositoryGetter: Getter<ShippingMethodRepository>,
    @repository.getter('SellerShippingChargeRepository')
    protected sellerShippingChargeRepositoryGetter: Getter<SellerShippingChargeRepository>,
    @repository.getter('ProductShippingChargeRepository')
    protected productShippingChargeRepositoryGetter: Getter<ProductShippingChargeRepository>,
    @repository.getter('WeightBasedShippingRuleRepository')
    protected weightBasedShippingRuleRepositoryGetter: Getter<WeightBasedShippingRuleRepository>,
  ) {
    super(SellerShippingProfile, dataSource, getCurrentUser);

    this.shippingMethod = this.createBelongsToAccessorFor(
      'shippingMethod',
      shippingMethodRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingMethod',
      this.shippingMethod.inclusionResolver,
    );

    this.shippingCharges = this.createHasManyRepositoryFactoryFor(
      'shippingCharges',
      sellerShippingChargeRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingCharges',
      this.shippingCharges.inclusionResolver,
    );

    this.productShippingCharges = this.createHasManyRepositoryFactoryFor(
      'productShippingCharges',
      productShippingChargeRepositoryGetter,
    );
    this.registerInclusionResolver(
      'productShippingCharges',
      this.productShippingCharges.inclusionResolver,
    );

    this.weightBasedRules = this.createHasManyRepositoryFactoryFor(
      'weightBasedRules',
      weightBasedShippingRuleRepositoryGetter,
    );
    this.registerInclusionResolver(
      'weightBasedRules',
      this.weightBasedRules.inclusionResolver,
    );
  }
}
