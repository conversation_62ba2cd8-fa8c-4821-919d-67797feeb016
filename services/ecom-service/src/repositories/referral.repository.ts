import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PgDataSource} from '../datasources';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {Referral, ReferralRelations} from '../models/referral.model';

export class ReferralRepository extends SequelizeUserModifyCrudRepositoryCore<
  Referral,
  typeof Referral.prototype.id,
  ReferralRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Referral, dataSource, getCurrentUser);
  }
}
