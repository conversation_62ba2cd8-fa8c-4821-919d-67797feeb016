import {ProductViewCount, ProductViewCountRelations, ProductVariant} from '../models';
import {PgDataSource} from '../datasources';
import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {ProductVariantRepository} from './product-variant.repository';

export class ProductViewCountRepository extends SequelizeUserModifyCrudRepositoryCore<
  ProductViewCount,
  typeof ProductViewCount.prototype.id,
  ProductViewCountRelations
> {

  public readonly productVariant: BelongsToAccessor<ProductVariant, typeof ProductViewCount.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>, @repository.getter('ProductVariantRepository') protected productVariantRepositoryGetter: Getter<ProductVariantRepository>,
  ) {
    super(ProductViewCount, dataSource, getCurrentUser);
    this.productVariant = this.createBelongsToAccessorFor('productVariant', productVariantRepositoryGetter,);
    this.registerInclusionResolver('productVariant', this.productVariant.inclusionResolver);
  }
}
