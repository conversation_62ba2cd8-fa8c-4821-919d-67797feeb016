import {Getter, inject} from '@loopback/core';
import {PlanPricing, PlanPricingRelations} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {SequelizeDataSource} from '@loopback/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';

export class PlanPricingRepository extends SequelizeUserModifyCrudRepositoryCore<
  PlanPricing,
  typeof PlanPricing.prototype.id,
  PlanPricingRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: SequelizeDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(PlanPricing, dataSource, getCurrentUser);
  }
}
