import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PgDataSource} from '../datasources';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {
  ReferralProgram,
  ReferralProgramRelations,
} from '../models/referral-program.model';

export class ReferralProgramRepository extends SequelizeUserModifyCrudRepositoryCore<
  ReferralProgram,
  typeof ReferralProgram.prototype.id,
  ReferralProgramRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(ReferralProgram, dataSource, getCurrentUser);
  }
}
