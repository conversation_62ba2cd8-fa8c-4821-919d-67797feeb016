import {Getter, inject} from '@loopback/core';
import {HasManyRepositoryFactory, repository} from '@loopback/repository';
import {SequelizeDataSource} from '@loopback/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {Chat, ChatRelations, ChatMessage} from '../models';
import {ChatMessageRepository} from './chat-message.repository';

export class ChatRepository extends SequelizeUserModifyCrudRepositoryCore<
  Chat,
  typeof Chat.prototype.id,
  ChatRelations
> {
  public readonly messages: HasManyRepositoryFactory<
    ChatMessage,
    typeof Chat.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: SequelizeDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ChatMessageRepository')
    protected chatMessageRepositoryGetter: Getter<ChatMessageRepository>,
  ) {
    super(Chat, dataSource, getCurrentUser);
    this.messages = this.createHasManyRepositoryFactoryFor(
      'messages',
      chatMessageRepositoryGetter,
    );
    this.registerInclusionResolver('messages', this.messages.inclusionResolver);
  }
}
