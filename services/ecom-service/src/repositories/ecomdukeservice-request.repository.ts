import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';

import {PgDataSource} from '../datasources';
import {
  EcomdukeserviceRequest,
  EcomDukeserviceRequestRelations,
  Ecomdukeservice,
} from '../models';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {EcomdukeserviceRepository} from './ecomdukeservice.repository';

export class EcomdukeserviceRequestRepository extends SequelizeUserModifyCrudRepositoryCore<
  EcomdukeserviceRequest,
  typeof EcomdukeserviceRequest.prototype.id,
  EcomDukeserviceRequestRelations
> {
  public readonly ecomdukeservice: BelongsToAccessor<
    Ecomdukeservice,
    typeof EcomdukeserviceRequest.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('EcomdukeserviceRepository')
    protected ecomdukeserviceRepositoryGetter: Getter<EcomdukeserviceRepository>,
  ) {
    super(EcomdukeserviceRequest, dataSource, getCurrentUser);
    this.ecomdukeservice = this.createBelongsToAccessorFor(
      'ecomdukeservice',
      ecomdukeserviceRepositoryGetter,
    );
    this.registerInclusionResolver(
      'ecomdukeservice',
      this.ecomdukeservice.inclusionResolver,
    );
  }
}
