import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';

import {PgDataSource} from '../datasources';
import {
  Ecomdukeservice,
  EcomDukeserviceRelations,
  TaxCategory,
} from '../models';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {TaxCategoryRepository} from './tax-category.repository';

export class EcomdukeserviceRepository extends SequelizeUserModifyCrudRepositoryCore<
  Ecomdukeservice,
  typeof Ecomdukeservice.prototype.id,
  EcomDukeserviceRelations
> {
  public readonly taxCategory: BelongsToAccessor<
    TaxCategory,
    typeof TaxCategory.prototype.id
  >;
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('TaxCategoryRepository')
    protected taxCategoryRepositoryGetter: Getter<TaxCategoryRepository>,
  ) {
    super(Ecomdukeservice, dataSource, getCurrentUser);
    this.taxCategory = this.createBelongsToAccessorFor(
      'taxCategory',
      taxCategoryRepositoryGetter,
    );
    this.registerInclusionResolver(
      'taxCategory',
      this.taxCategory.inclusionResolver,
    );
  }
}
