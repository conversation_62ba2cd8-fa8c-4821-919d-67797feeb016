import {Getter, inject} from '@loopback/core';
import {HasManyRepositoryFactory, repository} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  ShippingMethod,
  ShippingMethodRelations,
  SellerShippingProfile,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {SellerShippingProfileRepository} from './seller-shipping-profile.repository';

export class ShippingMethodRepository extends SequelizeUserModifyCrudRepositoryCore<
  ShippingMethod,
  typeof ShippingMethod.prototype.id,
  ShippingMethodRelations
> {
  public readonly sellerProfiles: HasManyRepositoryFactory<
    SellerShippingProfile,
    typeof ShippingMethod.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('SellerShippingProfileRepository')
    protected sellerShippingProfileRepositoryGetter: Getter<SellerShippingProfileRepository>,
  ) {
    super(ShippingMethod, dataSource, getCurrentUser);

    this.sellerProfiles = this.createHasManyRepositoryFactoryFor(
      'sellerProfiles',
      sellerShippingProfileRepositoryGetter,
    );
    this.registerInclusionResolver(
      'sellerProfiles',
      this.sellerProfiles.inclusionResolver,
    );
  }
}
