import {BindingScope, inject, injectable} from '@loopback/core';
import {FilterExcludingWhere, repository} from '@loopback/repository';
import {
  ProductViewRepository,
  ProductViewCountRepository,
} from '../repositories';
import {ProductVariantRepository} from '../repositories';
import {ProductVariant} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions, ILogger, LOGGER} from '@sourceloop/core';
import {ProductStatus} from '@local/core';

@injectable({scope: BindingScope.TRANSIENT})
export class ProductViewService {
  constructor(
    @repository(ProductViewRepository)
    private productViewRepository: ProductViewRepository,
    @repository(ProductViewCountRepository)
    private productViewCountRepository: ProductViewCountRepository,
    @repository(ProductVariantRepository)
    private productVariantRepository: ProductVariantRepository,
    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,
    @inject(LOGGER.LOGGER_INJECT) public logger: ILogger,
  ) {}

  /**
   * Record a product view for a user
   * @param productVariantId - The ID of the product variant being viewed
   * @param userId - The ID of the user viewing the product
   */
  async recordProductView(productVariantId: string): Promise<void> {
    if (!productVariantId || !this.currentUser.userTenantId) {
      return;
    }

    const userId = this.currentUser.userTenantId;
    // First, check if the product variant exists
    const productVariant =
      await this.productVariantRepository.findById(productVariantId);

    if (!productVariant) {
      this.logger.error("Can't find the variant");
      return;
    }

    // Check if the user has already viewed this product
    const existingView = await this.productViewRepository.findOne({
      where: {
        productVariantId,
        userId,
      },
    });

    const now = new Date();

    if (existingView) {
      // Update the viewed_at timestamp
      await this.productViewRepository.updateById(existingView.id, {
        viewedAt: now,
      });
    } else {
      // Create a new product view record
      await this.productViewRepository.create({
        productVariantId,
        userId,
        viewedAt: now,
      });

      // Update the view count
      const existingCount = await this.productViewCountRepository.findOne({
        where: {
          productVariantId,
        },
      });

      if (existingCount) {
        // Increment the view count
        await this.productViewCountRepository.updateById(existingCount.id, {
          viewCount: existingCount.viewCount + 1,
        });
      } else {
        // Create a new view count record
        await this.productViewCountRepository.create({
          productVariantId,
          viewCount: 1,
        });
      }
    }
  }

  /**
   * Get recently viewed products for a user
   * @param filter - Filter options for the query
   * @param xOrigin - Request origin to determine if customer filtering is needed
   */
  async getRecentlyViewedProducts(
    filter?: FilterExcludingWhere<ProductVariant>,
    xOrigin?: string,
  ): Promise<ProductVariant[]> {
    const userId = this.currentUser.userTenantId;
    if (!userId) return [];
    const recentViews = await this.productViewRepository.find({
      where: {
        userId,
      },
      order: ['viewedAt DESC'],
      limit: filter?.limit ?? 10,
      fields: {
        productVariantId: true,
      },
    });

    let variantFilter = {
      where: {
        id: {inq: recentViews.map(view => view.productVariantId)},
      },
      ...filter,
    };

    // For customer requests, only show variants of approved products
    if (xOrigin === 'ecomdukes-customer') {
      variantFilter = {
        ...variantFilter,
        include: [
          ...(filter?.include || []),
          {
            relation: 'product',
            scope: {
              where: {status: ProductStatus.APPROVED},
            },
          },
        ],
      };
    }

    return this.productVariantRepository.find(variantFilter);
  }

  /**
   * Get most viewed products
   * @param filter - Filter options for the query
   * @param xOrigin - Request origin to determine if customer filtering is needed
   */
  async getMostViewedProducts(
    filter?: FilterExcludingWhere<ProductVariant>,
    xOrigin?: string,
  ): Promise<ProductVariant[]> {
    const viewCounts = await this.productViewCountRepository.find({
      where: {
        viewCount: {gt: 0},
      },
      order: ['viewCount DESC'],
      limit: filter?.limit ?? 10,
      fields: {
        productVariantId: true,
      },
    });

    let variantFilter = {
      where: {
        id: {inq: viewCounts.map(count => count.productVariantId)},
      },
      ...filter,
    };

    // For customer requests, only show variants of approved products
    if (xOrigin === 'ecomdukes-customer') {
      variantFilter = {
        ...variantFilter,
        include: [
          ...(filter?.include || []),
          {
            relation: 'product',
            scope: {
              where: {status: ProductStatus.APPROVED},
            },
          },
        ],
      };
    }

    return this.productVariantRepository.find(variantFilter);
  }
}
