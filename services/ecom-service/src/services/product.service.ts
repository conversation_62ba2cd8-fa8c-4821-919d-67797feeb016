import {injectable, BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  AssetRepository,
  ProductAssetRepository,
  ProductBoxContentRepository,
  ProductCustomizationFieldRepository,
  ProductCustomizationOptionRepository,
  ProductDetailRepository,
  ProductDisclaimerRepository,
  ProductFacetValueRepository,
  ProductMoreInfoRepository,
  ProductOptionGroupRepository,
  ProductOptionRepository,
  ProductPersonalWorkRepository,
  ProductRepository,
  ProductReturnPolicyRepository,
  ProductSpecificationRepository,
  ProductSuitabilityRepository,
  ProductTermsAndConditionRepository,
  ProductUniquenessRepository,
  ProductVariantOptionRepository,
  ProductVariantPriceRepository,
  ProductVariantRepository,
  TaxCategoryRepository,
} from '../repositories';
import {
  Group,
  OptionDto,
  OptionVariantDto,
  Product,
  ProductAsset,
  ProductBoxContent,
  ProductCustomizationDto,
  ProductDetail,
  ProductDisclaimer,
  ProductDto,
  ProductFacetValue,
  ProductMoreInfo,
  ProductOption,
  ProductOptionGroupDto,
  ProductPersonalWork,
  ProductReturnPolicy,
  ProductSpecification,
  ProductSuitability,
  ProductTermsAndCondition,
  ProductUniqueness,
  ProductVariant,
  ProductVariantOption,
  TaxCategory,
  Value,
  BulkStatusUpdateDto,
  SingleStatusUpdateDto,
} from '../models';
import {Transaction} from '@loopback/sequelize';
import {generateUniqueSKU, nameToCode} from '../utils';
import {DEFAULT_CURRENCY, PREFIXES, ProductStatus} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {ProductVariantService} from './product-variant.service';

@injectable({scope: BindingScope.TRANSIENT})
export class ProductService {
  constructor(
    @repository(ProductRepository)
    public productRepository: ProductRepository,
    @repository(ProductVariantRepository)
    public productVariantRepository: ProductVariantRepository,
    @repository(AssetRepository)
    public assetRepository: AssetRepository,
    @repository(ProductOptionGroupRepository)
    public productOptionGroupRepository: ProductOptionGroupRepository,
    @repository(ProductOptionRepository)
    public productOptionRepository: ProductOptionRepository,
    @repository(ProductFacetValueRepository)
    public productFacetValueRepository: ProductFacetValueRepository,
    @repository(ProductBoxContentRepository)
    public productBoxContentRepository: ProductBoxContentRepository,
    @repository(ProductDetailRepository)
    public productDetailRepository: ProductDetailRepository,
    @repository(ProductDisclaimerRepository)
    public productDisclaimerRepository: ProductDisclaimerRepository,
    @repository(ProductMoreInfoRepository)
    public productMoreInfoRepository: ProductMoreInfoRepository,
    @repository(ProductReturnPolicyRepository)
    public productReturnPolicyRepository: ProductReturnPolicyRepository,
    @repository(ProductSpecificationRepository)
    public productSpeicificationRepository: ProductSpecificationRepository,
    @repository(ProductSuitabilityRepository)
    public productSuitabilityRepository: ProductSuitabilityRepository,
    @repository(ProductTermsAndConditionRepository)
    public productTermsAndConditionRepository: ProductTermsAndConditionRepository,
    @repository(ProductUniquenessRepository)
    public productUniquenessRepository: ProductUniquenessRepository,
    @repository(ProductAssetRepository)
    public productAssetRepository: ProductAssetRepository,
    @repository(ProductVariantPriceRepository)
    public productVariantPriceRepository: ProductVariantPriceRepository,
    @repository(ProductCustomizationFieldRepository)
    public productCustomizationFieldRepository: ProductCustomizationFieldRepository,
    @repository(ProductCustomizationOptionRepository)
    public productCustomizationOptionRepository: ProductCustomizationOptionRepository,
    @service(ProductVariantService)
    private readonly productVariantService: ProductVariantService,
    @repository(ProductPersonalWorkRepository)
    public productPersonalWorkRepository: ProductPersonalWorkRepository,
    @repository(TaxCategoryRepository)
    private readonly taxCategoryRepository: TaxCategoryRepository,
    @repository(ProductVariantOptionRepository)
    private readonly productVariantOptionRepository: ProductVariantOptionRepository,
  ) {}

  async create(product: Omit<ProductDto, 'id' | 'slug'>): Promise<Product> {
    const transaction =
      await this.productRepository.dataSource.beginTransaction(
        Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      );

    try {
      const {
        variants: productVariants,
        assets,
        options,
        facets,
        boxContents,
        details,
        disclaimer,
        moreInfo,
        returnPolicy,
        specifications,
        suitability,
        terms,
        uniqueness,
        personalWork,
        taxCategoryId,
        customizations,
        ...rest
      } = product;

      const taxCategory = await this.taxCategoryRepository.findById(
        taxCategoryId,
        {fields: {id: true, taxRate: true}},
      );

      /** Step 1: Create Product */
      const productDisplayId = await this.generateProductId();
      const newProduct = await this.productRepository.create(
        {
          ...rest,
          slug: nameToCode(rest.name),
          productId: productDisplayId,
          taxCategoryId,
        },
        {transaction},
      );
      const productId = newProduct.id as string;

      /** Step 2: Attach Additional Product Data */
      await Promise.all([
        this.attachAssets(productId, assets, transaction),
        this.attachFacets(productId, facets, transaction),
        this.attachBoxContents(productId, boxContents, transaction),
        this.attachDetails(productId, details, transaction),
        this.attachDisclaimers(productId, disclaimer, transaction),
        this.attachMoreInfos(productId, moreInfo, transaction),
        this.attachReturnPolicies(productId, returnPolicy, transaction),
        this.attachSpecifications(productId, specifications, transaction),
        this.attachSuitabilities(productId, suitability, transaction),
        this.attachTerms(productId, terms, transaction),
        this.attachUniquenesses(productId, uniqueness, transaction),
        this.attachProductCustomizations(
          productId,
          customizations ?? [],
          transaction,
        ),
        this.attachPersonalWork(productId, personalWork, transaction),
      ]);

      if (options?.length) {
        const {optionMap} = await this.createProductOptions(
          options,
          productId,
          transaction,
        );

        await this.createProductVariants(
          productId,
          productVariants,
          optionMap,
          options,
          productDisplayId,
          rest.name,
          taxCategory,
          product.featuredAssetId,
          transaction,
          customizations,
          terms,
          suitability,
          disclaimer,
          uniqueness,
          personalWork,
          returnPolicy,
          specifications,
          moreInfo,
          details,
          boxContents,
        );
      } else {
        await this.createDefaultVariant(
          newProduct,
          assets,
          taxCategoryId,
          transaction,
        );
      }

      await transaction.commit();
      return newProduct;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  private async createProductOptions(
    options: OptionDto[],
    productId: string,
    transaction: Transaction,
  ) {
    const optionGroupMap = new Map<number, string>(); // Map<groupId, DB ID>
    const optionMap = new Map<string, string>(); // Map<groupId:optionId, DB ID>
    const promiseArr = options.map(group =>
      this.createOptionGroup(
        {id: group.id, name: group.name, productId, unit: group.unit},
        optionGroupMap,
        transaction,
      ),
    );
    await Promise.all(promiseArr);
    const optionValuePromiseArr: Promise<void>[] = [];

    optionGroupMap.forEach((groupDbId, groupTempId) => {
      const values = options.find(op => op.id === groupTempId)?.values ?? [];
      values.forEach(val => {
        optionValuePromiseArr.push(
          this.createOptionValue(
            {
              name: val.name,
              id: val.id,
              groupId: groupDbId,
            },
            optionMap,
            groupTempId,
            transaction,
          ),
        );
      });
    });
    await Promise.all(optionValuePromiseArr);
    return {optionGroupMap, optionMap};
  }

  private async createOptionGroup(
    group: Group,
    optionGroupMap: Map<number, string>,
    transaction: Transaction,
  ): Promise<void> {
    const createdGroup = await this.productOptionGroupRepository.create(
      {
        name: group.name,
        code: nameToCode(group.name),
        productId: group.productId,
        unit: group.unit,
      },
      {transaction},
    );
    optionGroupMap.set(group.id, createdGroup.id ?? '');
  }

  private async createOptionValue(
    option: Value,
    optionMap: Map<string, string>,
    groupTempId: number,
    transaction: Transaction,
  ): Promise<void> {
    const createdOption = await this.productOptionRepository.create(
      new ProductOption({
        name: option.name,
        code: nameToCode(option.name),
        productOptionGroupId: option.groupId,
      }),
      {transaction},
    );
    optionMap.set(`${groupTempId}:${option.id}`, createdOption.id ?? '');
  }

  private async attachAssets(
    productId: string,
    assets?: string[],
    transaction?: Transaction,
  ) {
    if (assets?.length) {
      await this.productAssetRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productAssetRepository.createAll(
        assets.map(
          (asset, idx) =>
            new ProductAsset({productId, assetId: asset, position: idx + 1}),
        ),
        {transaction},
      );
    }
  }

  private async attachFacets(
    productId: string,
    facets?: string[],
    transaction?: Transaction,
  ) {
    if (facets?.length) {
      await this.productFacetValueRepository.deleteAll(
        {productId},
        {transaction},
      );
      await this.productFacetValueRepository.createAll(
        facets.map(
          facet => new ProductFacetValue({productId, facetValueId: facet}),
        ),
        {transaction},
      );
    }
  }

  private async attachBoxContents(
    productId: string,
    boxContents?: ProductBoxContent[],
    transaction?: Transaction,
  ) {
    if (boxContents?.length) {
      await this.productBoxContentRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productBoxContentRepository.createAll(
        boxContents.map(
          box =>
            new ProductBoxContent({
              productId,
              itemName: box.itemName,
              quantity: box.quantity,
            }),
        ),
        {transaction},
      );
    }
  }

  private async attachDetails(
    productId: string,
    details?: ProductDetail,
    transaction?: Transaction,
  ) {
    if (details) {
      await this.productDetailRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productDetailRepository.create(
        {
          productId,
          details: details.details,
        },
        {transaction},
      );
    }
  }

  private async attachDisclaimers(
    productId: string,
    disclaimer?: ProductDisclaimer,
    transaction?: Transaction,
  ) {
    if (disclaimer) {
      await this.productDisclaimerRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productDisclaimerRepository.create(
        {
          productId,
          disclaimer: disclaimer.disclaimer,
        },
        {transaction},
      );
    }
  }

  private async attachMoreInfos(
    productId: string,
    moreInfo?: ProductMoreInfo,
    transaction?: Transaction,
  ) {
    if (moreInfo) {
      await this.productMoreInfoRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productMoreInfoRepository.create(
        {productId, info: moreInfo.info},
        {transaction},
      );
    }
  }

  private async attachReturnPolicies(
    productId: string,
    policy?: ProductReturnPolicy,
    transaction?: Transaction,
  ) {
    if (policy) {
      await this.productReturnPolicyRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productReturnPolicyRepository.create(
        {
          productId,
          returnPolicy: policy.returnPolicy,
        },
        {transaction},
      );
    }
  }

  private async attachSpecifications(
    productId: string,
    specifications?: ProductSpecification[],
    transaction?: Transaction,
  ) {
    if (specifications?.length) {
      await this.productSpeicificationRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productSpeicificationRepository.createAll(
        specifications.map(spec => ({
          productId,
          name: spec.name,
          value: spec.value,
        })),
        {transaction},
      );
    }
  }

  private async attachSuitabilities(
    productId: string,
    suitability?: ProductSuitability,
    transaction?: Transaction,
  ) {
    if (suitability) {
      await this.productSuitabilityRepository.deleteAllHard(
        {productId},
        {transaction},
      );

      await this.productSuitabilityRepository.create(
        {
          productId,
          suitableFor: suitability.suitableFor,
        },
        {transaction},
      );
    }
  }

  private async attachPersonalWork(
    productId: string,
    personalWork?: ProductPersonalWork,
    transaction?: Transaction,
  ) {
    if (personalWork) {
      await this.productPersonalWorkRepository.deleteAllHard(
        {productId},
        {transaction},
      );

      await this.productPersonalWorkRepository.create(
        {
          productId,
          workLevel: personalWork.workLevel,
        },
        {
          transaction,
        },
      );
    }
  }

  private async attachTerms(
    productId: string,
    terms?: ProductTermsAndCondition,
    transaction?: Transaction,
  ) {
    if (terms) {
      await this.productTermsAndConditionRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productTermsAndConditionRepository.create(
        {productId, terms: terms.terms},
        {transaction},
      );
    }
  }

  private async attachUniquenesses(
    productId: string,
    uniqueness?: ProductUniqueness,
    transaction?: Transaction,
  ) {
    if (uniqueness) {
      await this.productUniquenessRepository.deleteAllHard(
        {productId},
        {transaction},
      );
      await this.productUniquenessRepository.create(
        {
          productId,
          uniqueness: uniqueness.uniqueness,
        },
        {transaction},
      );
    }
  }

  private async attachProductCustomizations(
    productId: string,
    customizations: ProductCustomizationDto[],
    transaction: Transaction,
  ) {
    if (customizations?.length) {
      await this.clearExsitingCustomization(productId, transaction);
      const promiseArr = customizations.map(customization =>
        this.createProductCustomization(productId, customization, transaction),
      );
      await Promise.all(promiseArr);
    }
  }

  private async clearExsitingCustomization(
    productId: string,
    transaction: Transaction,
  ) {
    const customizations =
      await this.productCustomizationFieldRepository.findAll({
        where: {productId},
        fields: {id: true},
      });
    await this.productCustomizationOptionRepository.deleteAllHard(
      {
        productCustomizationFieldId: {
          inq: customizations.map(item => item.id ?? ''),
        },
      },
      {transaction},
    );

    await this.productCustomizationFieldRepository.deleteAllHard(
      {productId},
      {transaction},
    );
  }

  private async createProductCustomization(
    productId: string,
    customization: ProductCustomizationDto,
    transaction: Transaction,
  ) {
    const {options, ...rest} = customization;
    const newCustomization =
      await this.productCustomizationFieldRepository.create(
        {...rest, productId},
        {
          transaction,
        },
      );
    if (options?.length) {
      await this.productCustomizationOptionRepository.createAll(
        options.map(option => ({
          productCustomizationFieldId: newCustomization.id,
          label: option.label,
          value: option.value,
        })),
        {transaction},
      );
    }
  }

  private async deleteVariantCustomizations(
    variantId: string,
    tx: Transaction,
  ): Promise<void> {
    const fields = await this.productCustomizationFieldRepository.find(
      {where: {productVariantId: variantId}},
      {transaction: tx},
    );

    const fieldIds = fields
      .map(f => f.id)
      .filter((id): id is string => Boolean(id));

    if (fieldIds.length) {
      // Delete all options first (FK dependency)
      await this.productCustomizationOptionRepository.deleteAll(
        {productCustomizationFieldId: {inq: fieldIds}},
        {transaction: tx},
      );

      // Delete all customization fields
      await this.productCustomizationFieldRepository.deleteAll(
        {id: {inq: fieldIds}},
        {transaction: tx},
      );
    }
  }
  private async attachVariantCustomizations(
    variantId: string,
    customs: ProductCustomizationDto[],
    tx: Transaction,
  ): Promise<void> {
    await this.deleteVariantCustomizations(variantId, tx);

    if (!customs?.length) return;

    const productId = await this.getVariantProductId(variantId, tx);

    const createdFields =
      await this.productCustomizationFieldRepository.createAll(
        customs.map(c => ({
          ...c,
          productId,
          productVariantId: variantId,
        })),
        {transaction: tx},
      );

    await Promise.all(
      createdFields.map((field, i) =>
        this.productCustomizationOptionRepository.createAll(
          customs[i].options?.map(o => ({
            productCustomizationFieldId: field.id,
            label: o.label,
            value: o.value,
          })) ?? [],
          {transaction: tx},
        ),
      ),
    );
  }

  private async replaceVariantEntity<T extends {productVariantId: string}>(
    repo: {
      deleteAll: Function;
      create: Function;
    },
    variantId: string,
    data: T | undefined,
    tx: Transaction,
  ): Promise<void> {
    await repo.deleteAll({productVariantId: variantId}, {transaction: tx});

    if (!data) return;

    const productId = await this.getVariantProductId(variantId, tx);

    await repo.create(
      {
        ...data,
        productVariantId: variantId,
        productId,
      },
      {transaction: tx},
    );
  }

  private async attachVariantTerms(
    variantId: string,
    terms: ProductTermsAndCondition | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productTermsAndConditionRepository,
      variantId,
      terms,
      tx,
    );
  }

  private async attachVariantSuitability(
    variantId: string,
    suitability: ProductSuitability | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productSuitabilityRepository,
      variantId,
      suitability,
      tx,
    );
  }

  private async attachVariantDisclaimer(
    variantId: string,
    disclaimer: ProductDisclaimer | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productDisclaimerRepository,
      variantId,
      disclaimer,
      tx,
    );
  }

  private async attachVariantDetails(
    variantId: string,
    details: ProductDetail | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productDetailRepository,
      variantId,
      details,
      tx,
    );
  }

  private async attachVariantMoreInfos(
    variantId: string,
    moreInfo: ProductMoreInfo | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productMoreInfoRepository,
      variantId,
      moreInfo,
      tx,
    );
  }

  private async attachVariantSpecifications(
    variantId: string,
    specifications: ProductSpecification[] | undefined,
    tx: Transaction,
  ): Promise<void> {
    if (!specifications?.length) return;

    const productId = await this.getVariantProductId(variantId, tx);

    await this.productSpeicificationRepository.deleteAll(
      {productVariantId: variantId},
      {transaction: tx},
    );

    await this.productSpeicificationRepository.createAll(
      specifications.map(spec => ({
        ...spec,
        productId,
        productVariantId: variantId,
      })),
      {transaction: tx},
    );
  }

  private async attachVariantBoxContents(
    variantId: string,
    boxContents: ProductBoxContent[] | undefined,
    tx: Transaction,
  ): Promise<void> {
    if (!boxContents?.length) return;

    const productId = await this.getVariantProductId(variantId, tx);

    await this.productBoxContentRepository.deleteAll(
      {productVariantId: variantId},
      {transaction: tx},
    );

    await this.productBoxContentRepository.createAll(
      boxContents.map(content => ({
        ...content,
        productId,
        productVariantId: variantId,
      })),
      {transaction: tx},
    );
  }

  private async attachVariantUniqueness(
    variantId: string,
    uniqueness: ProductUniqueness | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productUniquenessRepository,
      variantId,
      uniqueness,
      tx,
    );
  }

  private async attachVariantPersonalWork(
    variantId: string,
    personalWork: ProductPersonalWork | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productPersonalWorkRepository,
      variantId,
      personalWork,
      tx,
    );
  }

  private async attachVariantReturnPolicy(
    variantId: string,
    returnPolicy: ProductReturnPolicy | undefined,
    tx: Transaction,
  ) {
    await this.replaceVariantEntity(
      this.productReturnPolicyRepository,
      variantId,
      returnPolicy,
      tx,
    );
  }

  private async getVariantProductId(
    variantId: string,
    tx: Transaction,
  ): Promise<string> {
    const variant = await this.productVariantRepository.findById(
      variantId,
      {fields: {productId: true}},
      {transaction: tx},
    );

    if (!variant?.productId) {
      throw new Error('Product ID missing for variant');
    }

    return variant.productId;
  }

  private async createProductVariants(
    productId: string,
    variants: OptionVariantDto[] | undefined,
    optionMap: Map<string, string>,
    options: OptionDto[],
    displayId: string,
    productName: string,
    taxCategory: TaxCategory,
    featuredAssetId: string,
    tx: Transaction,
    customization?: ProductCustomizationDto[],
    terms?: ProductTermsAndCondition,
    suitability?: ProductSuitability,
    disclaimer?: ProductDisclaimer,
    uniqueness?: ProductUniqueness,
    personalWork?: ProductPersonalWork,
    returnPolicy?: ProductReturnPolicy,
    specifications?: ProductSpecification[],
    moreInfo?: ProductMoreInfo,
    details?: ProductDetail,
    boxContents?: ProductBoxContent[],
  ) {
    if (!variants?.length) return;
    const variantOptionMap = new Map<string, string>();

    await Promise.all(
      variants.map(v => {
        const priceWithTax =
          Number(v.price) +
          (Number(v.price) * (taxCategory.taxRate ?? 0)) / 100;
        return this.createProductVariantWithPrice(
          new ProductVariant({
            productId,
            name: `${productName} (${v.name})`,
            outOfStockThreshold: v.outOfStockThreshold,
            enabled: v.enabled,
            taxCategoryId: taxCategory.id,
            featuredAssetId,
          }),
          optionMap,
          variantOptionMap,
          v.mrp ?? v.price,
          priceWithTax,
          v.price,
          v.optionIds,
          options,
          displayId,
          tx,
          customization,
          terms,
          suitability,
          disclaimer,
          uniqueness,
          personalWork,
          returnPolicy,
          specifications,
          moreInfo,
          details,
          boxContents,
        );
      }),
    );

    await this.createProductVariantOptions(variantOptionMap, tx);
  }

  private async createDefaultVariant(
    product: Product,
    assets: string[],
    taxCategoryId: string,
    transaction: Transaction,
  ) {
    const sku = generateUniqueSKU(product.productId, ['default']);
    const variant = new ProductVariant({
      name: product.name,
      enabled: product.enabled,
      outOfStockThreshold: 1,
      productId: product.id,
      featuredAssetId: product.featuredAssetId,
      taxCategoryId,
    });
    const newVariant = await this.productVariantRepository.create(
      {...variant, sku},
      {
        transaction,
      },
    );
    await this.productVariantPriceRepository.create(
      {
        mrp: 0, // add default price as zero
        price: 0, // add default price as zero
        productVariantId: newVariant.id as string,
        currencyCode: DEFAULT_CURRENCY,
      },
      {transaction},
    );
    if (assets.length) {
      await this.productVariantService.attachAssets(
        newVariant.id ?? '',
        assets,
        transaction,
      );
    }
  }

  private async createProductVariantWithPrice(
    variant: ProductVariant,
    optionMap: Map<string, string>,
    variantOptionMap: Map<string, string>,
    mrp: number,
    priceWithoutTax: number,
    price: number,
    optionIds: number[],
    options: OptionDto[],
    displayId: string,
    tx: Transaction,
    customization?: ProductCustomizationDto[],
    terms?: ProductTermsAndCondition,
    suitability?: ProductSuitability,
    disclaimer?: ProductDisclaimer,
    uniqueness?: ProductUniqueness,
    personalWork?: ProductPersonalWork,
    returnPolicy?: ProductReturnPolicy,
    specifications?: ProductSpecification[],
    moreInfo?: ProductMoreInfo,
    details?: ProductDetail,
    boxContents?: ProductBoxContent[],
  ): Promise<void> {
    const sku = generateUniqueSKU(
      displayId,
      this.getOptionNames(options, optionIds),
    );
    const newVariant = await this.productVariantRepository.create(
      {...variant, sku},
      {transaction: tx},
    );
    if (!newVariant.id) {
      throw new Error('newVariant.id is undefined after creation');
    }

    await this.productVariantPriceRepository.create(
      {
        priceWithoutTax,
        price,
        mrp,
        productVariantId: newVariant.id as string,
        currencyCode: DEFAULT_CURRENCY,
      },
      {transaction: tx},
    );

    optionIds.forEach((optId, idx) => {
      const groupId = options[idx]?.id;
      const dbOpt = optionMap.get(`${groupId}:${optId}`);
      if (dbOpt) {
        variantOptionMap.set(
          `${dbOpt}|${newVariant.id}`,
          newVariant.id as string,
        );
      }
    });

    await Promise.all([
      this.attachVariantSpecifications(newVariant.id, specifications, tx),
      this.attachVariantMoreInfos(newVariant.id, moreInfo, tx),
      this.attachVariantDetails(newVariant.id, details, tx),
      this.attachVariantBoxContents(newVariant.id, boxContents, tx),
      this.attachVariantCustomizations(newVariant.id, customization ?? [], tx),
      this.attachVariantTerms(newVariant.id, terms, tx),
      this.attachVariantSuitability(newVariant.id, suitability, tx),
      this.attachVariantDisclaimer(newVariant.id, disclaimer, tx),
      this.attachVariantUniqueness(newVariant.id, uniqueness, tx),
      this.attachVariantPersonalWork(newVariant.id, personalWork, tx),
      this.attachVariantReturnPolicy(newVariant.id, returnPolicy, tx),
    ]);
  }

  private async createProductVariantOptions(
    variantOptionMap: Map<string, string>,
    transaction: Transaction,
  ): Promise<void> {
    const variantOptionEntries: ProductVariantOption[] = [];

    variantOptionMap.forEach((variantId, key) => {
      const [optionId] = key.split('|');
      variantOptionEntries.push(
        new ProductVariantOption({
          productVariantId: variantId,
          productOptionId: optionId,
        }),
      );
    });

    await this.productVariantOptionRepository.createAll(variantOptionEntries, {
      transaction,
    });
  }

  private getOptionNames(options: OptionDto[], optionIds: number[]): string[] {
    return options.reduce((result: string[], option) => {
      const matchedValues = option.values.filter(value =>
        optionIds.includes(value.id),
      );
      matchedValues.forEach(value => result.push(value.name));
      return result;
    }, []);
  }

  private async generateProductId(): Promise<string> {
    const lastCreatedEntry =
      await this.productRepository.findOneIncludeSoftDelete({
        order: ['created_on DESC'],
        fields: {productId: true},
      });

    if (!lastCreatedEntry) {
      return `${PREFIXES.PRODUCT}-000001`;
    }
    const sequenceNumber = Number(lastCreatedEntry.productId.split('-')[1]);
    if (!sequenceNumber || isNaN(sequenceNumber)) {
      throw HttpErrors.BadRequest('Failed to generate seller Id');
    }
    return `${PREFIXES.PRODUCT}-${(sequenceNumber + 1)
      .toString()
      .padStart(6, '0')}`;
  }

  async deleteProductById(productId: string): Promise<void> {
    const transaction =
      await this.productRepository.dataSource.beginTransaction(
        Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      );

    try {
      const options = {transaction};

      // Get all variants
      const variants = await this.productVariantRepository.find(
        {where: {productId}},
        options,
      );
      const variantIds = variants
        .map(v => v.id)
        .filter((id): id is string => Boolean(id));

      if (variantIds.length) {
        // Delete all variant-related entries
        await this.productVariantPriceRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productVariantOptionRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productCustomizationFieldRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productTermsAndConditionRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productSuitabilityRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productDisclaimerRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productReturnPolicyRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productUniquenessRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productPersonalWorkRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productBoxContentRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productMoreInfoRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );
        await this.productSpeicificationRepository.deleteAll(
          {productVariantId: {inq: variantIds}},
          options,
        );

        await this.productVariantRepository.deleteAll({productId}, options);
      }

      // Delete Option Groups & Options
      const optionGroups = await this.productOptionGroupRepository.find(
        {where: {productId}},
        options,
      );
      const optionGroupIds = optionGroups
        .map(g => g.id)
        .filter((id): id is string => Boolean(id));

      if (optionGroupIds.length) {
        await this.productOptionRepository.deleteAll(
          {productOptionGroupId: {inq: optionGroupIds}},
          options,
        );
      }
      await this.productOptionGroupRepository.deleteAll({productId}, options);

      // Delete Customization Fields & Options
      const customizationGroups =
        await this.productCustomizationFieldRepository.find(
          {where: {productId}},
          options,
        );
      const customizationGroupIds = customizationGroups
        .map(cg => cg.id)
        .filter((id): id is string => Boolean(id));

      if (customizationGroupIds.length) {
        await this.productCustomizationOptionRepository.deleteAll(
          {productCustomizationFieldId: {inq: customizationGroupIds}},
          options,
        );
      }
      await this.productCustomizationFieldRepository.deleteAll(
        {productId},
        options,
      );

      // Delete product-level related data
      await this.productAssetRepository.deleteAll({productId}, options);
      await this.productFacetValueRepository.deleteAll({productId}, options);
      await this.productSpeicificationRepository.deleteAll(
        {productId},
        options,
      );
      await this.productBoxContentRepository.deleteAll({productId}, options);
      await this.productMoreInfoRepository.deleteAll({productId}, options);
      await this.productCustomizationFieldRepository.deleteAll(
        {productId},
        options,
      );
      await this.productTermsAndConditionRepository.deleteAll(
        {productId},
        options,
      );
      await this.productSuitabilityRepository.deleteAll({productId}, options);
      await this.productDisclaimerRepository.deleteAll({productId}, options);
      await this.productReturnPolicyRepository.deleteAll({productId}, options);
      await this.productUniquenessRepository.deleteAll({productId}, options);
      await this.productPersonalWorkRepository.deleteAll({productId}, options);

      // Finally delete the product itself
      await this.productRepository.deleteById(productId, options);

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }

  async updateProduct(
    productId: string,
    product: Partial<ProductDto>,
  ): Promise<void> {
    const transaction =
      await this.productRepository.dataSource.beginTransaction(
        Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      );

    try {
      const {
        assets,
        facets,
        boxContents,
        details,
        disclaimer,
        moreInfo,
        returnPolicy,
        specifications,
        suitability,
        terms,
        uniqueness,
        personalWork,
        customizations,
        ...rest
      } = product;

      if (rest.name) {
        rest.slug = nameToCode(rest.name);
      }

      /** Step 1: Update Product */
      await this.productRepository.updateById(
        productId,
        {...rest},
        {transaction},
      );

      /** Step 2: Attach Product-Level Additional Data */
      await Promise.all([
        this.attachAssets(productId, assets, transaction),
        this.attachFacets(productId, facets, transaction),
        this.attachBoxContents(productId, boxContents, transaction),
        this.attachDetails(productId, details, transaction),
        this.attachDisclaimers(productId, disclaimer, transaction),
        this.attachMoreInfos(productId, moreInfo, transaction),
        this.attachReturnPolicies(productId, returnPolicy, transaction),
        this.attachSpecifications(productId, specifications, transaction),
        this.attachSuitabilities(productId, suitability, transaction),
        this.attachTerms(productId, terms, transaction),
        this.attachUniquenesses(productId, uniqueness, transaction),
        this.attachProductCustomizations(
          productId,
          customizations ?? [],
          transaction,
        ),
        this.attachPersonalWork(productId, personalWork, transaction),
      ]);

      /** Step 3: Attach Shared Fields to Product Variants */
      const variants = await this.productVariantRepository.find(
        {where: {productId}},
        {transaction},
      );

      await Promise.all(
        variants.map(variant => {
          if (!variant.id) throw new Error('Variant ID is missing');

          return Promise.all([
            this.attachVariantCustomizations(
              variant.id,
              customizations ?? [],
              transaction,
            ),
            this.attachVariantTerms(variant.id, terms, transaction),
            this.attachVariantSuitability(variant.id, suitability, transaction),
            this.attachVariantDisclaimer(variant.id, disclaimer, transaction),
            this.attachVariantUniqueness(variant.id, uniqueness, transaction),
            this.attachVariantPersonalWork(
              variant.id,
              personalWork,
              transaction,
            ),
            this.attachVariantReturnPolicy(
              variant.id,
              returnPolicy,
              transaction,
            ),
          ]);
        }),
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async createOptionGroupByProductId(
    productId: string,
    productOption: Omit<ProductOptionGroupDto, 'id' | 'code' | 'productId'>,
  ): Promise<void> {
    const {options, ...rest} = productOption;
    const transaction =
      await this.productOptionGroupRepository.dataSource.beginTransaction(
        Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      );
    try {
      const optionGroup = await this.productOptionGroupRepository.create(
        {
          productId,
          code: nameToCode(rest.name),
          ...rest,
        },
        {transaction},
      );
      const optionValues = options.map(
        item =>
          new ProductOption({
            ...item,
            code: nameToCode(item.name),
            productOptionGroupId: optionGroup.id,
          }),
      );
      await this.productOptionRepository.createAll(optionValues, {transaction});
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async updateOptionGroupByProductId(
    productId: string,
    groupId: string,
    productOption: Partial<ProductOptionGroupDto>,
  ): Promise<void> {
    const {options, ...rest} = productOption;
    const transaction =
      await this.productOptionGroupRepository.dataSource.beginTransaction(
        Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      );
    try {
      if (rest.name) {
        rest.code = nameToCode(rest.name);
      }
      await this.productOptionGroupRepository.updateById(
        groupId,
        {
          productId,
          ...rest,
        },
        {transaction},
      );
      if (options?.length) {
        const existingOptions = await this.productOptionRepository.find({
          where: {productOptionGroupId: groupId},
          fields: {id: true},
        });
        const newOptions = options.filter(op => !op.id);
        const removedOptions = existingOptions.filter(ep =>
          options.some(op => op.id === ep.id),
        );
        const updatedOptions = options.filter(op => op.id);
        if (removedOptions.length) {
          await this.productVariantOptionRepository.deleteAllHard(
            {
              productOptionId: {inq: removedOptions.map(item => item.id ?? '')},
            },
            {transaction},
          );
          await this.productOptionRepository.deleteAllHard(
            {
              productOptionGroupId: groupId,
            },
            {transaction},
          );
        }
        if (newOptions) {
          const optionValues = newOptions.map(
            item =>
              new ProductOption({
                name: item.name,
                code: nameToCode(item.name),
                productOptionGroupId: groupId,
              }),
          );
          await this.productOptionRepository.createAll(optionValues, {
            transaction,
          });
        }
        if (updatedOptions.length) {
          await Promise.all(
            updatedOptions.map(op =>
              this.productOptionRepository.updateById(
                op.id,
                {name: op.name, code: nameToCode(op.name)},
                {
                  transaction,
                },
              ),
            ),
          );
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async bulkStatusUpdate(bulkUpdate: BulkStatusUpdateDto): Promise<void> {
    const {productIds, status, rejectedReason} = bulkUpdate;

    // Validate that rejectedReason is provided when status is REJECTED
    if (
      status === ProductStatus.REJECTED &&
      (!rejectedReason || !rejectedReason.trim())
    ) {
      throw new HttpErrors.BadRequest(
        'Rejected reason is required when rejecting products',
      );
    }

    const updateData: Partial<Product> = {status};
    if (rejectedReason) {
      updateData.rejectedReason = rejectedReason;
    }

    await this.productRepository.updateAll(updateData, {id: {inq: productIds}});
  }

  async updateProductStatus(
    id: string,
    statusUpdate: SingleStatusUpdateDto,
  ): Promise<void> {
    const {status, rejectedReason} = statusUpdate;

    // Validate that rejectedReason is provided when status is REJECTED
    if (
      status === ProductStatus.REJECTED &&
      (!rejectedReason || !rejectedReason.trim())
    ) {
      throw new HttpErrors.BadRequest(
        'Rejected reason is required when rejecting products',
      );
    }

    const updateData: Partial<Product> = {status};
    if (rejectedReason) {
      updateData.rejectedReason = rejectedReason;
    }

    await this.productRepository.updateById(id, updateData);
  }
}
