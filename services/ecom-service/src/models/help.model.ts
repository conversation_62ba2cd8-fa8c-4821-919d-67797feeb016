import {SupportStatus, SupportVisibility} from '@local/core';
import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({settings: {strict: false}, name: 'helps'})
export class Help extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  question: string;

  @property({
    type: 'string',
    required: true,
  })
  answer: string;

  @property({
    type: 'string',
    required: true,
  })
  category: string;

  @property({
    type: 'number',
    default: 0,
  })
  visibility: SupportVisibility;

  @property({
    type: 'number',
    default: 0,
  })
  status: SupportStatus;

  constructor(data?: Partial<Help>) {
    super(data);
  }
}

export interface HelpRelations {
  // define navigational relations here
}

export type HelpWithRelations = Help & HelpRelations;
