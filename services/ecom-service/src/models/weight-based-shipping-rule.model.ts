import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';

@model({
  name: 'weight_based_shipping_rules',
})
export class WeightBasedShippingRule extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'number',
    required: true,
    name: 'min_weight',
  })
  minWeight: number;

  @property({
    type: 'number',
    name: 'max_weight',
  })
  maxWeight?: number;

  @property({
    type: 'number',
    required: true,
  })
  charge: number;

  @belongsTo(
    () => SellerShippingProfile,
    {keyTo: 'id'},
    {name: 'shipping_profile_id'},
  )
  shippingProfileId: string;

  constructor(data?: Partial<WeightBasedShippingRule>) {
    super(data);
  }
}

export interface WeightBasedShippingRuleRelations {
  shippingProfile?: SellerShippingProfile;
}

export type WeightBasedShippingRuleWithRelations = WeightBasedShippingRule &
  WeightBasedShippingRuleRelations;
