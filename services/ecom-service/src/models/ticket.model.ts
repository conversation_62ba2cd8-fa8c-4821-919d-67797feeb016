import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {TicketStatus, TicketCategory} from '@local/core';

@model({settings: {strict: false}, name: 'tickets'})
export class Ticket extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  category: TicketCategory;

  @property({
    type: 'string',
    required: true,
  })
  title: string;

  @property({
    type: 'string',
    required: true,
  })
  description: string;

  @property({
    type: 'number',
    default: 0,
  })
  priority: number;

  @property({
    type: 'string',
    required: true,
  })
  shortCode: string;

  @property({
    type: 'string',
    required: true,
  })
  status: TicketStatus;

  @property({
    type: 'string',
  })
  assignee?: string;

  @property({
    type: 'string',
  })
  attachments?: string;

  constructor(data?: Partial<Ticket>) {
    super(data);
  }
}

export interface TicketRelations {
  // define navigational relations here
}

export type TicketWithRelations = Ticket & TicketRelations;
