import {model, property, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ChatMessage} from './chat-message.model';
import {ChatStatus} from '@local/core';

@model({name: 'chats'})
export class Chat extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @property({
    type: 'string',
    required: true,
    name: 'seller_id',
  })
  sellerId: string;

  @property({
    type: 'string',
    jsonSchema: {
      enum: Object.values(ChatStatus),
    },
    default: ChatStatus.ACTIVE,
  })
  status?: string;

  @hasMany(() => ChatMessage, {keyTo: 'chatId'})
  messages: ChatMessage[];

  constructor(data?: Partial<Chat>) {
    super(data);
  }
}

export interface ChatRelations {
  // describe navigational properties here
}

export type ChatWithRelations = Chat & ChatRelations;
