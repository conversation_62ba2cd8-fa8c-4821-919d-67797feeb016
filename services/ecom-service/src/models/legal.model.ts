import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {LegalCategory, Legals, LegalVisibility} from '@local/core';

@model({name: 'legals'})
export class Legal extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({type: 'string', required: true})
  data: string;

  @property({type: 'string', required: true})
  category: LegalCategory;

  @property({
    type: 'string',
    required: true,
  })
  type: Legals;

  @property({
    type: 'number',
  })
  visibility: LegalVisibility;
}

export interface LegalRelations {}
export type LegalWithRelations = Legal & LegalRelations;
