import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {Asset} from './asset.model';
import {UserModifiableEntity} from '@sourceloop/core';
import {TaxCategory} from './tax-category.model';
import {CollectionStatus} from '../enums/collection-status.enum';

@model({name: 'collections'})
export class Collection extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'is_root',
  })
  isRoot?: boolean;

  @property({
    type: 'number',
    default: 0,
    name: 'position',
  })
  position?: number;

  @property({
    type: 'boolean',
    default: false,
    name: 'is_private',
  })
  isPrivate?: boolean;

  @property({
    type: 'string',
    required: false,
    default: CollectionStatus.ACTIVE,
  })
  status: CollectionStatus;

  @property({
    type: 'string',
  })
  filters?: string;

  @property({
    type: 'boolean',
    default: true,
    name: 'inherit_filters',
  })
  inheritFilters?: boolean;

  @belongsTo(
    () => Collection,
    {keyTo: 'id'},
    {required: false, name: 'parent_id'},
  )
  parentId: string;

  @belongsTo(
    () => Asset,
    {keyTo: 'id'},
    {
      required: false,
      name: 'featured_asset_id',
    },
  )
  featuredAssetId?: string;

  @belongsTo(
    () => TaxCategory,
    {keyTo: 'id'},
    {
      required: false,
      name: 'tax_category_id',
    },
  )
  taxCategoryId?: string;

  @hasMany(() => Collection, {keyTo: 'parentId'})
  childrens: Collection[];

  constructor(data?: Partial<Collection>) {
    super(data);
  }
}

export interface CollectionRelations {
  featuredAsset: Asset;
  parent: Collection;
}

export type CollectionWithRelations = Collection & CollectionRelations;
