import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ReferralStatus, ReferralType} from '@local/core';

@model({settings: {strict: false}, name: 'referrals'})
export class Referral extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'referral_code',
  })
  referralCode: string;

  @property({
    type: 'string',
    required: true,
  })
  type: ReferralType;

  @property({
    type: 'string',
    required: true,
  })
  status: ReferralStatus;

  @property({
    type: 'string',
    required: true,
    name: 'referrer_id',
  })
  referrerId: string;

  @property({
    type: 'string',
    name: 'referred_id',
  })
  referredId?: string;

  constructor(data?: Partial<Referral>) {
    super(data);
  }
}

export interface ReferralRelations {
  // describe navigational properties here
}

export type ReferralWithRelations = Referral & ReferralRelations;
