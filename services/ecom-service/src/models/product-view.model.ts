import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ProductVariant} from './product-variant.model';

@model({name: 'product_views'})
export class ProductView extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'user_id',
  })
  userId: string;

  @property({
    type: 'date',
    required: true,
    name: 'viewed_at',
  })
  viewedAt: Date;

  @belongsTo(() => ProductVariant, {keyTo: 'id'}, {name: 'product_variant_id'})
  productVariantId: string;

  constructor(data?: Partial<ProductView>) {
    super(data);
  }
}

export interface ProductViewRelations {
  productVariant?: ProductVariant;
}

export type ProductViewWithRelations = ProductView & ProductViewRelations;
