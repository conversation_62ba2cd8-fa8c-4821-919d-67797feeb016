import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Chat} from './chat.model';
import {SenderType} from '@local/core';

@model({name: 'chat_messages'})
export class ChatMessage extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'sender_id',
  })
  senderId: string;

  @property({
    type: 'string',
    required: true,
    name: 'sender_type',
    jsonSchema: {
      enum: Object.values(SenderType),
    },
  })
  senderType: string;

  @property({
    type: 'string',
    required: true,
  })
  message: string;

  @property({
    type: 'boolean',
    default: false,
  })
  read?: boolean;

  @belongsTo(() => Chat, {keyTo: 'id'}, {name: 'chat_id'})
  chatId: string;

  constructor(data?: Partial<ChatMessage>) {
    super(data);
  }
}

export interface ChatMessageRelations {
  // describe navigational properties here
}

export type ChatMessageWithRelations = ChatMessage & ChatMessageRelations;
