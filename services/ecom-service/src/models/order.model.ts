import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {Cart} from './cart.model';
import {OrderStatus} from '@local/core';
import {UserModifiableEntity} from '@sourceloop/core';
import {OrderLineItem} from './order-line-item.model';
import {PromoCode} from './promo-code.model';
import {Address} from './address.model';
import {DiscountCondition} from './discount-condition.model';

@model({name: 'orders'})
export class Order extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @property({
    type: 'string',
    required: false,
    name: 'order_id',
  })
  orderId: string;

  @property({
    type: 'string',
    required: false,
    name: 'order_reference_id',
  })
  orderReferenceId: string;

  @property({
    type: 'number',
    required: true,
    name: 'discount_amount',
  })
  discountAmount: number;

  @property({
    type: 'number',
    required: true,
    name: 'payable_amount',
  })
  payableAmount: number;

  @property({
    type: 'number',
    required: true,
    name: 'total_amount',
  })
  totalAmount: number;

  @property({
    type: 'string',
    required: true,
  })
  currency: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(OrderStatus),
    },
  })
  status: string;

  @property({
    type: 'string',
    name: 'invoice_id',
  })
  invoiceId?: string;

  @property({
    type: 'number',
    name: 'ecom_duke_coins_used',
    default: 0,
  })
  ecomDukeCoinsUsed?: number;

  @property({
    type: 'string',
    name: 'invoice_url',
  })
  invoiceUrl?: string;

  @belongsTo(() => Cart, {keyTo: 'id'}, {name: 'cart_id'})
  cartId: string;

  @hasMany(() => OrderLineItem, {keyTo: 'orderId'})
  orderLineItems: OrderLineItem[];

  @belongsTo(() => PromoCode, {keyTo: 'id'}, {name: 'promo_code_id'})
  promoCodeId: string;

  @belongsTo(() => Address, {keyTo: 'id'}, {name: 'shipping_address_id'})
  shippingAddressId: string;

  @belongsTo(() => Address, {keyTo: 'id'}, {name: 'billing_address_id'})
  billingAddressId: string;

  @belongsTo(
    () => DiscountCondition,
    {keyTo: 'id'},
    {name: 'discount_condition_id'},
  )
  discountConditionId: string;

  constructor(data?: Partial<Order>) {
    super(data);
  }
}

export interface OrderRelations {
  // describe navigational properties here
}

export type OrderWithRelations = Order & OrderRelations;
