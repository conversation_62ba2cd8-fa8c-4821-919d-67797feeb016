import {model, property} from '@loopback/repository';
import {SellerShippingCharge} from '../seller-shipping-charge.model';
import {WeightBasedShippingRule} from '../weight-based-shipping-rule.model';
import {ProductShippingCharge} from '../product-shipping-charge.model';

@model()
export class SellerShippingProfileDto {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  isDefault?: boolean;

  @property({
    type: 'boolean',
    default: true,
  })
  isActive?: boolean;

  @property({
    type: 'string',
    required: true,
  })
  sellerId: string;

  @property({
    type: 'string',
    required: true,
  })
  shippingMethodId: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  shippingCharges?: Partial<SellerShippingCharge>[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  weightBasedRules?: Partial<WeightBasedShippingRule>[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  productShippingCharges?: Partial<ProductShippingCharge>[];

  constructor(data?: Partial<SellerShippingProfileDto>) {
    Object.assign(this, data);
  }
}
