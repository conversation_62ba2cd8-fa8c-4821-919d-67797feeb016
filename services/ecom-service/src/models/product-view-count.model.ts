import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ProductVariant} from './product-variant.model';

@model({name: 'product_view_counts'})
export class ProductViewCount extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'number',
    required: true,
    default: 1,
    name: 'view_count',
  })
  viewCount: number;

  @belongsTo(() => ProductVariant, {keyTo: 'id'}, {name: 'product_variant_id'})
  productVariantId: string;

  constructor(data?: Partial<ProductViewCount>) {
    super(data);
  }
}

export interface ProductViewCountRelations {
  productVariant?: ProductVariant;
}

export type ProductViewCountWithRelations = ProductViewCount &
  ProductViewCountRelations;
