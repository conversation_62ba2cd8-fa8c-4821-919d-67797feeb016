import {SupportStatus, SupportVisibility} from '@local/core';
import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'supports'})
export class Support extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  supportEmail: string;

  @property({
    type: 'string',
    required: true,
  })
  supportPhone: string;

  @property({
    type: 'number',
    required: false,
  })
  status?: SupportStatus;

  @property({
    type: 'number',
    required: false,
  })
  visibility?: SupportVisibility;

  constructor(data?: Partial<Support>) {
    super(data);
  }
}

export interface SupportRelations {}

export type SupportWithRelations = Support & SupportRelations;
