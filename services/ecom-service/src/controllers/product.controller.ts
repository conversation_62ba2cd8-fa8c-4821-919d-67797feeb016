import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
} from '@loopback/rest';
import {
  Product,
  ProductDto,
  ProductOptionGroupDto,
  ProductVariant,
  BulkStatusUpdateDto,
  SingleStatusUpdateDto,
} from '../models';
import {ProductRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {
  ProductService,
  ProductViewService,
  TopSellingProductsService,
} from '../services';
import {service} from '@loopback/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
const basePath = '/products';
export class ProductController {
  constructor(
    @repository(ProductRepository)
    public productRepository: ProductRepository,
    @service(ProductService)
    private readonly productService: ProductService,
    @service(ProductViewService)
    private readonly productViewService: ProductViewService,
    @service(TopSellingProductsService)
    private readonly topSellingProductsService: TopSellingProductsService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Product)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductDto, {
            title: 'NewProduct',
            exclude: ['id', 'slug'],
          }),
        },
      },
    })
    product: Omit<ProductDto, 'id' | 'slug'>,
  ): Promise<Product> {
    return this.productService.create(product);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Product) where?: Where<Product>): Promise<Count> {
    return this.productRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Product model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Product, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Product) filter?: Filter<Product>,
  ): Promise<Product[]> {
    return this.productRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product PATCH success count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Product, {partial: true}),
        },
      },
    })
    product: Product,
    @param.where(Product) where?: Where<Product>,
  ): Promise<Count> {
    return this.productRepository.updateAll(product, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Product, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Product, {exclude: 'where'})
    filter?: FilterExcludingWhere<Product>,
  ): Promise<Product> {
    return this.productRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductDto, {partial: true}),
        },
      },
    })
    product: Partial<ProductDto>,
  ): Promise<void> {
    await this.productService.updateProduct(id, product);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @put(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() product: Product,
  ): Promise<void> {
    await this.productRepository.replaceById(id, product);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteProduct]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.productService.deleteProductById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @post(`${basePath}/{id}/option-group`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product option creation success',
      },
    },
  })
  async createProductOptionGroup(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductOptionGroupDto, {
            title: 'ProductOptions',
            exclude: ['id', 'code', 'productId'],
          }),
        },
      },
    })
    productOption: Omit<ProductOptionGroupDto, 'id' | 'code' | 'productId'>,
  ): Promise<void> {
    return this.productService.createOptionGroupByProductId(id, productOption);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @patch(`${basePath}/{id}/option-group/{groupId}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product option update success',
      },
    },
  })
  async updateProductOptionGroup(
    @param.path.string('id') id: string,
    @param.path.string('groupId') groupId: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductOptionGroupDto, {
            title: 'ProductOptions Update',
            partial: true,
          }),
        },
      },
    })
    productOption: Partial<ProductOptionGroupDto>,
  ): Promise<void> {
    return this.productService.updateOptionGroupByProductId(
      id,
      groupId,
      productOption,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/recently-viewed`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Recently viewed products',
      },
    },
  })
  async getRecentlyViewedProducts(
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]> {
    return this.productViewService.getRecentlyViewedProducts(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get('/products/most-viewed', {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Most viewed products',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async getMostViewedProducts(
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]> {
    return this.productViewService.getMostViewedProducts(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get('/products/top-selling', {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Most viewed products',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async getTopSellingProducts(
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]> {
    return this.topSellingProductsService.getTopSellingProductVariants(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/bulk-status-update`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Bulk product status update success',
      },
    },
  })
  async bulkStatusUpdate(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BulkStatusUpdateDto),
        },
      },
    })
    bulkUpdate: BulkStatusUpdateDto,
  ): Promise<void> {
    await this.productService.bulkStatusUpdate(bulkUpdate);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/{id}/status`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product status update success',
      },
    },
  })
  async updateProductStatus(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SingleStatusUpdateDto),
        },
      },
    })
    statusUpdate: SingleStatusUpdateDto,
  ): Promise<void> {
    await this.productService.updateProductStatus(id, statusUpdate);
  }
}
