import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
} from '@loopback/rest';
import {ReferralProgram} from '../models';
import {ReferralProgramRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';

const basePath = '/referral-programs';

export class ReferralProgramController {
  constructor(
    @repository(ReferralProgramRepository)
    public referralProgramRepository: ReferralProgramRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateReferralProgram]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ReferralProgram)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ReferralProgram, {
            title: 'NewReferralProgram',
            exclude: ['id'],
          }),
        },
      },
    })
    referralProgram: Omit<ReferralProgram, 'id'>,
  ): Promise<ReferralProgram> {
    return this.referralProgramRepository.create(referralProgram);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferralProgram]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(ReferralProgram) where?: Where<ReferralProgram>,
  ): Promise<Count> {
    return this.referralProgramRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferralProgram]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ReferralProgram model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ReferralProgram, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(ReferralProgram) filter?: Filter<ReferralProgram>,
  ): Promise<ReferralProgram[]> {
    return this.referralProgramRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateReferralProgram]})
  @patch(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram PATCH success count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ReferralProgram, {partial: true}),
        },
      },
    })
    referralProgram: Partial<ReferralProgram>,
    @param.where(ReferralProgram) where?: Where<ReferralProgram>,
  ): Promise<Count> {
    return this.referralProgramRepository.updateAll(referralProgram, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferralProgram]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(ReferralProgram, {
              includeRelations: true,
            }),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReferralProgram, {exclude: 'where'})
    filter?: FilterExcludingWhere<ReferralProgram>,
  ): Promise<ReferralProgram> {
    return this.referralProgramRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateReferralProgram]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ReferralProgram PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ReferralProgram, {partial: true}),
        },
      },
    })
    referralProgram: Partial<ReferralProgram>,
  ): Promise<void> {
    await this.referralProgramRepository.updateById(id, referralProgram);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateReferralProgram]})
  @put(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ReferralProgram PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() referralProgram: ReferralProgram,
  ): Promise<void> {
    await this.referralProgramRepository.replaceById(id, referralProgram);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteReferralProgram]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ReferralProgram DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.referralProgramRepository.deleteById(id);
  }
}
