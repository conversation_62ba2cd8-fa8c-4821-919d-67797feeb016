import {
  injectable,
  /* inject, */ BindingScope,
  inject,
  Getter,
  service,
} from '@loopback/core';
import {IsolationLevel, repository} from '@loopback/repository';
import {CustomerRepository} from '../repositories';
import {CustomerStatus, PREFIXES} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {
  Customer,
  IAuthUserWithTenant,
  CustomerStatusUpdateDto,
} from '../models';
import {Transaction} from '@loopback/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {User} from '@sourceloop/authentication-service';
import {
  AuthClientRepository,
  AuthServiceBindings,
  PasswordHashingFn,
  RoleRepository,
  TenantRepository,
  UserLevelPermissionRepository,
  UserRepository,
  UserTenantRepository,
} from '@sourceloop/authentication-service';
import {NotificationHelperService} from './notification-helper.service';
import {RoleType} from '../enums/role-type.enum';
import {RoleTypes, UserStatus} from '@sourceloop/core';

@injectable({scope: BindingScope.TRANSIENT})
export class CustomerService {
  constructor(
    @repository(CustomerRepository)
    private readonly customerRepository: CustomerRepository,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @repository(UserRepository) private readonly userRepository: UserRepository,
    @repository(AuthClientRepository)
    private readonly authClientRepository: AuthClientRepository,
    @repository(TenantRepository)
    private readonly tenantRepository: TenantRepository,
    @inject(AuthServiceBindings.PASSWORD_HASHING_PROVIDER)
    private readonly passwordHashingFn: PasswordHashingFn,
    @repository(RoleRepository)
    private readonly roleRepository: RoleRepository,
    @repository(UserLevelPermissionRepository)
    private readonly userLevelPermissionRepository: UserLevelPermissionRepository,
    @repository(UserTenantRepository)
    private readonly userTenantRepository: UserTenantRepository,
    @service(NotificationHelperService)
    private readonly notificationHelper: NotificationHelperService,
  ) {}

  async generateCustomerId(): Promise<string> {
    const lastCreatedEntry =
      await this.customerRepository.findOneIncludeSoftDelete({
        order: ['created_on DESC'],
        fields: {customerId: true},
      });

    if (!lastCreatedEntry) {
      return `${PREFIXES.CUSTOMER}-000001`;
    }
    const sequenceNumber = Number(lastCreatedEntry.customerId.split('-')[1]);
    if (!sequenceNumber || isNaN(sequenceNumber)) {
      throw HttpErrors.BadRequest('Failed to generate customer Id');
    }
    return `${PREFIXES.CUSTOMER}-${(sequenceNumber + 1)
      .toString()
      .padStart(6, '0')}`;
  }

  async createNewCustomer(
    customer: Partial<Omit<Customer, 'id, customerId'>>,
    transaction?: Transaction,
  ): Promise<Customer> {
    const user = await this.getCurrentUser();
    const customerId = await this.generateCustomerId();
    const newSeller = new Customer({
      emailVerified: customer.emailVerified ?? false,
      phoneVerified: customer.phoneVerified ?? false,
      status: customer.status ?? CustomerStatus.ACTIVE,
      userTenantId: customer.userTenantId ?? user.userTenantId,
      customerId,
    });
    const createdSeller = await this.customerRepository.create(newSeller, {
      transaction,
    });
    return createdSeller;
  }

  async createCustomer(customerDto: User): Promise<Customer> {
    const tx = await this.userRepository.dataSource.beginTransaction(
      IsolationLevel.READ_COMMITTED,
    );
    const {
      firstName,
      lastName,
      email,
      phone,
      gender,
      dob,
      designation,
      photoUrl,
    } = customerDto;
    const password = process.env.USER_TEMP_PASSWORD;

    const [tenant, role, client] = await Promise.all([
      this.tenantRepository.findOne({
        where: {key: 'ecomdukes'},
      }),
      this.roleRepository.findOne({
        where: {roleType: RoleType.CUSTOMER as unknown as RoleTypes},
      }),
      this.authClientRepository.findOne({
        where: {clientId: 'email_password_client'},
      }),
    ]);

    if (!client) {
      throw new HttpErrors.Unauthorized('Invalid client ID');
    }

    const userExists = await this.userRepository.findOne({
      where: {
        or: [{username: email?.toLowerCase()}, {email: email?.toLowerCase()}],
      },
    });
    if (userExists) {
      throw new HttpErrors.Conflict('Email is already registered');
    }
    try {
      const user = await this.userRepository.createWithoutPassword(
        {
          firstName,
          lastName,
          phone: phone,
          username: email?.toLowerCase(),
          email: email?.toLowerCase(),
          defaultTenantId: tenant?.id,
          authClientIds: `{${client?.id}}`,
          gender,
          designation,
          photoUrl,
          dob,
        },
        {transaction: tx},
      );
      const hashedPassword = await this.passwordHashingFn(password);
      await this.userRepository.credentials(user.id).create(
        {
          userId: user.id,
          authProvider: 'internal',
          password: hashedPassword,
        },
        {transaction: tx},
      );

      const userTenant = await this.userRepository.userTenants(user.id).create(
        {
          userId: user.id,
          tenantId: tenant?.id,
          status: UserStatus.ACTIVE,
          roleId: role?.id,
        },
        {transaction: tx},
      );

      if (!userTenant?.id) {
        await tx.rollback();
        throw new HttpErrors.InternalServerError('userTenant creation failed');
      }

      const customer = await this.customerRepository.create(
        {
          userTenantId: userTenant.id,
          customerId: await this.generateCustomerId(),
          emailVerified: false,
          phoneVerified: false,
          status: CustomerStatus.ACTIVE,
        },
        {transaction: tx},
      );

      await tx.commit();
      return customer;
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }

  async updateCustomer(
    customerId: string,
    customerDto: Partial<User> & {status?: CustomerStatus},
  ): Promise<void> {
    const tx = await this.userRepository.dataSource.beginTransaction(
      IsolationLevel.READ_COMMITTED,
    );

    try {
      const customer = await this.customerRepository.findOne({
        where: {id: customerId},
      });

      if (!customer) {
        throw new HttpErrors.NotFound('Customer not found');
      }

      const userTenant = await this.userTenantRepository.findOne({
        where: {id: customer.userTenantId},
      });

      if (!userTenant) {
        throw new HttpErrors.NotFound('UserTenant not found');
      }

      const user = await this.userRepository.findById(userTenant.userId);
      if (!user) {
        throw new HttpErrors.NotFound('User not found');
      }

      if (!user) {
        throw new HttpErrors.NotFound('User not found');
      }

      const {
        firstName,
        lastName,
        email,
        phone,
        gender,
        dob,
        designation,
        photoUrl,
        status,
      } = customerDto;
      await this.userRepository.updateAll(
        {
          firstName: firstName ?? user.firstName,
          lastName: lastName ?? user.lastName,
          email: email?.toLowerCase() ?? user.email,
          username: email?.toLowerCase() ?? user.username,
          phone: phone ?? user.phone,
          gender: gender ?? user.gender,
          designation: designation ?? user.designation,
          photoUrl: photoUrl ?? user.photoUrl,
          dob: dob ?? user.dob,
        },
        {id: user.id},
        {transaction: tx},
      );

      if (status) {
        await this.customerRepository.updateAll(
          {status},
          {id: customer.id},
          {transaction: tx},
        );
      }

      await tx.commit();
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }

  async deleteCustomer(customerId: string): Promise<void> {
    const tx = await this.userRepository.dataSource.beginTransaction(
      IsolationLevel.READ_COMMITTED,
    );

    try {
      const customer = await this.customerRepository.findOne({
        where: {id: customerId},
      });

      if (!customer) {
        throw new HttpErrors.NotFound('Customer not found');
      }

      const userTenant = await this.userTenantRepository.findById(
        customer.userTenantId,
      );

      if (!userTenant) {
        throw new HttpErrors.NotFound('User tenant record not found');
      }

      const userId = userTenant.userId;

      await Promise.all([
        this.userRepository.credentials(userId).delete({transaction: tx}),
        this.userLevelPermissionRepository.deleteAll(
          {userTenantId: userTenant.id},
          {transaction: tx},
        ),
        this.customerRepository.deleteById(customer.id, {transaction: tx}),
        this.userTenantRepository.deleteById(userTenant.id, {transaction: tx}),
        this.userRepository.deleteById(userId, {transaction: tx}),
      ]);

      await tx.commit();
    } catch (error) {
      await tx.rollback();
      console.error('Error in deleteCustomer:', error);
      throw error;
    }
  }

  async updateCustomerStatus(
    customerId: string,
    statusUpdate: CustomerStatusUpdateDto,
  ): Promise<void> {
    const customer = await this.customerRepository.findOne({
      where: {id: customerId},
    });

    if (!customer) {
      throw new HttpErrors.NotFound('Customer not found');
    }

    // Update customer status
    await this.customerRepository.updateById(customerId, {
      status: statusUpdate.status,
    });
  }
}
