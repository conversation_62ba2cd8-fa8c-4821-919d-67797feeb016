import {Getter, inject} from '@loopback/core';
import {UserNotification, UserNotificationRelations} from '../models';
import {SequelizeUserModifyCrudRepository} from '@sourceloop/core/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {NotifDbSourceName} from '@sourceloop/notification-service';
import {PgDataSource} from '../datasources';

export class UserNotificationRepository extends SequelizeUserModifyCrudRepository<
  UserNotification,
  typeof UserNotification.prototype.id,
  UserNotificationRelations
> {
  constructor(
    @inject(`datasources.${NotifDbSourceName}`) dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(UserNotification, dataSource, getCurrentUser);
  }
}
