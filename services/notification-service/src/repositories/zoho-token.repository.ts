import {inject} from '@loopback/core';
import {DefaultKeyValueRepository} from '@loopback/repository';
import {ZohoToken} from '../models';
import {AuthCacheSourceName} from '@sourceloop/core';
import {RedisDataSource} from '../datasources/redis.datasource';

export class ZohoTokenRepository extends DefaultKeyValueRepository<ZohoToken> {
  constructor(
    @inject(`datasources.${AuthCacheSourceName}`) dataSource: RedisDataSource,
  ) {
    super(ZohoToken, dataSource);
  }
}
