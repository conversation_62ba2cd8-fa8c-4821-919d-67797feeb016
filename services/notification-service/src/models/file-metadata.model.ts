import {model, property} from '@loopback/repository';

@model()
export class FileMetadata {
  @property({
    type: 'string',
    required: true,
  })
  buffer: string;

  @property({
    type: 'string',
    required: true,
  })
  fieldname: string;

  @property({
    type: 'string',
    required: true,
  })
  originalname: string;

  @property({
    type: 'string',
    required: true,
  })
  encoding: string;

  @property({
    type: 'string',
    required: true,
  })
  mimetype: string;

  @property({
    type: 'number',
    required: true,
  })
  size: number;

  @property({
    type: 'string',
    required: true,
  })
  bucket: string;

  @property({
    type: 'string',
    required: true,
  })
  key: string;

  @property({
    type: 'string',
    required: true,
  })
  acl: string;

  @property({
    type: 'string',
  })
  contentType?: string;

  @property({
    type: 'string',
  })
  contentDisposition?: string | null;

  @property({
    type: 'string',
  })
  contentEncoding?: string | null;

  @property({
    type: 'string',
    required: true,
  })
  storageClass: string;

  @property({
    type: 'string',
  })
  serverSideEncryption?: string | null;

  @property({
    type: 'object',
    required: true,
  })
  metadata: object;

  @property({
    type: 'string',
    required: true,
  })
  location: string;

  @property({
    type: 'string',
    required: true,
  })
  etag: string;

  @property({
    type: 'string',
  })
  versionId?: string;

  constructor(data?: Partial<FileMetadata>) {
    Object.assign(this, data);
  }
}

export interface FileMetadataRelations {
  // describe navigational properties here
}

export type FileMetadataWithRelations = FileMetadata & FileMetadataRelations;
