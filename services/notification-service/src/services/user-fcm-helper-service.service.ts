/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {UserFcmRepository} from '../repositories'; // Adjust path as needed
import {UserFcm} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class UserFcmHelperServiceService {
  constructor(
    @repository(UserFcmRepository)
    private userFcmRepository: UserFcmRepository,
  ) {}

  async createOrUpdateFcm(data: UserFcm): Promise<UserFcm> {
    const existingFcm = await this.userFcmRepository.findOne({
      where: {
        deviceId: data.deviceId,
        userTenantId: data.userTenantId,
      },
    });

    if (existingFcm) {
      await this.userFcmRepository.updateById(existingFcm.id, data);
      const updated = await this.userFcmRepository.findById(existingFcm.id);
      return updated;
    }

    const created = await this.userFcmRepository.create(data);
    return created;
  }
}
