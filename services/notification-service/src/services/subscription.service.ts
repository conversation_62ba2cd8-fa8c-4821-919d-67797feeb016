/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import * as admin from 'firebase-admin';

export interface SubscriptionInput {
  topic: string;
  email?: string;
  fcmToken?: string;
  zohoListKey: string;
  zohoTopicId: string;
}

const firebaseConfig = {
  apiKey: 'AIzaSyBIlR9wWTj3e7LhzIgHfIfvqyqHV4BFlXM',
  authDomain: 'ecomdukes-396c4.firebaseapp.com',
  projectId: 'ecomdukes-396c4',
  storageBucket: 'ecomdukes-396c4.firebasestorage.app',
  messagingSenderId: '478123902567',
  appId: '1:478123902567:web:8ceb6f5be74ff8d50676c2',
  measurementId: 'G-CW4CN90MSQ',
  private_key: process.env.FCM_PRIVATE_KEY,
  client_email:
    '<EMAIL>',
};

@injectable({scope: BindingScope.TRANSIENT})
export class SubscriptionService {
  constructor() {
    admin.initializeApp({
      credential: admin.credential.cert(firebaseConfig),
    });
  }

  async subscribe(
    input: SubscriptionInput,
  ): Promise<{fcm?: string; zoho?: string}> {
    const results: {fcm?: string; zoho?: string} = {};
    const messaging = admin.messaging();

    if (!input.fcmToken && !input.email) {
      throw new HttpErrors.UnprocessableEntity(
        'Either email or FCM token must be provided',
      );
    }

    // FCM Subscription
    if (input.fcmToken) {
      try {
        await messaging.subscribeToTopic([input.fcmToken], input.topic);
        // Below console log on purpose
        console.log(`Subscribed tokens to topic: ${input.topic}`);
        results.fcm = 'Subscribed to FCM topic';
      } catch (error) {
        throw new HttpErrors.InternalServerError(
          'FCM Subscription failed: ' + error,
        );
      }
    }

    return results;
  }
}
