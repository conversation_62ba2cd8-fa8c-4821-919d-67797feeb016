import {injectable, BindingScope} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import axios from 'axios';

@injectable({scope: BindingScope.TRANSIENT})
export class ZohoCampaignService {
  private readonly zohoBaseUrl = 'https://campaigns.zoho.com/api/v1.1';
  private readonly accessToken = process.env.ZOHO_CAMPAIGN_ACCESS_TOKEN; // Store securely

  constructor() {}

  async sendBulkEmail(data: {
    subject: string;
    content: string;
    listKey: string; // Zoho List Key
    fromEmail: string;
    campaignName: string;
  }) {
    if (!this.accessToken) {
      throw new HttpErrors.InternalServerError(
        'Zoho Access Token not available',
      );
    }

    const {subject, content, listKey, fromEmail, campaignName} = data;

    // Step 1: Create campaign
    const createResponse = await axios.post(
      `${this.zohoBaseUrl}/createcampaign`,
      null,
      {
        params: {
          scope: 'CampaignsAPI',
          listkey: listKey,
          subject,
          content,
          fromname: 'YourAppName',
          fromemail: fromEmail,
          campaignname: campaignName,
          type: 'text', // or 'html'
          accesstoken: this.accessToken,
        },
      },
    );

    const {campaignKey} = createResponse.data;

    if (!campaignKey) {
      throw new HttpErrors.InternalServerError(
        'Failed to create Zoho campaign',
      );
    }

    // Step 2: Send campaign
    const sendResponse = await axios.post(
      `${this.zohoBaseUrl}/sendcampaign`,
      null,
      {
        params: {
          scope: 'CampaignsAPI',
          campaignkey: campaignKey,
          accesstoken: this.accessToken,
        },
      },
    );

    if (sendResponse.data.status !== 'success') {
      throw new HttpErrors.InternalServerError(
        'Failed to send Zoho email campaign',
      );
    }

    return sendResponse.data;
  }
}
