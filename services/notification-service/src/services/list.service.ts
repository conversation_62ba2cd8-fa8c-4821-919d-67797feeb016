/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, service} from '@loopback/core';
import {Campaign, Groups} from '../models';
import {HttpErrors} from '@loopback/rest';
import axios from 'axios';
import {ZohoTokenService} from './zoho-token.service';

@injectable({scope: BindingScope.TRANSIENT})
export class ListService {
  constructor(
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
  ) {}
  async getMailingLists(
    sort: 'asc' | 'desc' = 'asc',
    fromIndex: number = 1,
    range: number = 20,
  ): Promise<Campaign> {
    try {
      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const response = await axios.get(
        `${process.env.ZOHO_API_BASE_URL}/getmailinglists?resfmt=JSON&sort=${sort}&fromindex=${fromIndex}&range=${range}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (err) {
      console.error(
        '❌ Error fetching mailing lists:',
        err.response?.data || err.message,
      );
      throw new HttpErrors.InternalServerError(
        'Failed to fetch mailing lists: ' + (err.response?.data || err.message),
      );
    }
  }

  async getListSubscribers(listKey: string): Promise<Campaign> {
    try {
      if (!listKey) {
        throw new HttpErrors.BadRequest('List key is required');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const response = await axios.get(
        `${process.env.ZOHO_API_BASE_URL}/getlistsubscribers?resfmt=JSON&listkey=${listKey}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (err) {
      console.error(
        '❌ Error fetching list subscribers:',
        err.response?.data || err.message,
      );
      throw new HttpErrors.InternalServerError(
        'Failed to fetch list subscribers: ' +
          (err.response?.data || err.message),
      );
    }
  }

  async subscribeContactToList(
    listKey: string,
    contactInfo: {
      firstName: string;
      lastName: string;
      email: string;
    },
    topicId: string,
  ): Promise<Groups> {
    try {
      if (!listKey || !contactInfo.email) {
        throw new HttpErrors.BadRequest(
          'List key and contact email are required',
        );
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const contactinfoParam = {
        'First Name': contactInfo.firstName,
        'Last Name': contactInfo.lastName,
        'Contact Email': contactInfo.email,
      };

      const payload = new URLSearchParams({
        resfmt: 'JSON',
        listkey: listKey,
        contactinfo: JSON.stringify(contactinfoParam),
        topic_id: topicId,
      }).toString();

      const response = await axios.post(
        `${process.env.ZOHO_API_BASE_URL}/json/listsubscribe`,
        payload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      return response.data;
    } catch (err) {
      console.error(
        '❌ Error subscribing contact to list:',
        err.response?.data || err.message,
      );
      throw new HttpErrors.InternalServerError(
        'Failed to subscribe contact to list: ' +
          (err.response?.data || err.message),
      );
    }
  }
}
