/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, service} from '@loopback/core';
import {Campaign, Groups} from '../models';
import {HttpErrors} from '@loopback/rest';
import axios from 'axios';
import {ZohoTokenService} from './zoho-token.service';
import {XMLParser} from 'fast-xml-parser';

@injectable({scope: BindingScope.TRANSIENT})
export class ListService {
  constructor(
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
  ) {}

  async createZohoList(
    name: string,
  ): Promise<{listKey: string; listName: string}> {
    const accessToken = await this.zohoTokenService.getValidAccessToken();

    const emailIds = ['<EMAIL>'];
    const payload = `emailids=${encodeURIComponent(emailIds.join(','))}&listname=${encodeURIComponent(name)}&signupform=private&mode=newlist`;

    try {
      const zohoListResponse = await axios.post(
        'https://campaigns.zoho.in/api/v1.1/addlistandcontacts',
        payload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      // Parse the XML response
      const parser = new XMLParser();
      const parsed = parser.parse(zohoListResponse.data);

      const response = parsed.response;
      return {
        listKey: response.listkey,
        listName: response.listname,
      };
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Zoho List Creation failed: ' + (err.response?.data || err.message),
      );
    }
  }

  async getMailingLists(
    sort: 'asc' | 'desc' = 'asc',
    fromIndex: number = 1,
    range: number = 20,
  ): Promise<Campaign> {
    try {
      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const response = await axios.get(
        `${process.env.ZOHO_API_BASE_URL}/getmailinglists?resfmt=JSON&sort=${sort}&fromindex=${fromIndex}&range=${range}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to fetch mailing lists: ' + (err.response?.data || err.message),
      );
    }
  }

  async getListSubscribers(listKey: string): Promise<Campaign> {
    try {
      if (!listKey) {
        throw new HttpErrors.BadRequest('List key is required');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const response = await axios.get(
        `${process.env.ZOHO_API_BASE_URL}/getlistsubscribers?resfmt=JSON&listkey=${listKey}`,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to fetch list subscribers: ' +
          (err.response?.data || err.message),
      );
    }
  }

  async subscribeContactToList(
    listKey: string,
    contactInfo: {
      firstName: string;
      lastName: string;
      email: string;
    },
    topicId: string,
  ): Promise<Groups> {
    try {
      if (!listKey || !contactInfo.email) {
        throw new HttpErrors.BadRequest(
          'List key and contact email are required',
        );
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const contactinfoParam = {
        'First Name': contactInfo.firstName,
        'Last Name': contactInfo.lastName,
        'Contact Email': contactInfo.email,
      };

      const payload = new URLSearchParams({
        resfmt: 'JSON',
        listkey: listKey,
        contactinfo: JSON.stringify(contactinfoParam),
        topic_id: topicId,
      }).toString();

      const response = await axios.post(
        `${process.env.ZOHO_API_BASE_URL}/json/listsubscribe`,
        payload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      return response.data;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to subscribe contact to list: ' +
          (err.response?.data || err.message),
      );
    }
  }

  async deleteZohoList(
    listKey: string,
  ): Promise<{status: string; message: string}> {
    const accessToken = await this.zohoTokenService.getValidAccessToken();

    const url = `https://campaigns.zoho.com/api/v1.1/deletemailinglist?resfmt=JSON&listkey=${encodeURIComponent(
      listKey,
    )}&deletecontacts=on`;

    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const data = response.data;
      if (data.code !== '200') {
        throw new Error(data.message || 'Zoho returned an error');
      }

      return {
        status: 'success',
        message: data.message,
      };
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Zoho List Deletion failed: ' + (err.response?.data || err.message),
      );
    }
  }
}
