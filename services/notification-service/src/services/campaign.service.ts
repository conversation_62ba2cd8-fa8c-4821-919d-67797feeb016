/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, inject, service} from '@loopback/core';
import {Campaign} from '../models';
import {
  INotificationFilterFunc,
  NotifServiceBindings,
} from '@sourceloop/notification-service';
import {repository} from '@loopback/repository';
import {CampaignRepository, GroupsRepository} from '../repositories';
import {HttpErrors} from '@loopback/rest';
import {FCMBindings} from '../keys';
import {FCMConfig} from '../types';
import * as admin from 'firebase-admin';
import axios from 'axios';
import {ZohoTokenService} from './zoho-token.service';

type FCMAppsMap = Record<string, admin.messaging.Messaging>;

@injectable({scope: BindingScope.TRANSIENT})
export class CampaignService {
  private fcmClients: FCMAppsMap = {};

  constructor(
    @inject(NotifServiceBindings.NotificationFilter)
    private readonly filterNotification: INotificationFilterFunc,
    @repository(CampaignRepository)
    public campaignRepository: CampaignRepository,
    @repository(GroupsRepository)
    public groupsRepository: GroupsRepository,
    @inject(FCMBindings.Config, {optional: false})
    private readonly fcmConfigs: FCMConfig[],
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
  ) {
    if (!fcmConfigs || fcmConfigs.length === 0) {
      throw new HttpErrors.PreconditionFailed('FCM configs are missing!');
    }

    for (const config of fcmConfigs) {
      const appName = config.projectId;

      try {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        admin.app(appName).delete();
      } catch (e) {
        // App might not exist yet – ignore
      }

      const app = admin.initializeApp(
        {
          credential: admin.credential.cert({
            projectId: config.projectId,
            clientEmail: config.clientEmail,
            privateKey: config.privateKey.replace(/\\n/g, '\n'),
          }),
        },
        appName,
      );

      this.fcmClients[appName] = app.messaging();
    }
  }

  async createCampaign(campaign: Campaign): Promise<Campaign> {
    const existingCampaigns = await this.campaignRepository.find({
      where: {name: campaign.name},
      limit: 1,
    });

    if (existingCampaigns.length > 0) {
      throw new HttpErrors.BadRequest(
        `Campaign with the name "${campaign.name}" already exists.`,
      );
    }
    const group = await this.groupsRepository.findById(campaign.groupId);
    if (campaign.type === 0) {
      const message = {
        topic: group.name,
        notification: {
          title: campaign.subject,
          body: campaign.body,
        },
        data: {
          click_action: campaign.clickAction ?? '',
        },
      };

      const [config] = this.fcmConfigs;
      const fcmClient = this.fcmClients[config.projectId];

      if (!fcmClient) {
        throw new HttpErrors.InternalServerError(
          'No FCM client configured for this group',
        );
      }

      await fcmClient.send(message);

      this.fcmClients[config.projectId]
        .send(message)
        .then(response => {
          console.log('✅ Successfully sent message:', response);
        })
        .catch(error => {
          console.error('❌ Error sending message:', error);
        });
    } else if (campaign.type === 1) {
      try {
        const accessToken = await this.zohoTokenService.getValidAccessToken();

        const listDetails = {
          [group.listKey]: [],
        };

        const payload = new URLSearchParams({
          resfmt: 'json',
          campaignname: campaign.name,
          from_email: process.env.ZOHO_FROM_EMAIL ?? '',
          subject: campaign.subject,
          list_details: JSON.stringify(listDetails),
          topicId: group.topicId,
          content_url: campaign.body ?? '',
        }).toString();

        const createZohoCampaignResponse = await axios.post(
          `${process.env.ZOHO_API_BASE_URL}/createCampaign`,
          payload,
          {
            headers: {
              Authorization: `Zoho-oauthtoken ${accessToken}`,
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );

        campaign.campaignKey = createZohoCampaignResponse.data?.campaignKey;
        campaign.campaignStatus =
          createZohoCampaignResponse.data?.campaign_status;
        campaign.isDraft = true;
      } catch (err) {
        throw new HttpErrors.InternalServerError(
          'Zoho List Creation failed: ' + (err.response?.data || err.message),
        );
      }
    }

    const createdCampaign = await this.campaignRepository.create(campaign);
    return createdCampaign;
  }

  async sendCampaign(campaignKey: string | undefined): Promise<Campaign> {
    try {
      if (!campaignKey) {
        throw new HttpErrors.BadRequest('Campaign key is missing');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const response = await axios.post(
        `${process.env.ZOHO_API_BASE_URL}/sendcampaign?resfmt=JSON&campaignkey=${campaignKey}`,
        null,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
          },
        },
      );

      const [campaign] = await this.campaignRepository.find({
        where: {campaignKey},
        limit: 1,
      });
      await this.campaignRepository.updateById(campaign.id, {isDraft: false});

      return response.data;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to send campaign: ' + (err.response?.data || err.message),
      );
    }
  }

  async getCampaignDetails(campaignKey: string | undefined): Promise<Campaign> {
    try {
      if (!campaignKey) {
        throw new HttpErrors.BadRequest('Campaign key is missing');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const url = `${process.env.ZOHO_API_BASE_URL}/getcampaigndetails?resfmt=JSON&campaignkey=${campaignKey}&campaigntype=normal`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
        },
      });

      return response.data;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to fetch campaign details: ' +
          (err.response?.data || err.message),
      );
    }
  }

  async deleteCampaign(
    campaignKey: string | undefined,
  ): Promise<{status: string; message: string}> {
    try {
      if (!campaignKey) {
        throw new HttpErrors.BadRequest('Campaign key is missing');
      }

      const accessToken = await this.zohoTokenService.getValidAccessToken();

      const url = `https://campaigns.zoho.com/api/v1.1/deletecampaign?campaignkey=${encodeURIComponent(
        campaignKey,
      )}`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
        },
      });

      const data = response.data;

      if (data.code !== '200') {
        throw new Error(
          data.message || 'Zoho returned an error while deleting campaign',
        );
      }

      return {
        status: 'success',
        message: data.message,
      };
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Failed to delete campaign: ' + (err.response?.data || err.message),
      );
    }
  }
}
