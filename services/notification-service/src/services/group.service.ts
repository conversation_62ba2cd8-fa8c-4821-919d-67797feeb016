/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {GroupsRepository} from '../repositories';
import {Groups} from '../models';
import {HttpErrors} from '@loopback/rest';
import {ErrorKeys} from '@sourceloop/notification-service';
import {ZohoTokenService} from './zoho-token.service';
import {ListService} from './list.service';

import {TopicService} from './topic.service';

@injectable({scope: BindingScope.TRANSIENT})
export class GroupService {
  constructor(
    @repository(GroupsRepository) public groupRepository: GroupsRepository,
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
    @service(ListService)
    private readonly listService: ListService,
    @service(TopicService)
    private readonly topicService: TopicService,
  ) {}

  async create(group: Groups): Promise<Groups> {
    if (!group) {
      throw new HttpErrors.UnprocessableEntity(ErrorKeys.ReceiverNotFound);
    }

    await this.topicService.subscribeToFcmTopic(group.name);

    const topicId = await this.topicService.createZohoTopic(group.name);
    const {listKey, listName} = await this.listService.createZohoList(
      group.name,
    );

    await this.listService.subscribeContactToList(
      listKey,
      {
        firstName: 'Subin',
        lastName: 'Firow',
        email: '<EMAIL>',
      },
      topicId,
    );
    group.topicId = topicId;
    group.listKey = listKey;
    group.listName = listName;
    group.groupId = listName?.toLowerCase();

    return this.groupRepository.create(group);
  }

  async delete(id: string): Promise<void> {
    return this.groupRepository.deleteById(id);
  }
}
