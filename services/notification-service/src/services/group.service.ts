/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {GroupsRepository} from '../repositories';
import {Groups} from '../models';
import {HttpErrors} from '@loopback/rest';
import {ErrorKeys} from '@sourceloop/notification-service';
import axios from 'axios';
import * as admin from 'firebase-admin';
import {ZohoTokenService} from './zoho-token.service';
import {XMLParser} from 'fast-xml-parser';
import {ListService} from './list.service';

const firebaseConfig = {
  apiKey: 'AIzaSyBIlR9wWTj3e7LhzIgHfIfvqyqHV4BFlXM',
  authDomain: 'ecomdukes-396c4.firebaseapp.com',
  projectId: 'ecomdukes-396c4',
  storageBucket: 'ecomdukes-396c4.firebasestorage.app',
  messagingSenderId: '478123902567',
  appId: '1:478123902567:web:8ceb6f5be74ff8d50676c2',
  measurementId: 'G-CW4CN90MSQ',
  private_key: process.env.FCM_PRIVATE_KEY,
  client_email:
    '<EMAIL>',
};

@injectable({scope: BindingScope.TRANSIENT})
export class GroupService {
  constructor(
    @repository(GroupsRepository) public groupRepository: GroupsRepository,
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
    @service(ListService)
    private readonly listService: ListService,
  ) {
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(firebaseConfig),
      });
    }
  }

  async create(group: Groups): Promise<Groups> {
    if (!group) {
      throw new HttpErrors.UnprocessableEntity(ErrorKeys.ReceiverNotFound);
    }

    await this.subscribeToFcmTopic(group.name);
    const accessToken = await this.zohoTokenService.getValidAccessToken();

    const topicId = await this.createZohoTopic(group.name, accessToken);
    const {listKey, listName} = await this.createZohoList(
      group.name,
      accessToken,
    );

    await this.listService.subscribeContactToList(
      listKey,
      {
        firstName: 'Subin',
        lastName: 'Firow',
        email: '<EMAIL>',
      },
      topicId,
    );
    group.topicId = topicId;
    group.listKey = listKey;
    group.listName = listName;
    group.groupId = listName?.toLowerCase();

    return this.groupRepository.create(group);
  }

  private async subscribeToFcmTopic(topic: string) {
    const dummyFCMToken =
      'eM1TZMvrusLYgH2hVfdBVb:APA91bH6cIye_mH4OQk2bvx_gz_q8xujQoSfG3hBE6JQTu52KFzVCHGZyXhJIltwEJPthtx8YwSjQkRm9jgUE_x7021wFnhS37YKnvwjWHzXLl9_JBca_Mo';
    try {
      await admin.messaging().subscribeToTopic([dummyFCMToken], topic);
      // Below console log on purpose
      console.log(`Subscribed to FCM topic: ${topic}`);
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        `FCM Subscription failed: ${err}`,
      );
    }
  }

  private async createZohoTopic(
    name: string,
    accessToken: string,
  ): Promise<string> {
    const details = {
      topic_name: name,
      topic_desc: name,
    };

    try {
      const response = await axios.post(
        `https://campaigns.zoho.in/api/v1.1/topics?details=${encodeURIComponent(JSON.stringify(details))}`,
        null,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      return response.data?.topic_id;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Zoho Topic Creation failed: ' + (err.response?.data || err.message),
      );
    }
  }

  private async createZohoList(
    name: string,
    accessToken: string,
  ): Promise<{listKey: string; listName: string}> {
    const emailIds = ['<EMAIL>'];
    const payload = `emailids=${encodeURIComponent(emailIds.join(','))}&listname=${encodeURIComponent(name)}&signupform=private&mode=newlist`;

    try {
      const zohoListResponse = await axios.post(
        'https://campaigns.zoho.in/api/v1.1/addlistandcontacts',
        payload,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      // Parse the XML response
      const parser = new XMLParser();
      const parsed = parser.parse(zohoListResponse.data);

      const response = parsed.response;
      return {
        listKey: response.listkey,
        listName: response.listname,
      };
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Zoho List Creation failed: ' + (err.response?.data || err.message),
      );
    }
  }
}
