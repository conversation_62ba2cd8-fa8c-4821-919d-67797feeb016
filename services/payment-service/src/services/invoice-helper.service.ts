import {injectable, BindingScope, inject} from '@loopback/core';
import {ZohoInvoice} from './zoho-invoice.service';
import {CreateZohoInvoiceRequestDto} from '../models';
import {
  ZohoInvoiceRequest,
  ZohoCustomerRequest,
  ZohoLineItem,
  ZohoCustomerResponse,
  ZohoTokenResponse,
  ZohoInvoiceResponse,
  ZohoPaymentRequest,
  PaymentMode,
} from '../types';
import {repository} from '@loopback/repository';
import {PaymentRepository} from '../repositories';
import {ILogger, LOGGER} from '@sourceloop/core';
import {toCamelCase, toSnakeCase} from '../utils';
@injectable({scope: BindingScope.TRANSIENT})
export class InvoiceHelperService {
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor(
    @inject('services.ZohoInvoice')
    private readonly zohoInvoiceService: ZohoInvoice,
    @repository(PaymentRepository)
    private readonly paymentRepository: PaymentRepository,
    @inject(LOGGER.LOGGER_INJECT)
    public logger: ILogger,
  ) {}

  private mapToPaymentMode(method: string): PaymentMode {
    const validModes: Set<string> = new Set([
      PaymentMode.CHECK,
      PaymentMode.CASH,
      PaymentMode.CREDIT_CARD,
      PaymentMode.BANK_TRANSFER,
      PaymentMode.BANK_REMITTANCE,
      PaymentMode.AUTO_TRANSACTION,
      PaymentMode.OTHERS,
    ]);

    return validModes.has(method)
      ? (method as PaymentMode)
      : PaymentMode.OTHERS;
  }

  /**
   * Create an invoice in Zoho after a successful payment
   * @param payment The payment record
   * @param orderDetails Additional order details needed for the invoice
   */
  async createInvoiceForPayment(
    invoiceData: CreateZohoInvoiceRequestDto,
  ): Promise<{
    invoiceId: string;
    invoiceUrl: string;
    contactId?: string;
  } | null> {
    try {
      // Ensure we have a valid token
      const token = await this.getValidToken();
      if (!token) {
        this.logger.error('Failed to get valid Zoho token');
        return null;
      }

      let customerId: string | null;
      let isNewCustomer = false;

      // Use existing Zoho contact ID if available, otherwise create one
      if (
        invoiceData?.zohoContactId &&
        invoiceData.zohoContactId.trim() !== ''
      ) {
        customerId = invoiceData.zohoContactId;
      } else {
        customerId = await this.createCustomer(
          invoiceData.customerName,
          invoiceData.customerEmail,
          invoiceData.customerPhone,
          token,
        );
        isNewCustomer = true;
      }

      if (!customerId) {
        this.logger.error('Failed to create or get customer in Zoho');
        return null;
      }

      // Prepare line items
      const lineItems: ZohoLineItem[] = invoiceData.items.map(item => ({
        itemId: item.itemId,
        name: item.name,
        description: item.description,
        rate: item.price,
        quantity: item.quantity,
      }));

      // Create invoice request
      const invoiceRequest: ZohoInvoiceRequest = {
        customerId,
        date: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
        lineItems,
        paymentTerms: 0, // Due on receipt
        notes: `Payment Reference: ${invoiceData.payment.transactionId}`,
        terms: 'Thank you for your business!',
      };

      // Add billing address if available
      if (invoiceData.billingAddress) {
        invoiceRequest.billingAddress = {
          address: invoiceData.billingAddress.address,
          city: invoiceData.billingAddress.city,
          state: invoiceData.billingAddress.state,
          zip: invoiceData.billingAddress.zip,
          country: invoiceData.billingAddress.country,
        };
      }

      const convertedRequest = toSnakeCase(
        invoiceRequest,
      ) as ZohoInvoiceRequest;

      // Create invoice in Zoho
      const response = await this.zohoInvoiceService.createInvoice(
        convertedRequest,
        token,
        process.env.ZOHO_ORGANIZATION_ID ?? '',
      );

      const zohoInvoiceResponse = toCamelCase(response) as ZohoInvoiceResponse;

      if (zohoInvoiceResponse.code === 0 && zohoInvoiceResponse.invoice) {
        //marking the invoice as sent
        await this.zohoInvoiceService.markInvoiceAsSent(
          zohoInvoiceResponse?.invoice?.invoiceId ?? '',
          token,
          process.env.ZOHO_ORGANIZATION_ID ?? '',
        );
        const paymentRecordReq: ZohoPaymentRequest = {
          customerId,
          paymentMode: this.mapToPaymentMode(invoiceData.payment.paymentMethod),
          amount: zohoInvoiceResponse.invoice.total,
          date: zohoInvoiceResponse.invoice.date,
          referenceNumber: zohoInvoiceResponse.invoice.referenceNumber,
          invoices: [
            {
              invoiceId: zohoInvoiceResponse.invoice.invoiceId ?? '',
              amountApplied: zohoInvoiceResponse.invoice.total,
            },
          ],
          invoiceId: zohoInvoiceResponse.invoice.invoiceId ?? '',
          amountApplied: zohoInvoiceResponse.invoice.total,
        };

        const convertedPaymentReq = toSnakeCase(paymentRecordReq);

        await this.zohoInvoiceService.recordInvoicePayment(
          convertedPaymentReq as ZohoPaymentRequest,
          token,
          process.env.ZOHO_ORGANIZATION_ID ?? '',
        );

        // Update payment record with invoice ID
        await this.paymentRepository.updateById(invoiceData.payment.id, {
          invoiceUrl: zohoInvoiceResponse.invoice.invoiceUrl,
        });

        this.logger.info(
          `Created Zoho invoice ${zohoInvoiceResponse.invoice.invoiceId} for payment ${invoiceData.payment.id}`,
        );
        return {
          invoiceId: zohoInvoiceResponse.invoice.invoiceId,
          invoiceUrl: zohoInvoiceResponse.invoice.invoiceUrl,
          contactId: isNewCustomer ? customerId : undefined,
        };
      } else {
        this.logger.error(
          `Failed to create Zoho invoice: ${zohoInvoiceResponse.message}`,
        );
        return null;
      }
    } catch (error) {
      this.logger.error('Error creating Zoho invoice:', error);
      return null;
    }
  }

  /**
   * Create a customer in Zoho or get existing customer by email
   */
  private async createCustomer(
    name: string,
    email: string,
    phone: string,
    token: string,
  ): Promise<string | null> {
    try {
      // For simplicity, we're creating a new customer each time
      // In a production environment, you might want to search for existing customers first
      const customerRequest: ZohoCustomerRequest = {
        contactName: name,
        contactPersons: [
          {
            firstName: name.split(' ')[0],
            lastName: name.split(' ').slice(1).join(' ') || '',
            email: email,
            phone: phone,
            isPrimaryContact: true,
          },
        ],
      };

      const body = toSnakeCase(customerRequest);
      console.log('🚀 ~ InvoiceHelperService ~ body:', body);

      const response = await this.zohoInvoiceService.createCustomer(
        body as ZohoCustomerRequest,
        token,
        process.env.ZOHO_ORGANIZATION_ID ?? '',
      );
      console.log('🚀 ~ InvoiceHelperService ~ response:', response);

      const zohoContactResponse = toCamelCase(response) as ZohoCustomerResponse;

      if (zohoContactResponse.code === 0 && zohoContactResponse.contact) {
        return zohoContactResponse.contact.contactId;
      } else {
        this.logger.error(
          `Failed to create Zoho customer: ${zohoContactResponse}`,
        );
        return null;
      }
    } catch (error) {
      this.logger.error('Error creating Zoho customer:', error);
      return null;
    }
  }

  /**
   * Get a valid Zoho access token
   */
  private async getValidToken(): Promise<string | null> {
    // Check if we have a valid token
    if (this.accessToken && this.tokenExpiry && this.tokenExpiry > new Date()) {
      return this.accessToken;
    }

    try {
      // Refresh the token
      const response = await this.zohoInvoiceService.refreshToken(
        process.env.ZOHO_REFRESH_TOKEN ?? '',
        process.env.ZOHO_CLIENT_ID ?? '',
        process.env.ZOHO_CLIENT_SECRET ?? '',
      );

      const zohoTokenResponse = toCamelCase(response) as ZohoTokenResponse;

      this.accessToken = zohoTokenResponse.accessToken;
      // Set expiry time (usually 3600 seconds = 1 hour)
      this.tokenExpiry = new Date(
        Date.now() + (zohoTokenResponse.expiresIn - 300) * 1000,
      ); // Subtract 5 minutes for safety

      return this.accessToken;
    } catch (error) {
      this.logger.error('Error refreshing Zoho token:', error);
      return null;
    }
  }

  async downloadInvoicePdf(invoiceId: string): Promise<Buffer> {
    const token = await this.getValidToken();

    const response = await this.zohoInvoiceService.getInvoicesPdf(
      invoiceId,
      token ?? '',
      process.env.ZOHO_ORGANIZATION_ID ?? '',
    );
    const buffer = Buffer.from(response as any);

    return buffer;
  }
}
