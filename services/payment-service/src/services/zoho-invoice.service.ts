import {getService} from '@loopback/service-proxy';
import {inject, Provider} from '@loopback/core';
import {ZohoInvoiceDataSource} from '../datasources';
import {
  ZohoInvoiceRequest,
  ZohoInvoiceResponse,
  ZohoCustomerRequest,
  ZohoCustomerResponse,
  ZohoTokenResponse,
  ZohoItemRequest,
  ZohoPaymentRequest,
} from '../types';
import {ZohoItemResponse} from '../models';

export interface ZohoInvoice {
  createInvoice: (
    body: ZohoInvoiceRequest,
    token: string,
    organizationId: string,
  ) => Promise<ZohoInvoiceResponse>;

  getInvoice: (
    invoiceId: string,
    token: string,
    organizationId: string,
  ) => Promise<ZohoInvoiceResponse>;

  createCustomer: (
    body: ZohoCustomerRequest,
    token: string,
    organizationId: string,
  ) => Promise<ZohoCustomerResponse>;

  getCustomer: (
    contactId: string,
    token: string,
    organizationId: string,
  ) => Promise<ZohoCustomerResponse>;

  refreshToken: (
    refreshToken: string,
    clientId: string,
    clientSecret: string,
  ) => Promise<ZohoTokenResponse>;

  createItem: (
    body: ZohoItemRequest,
    token: string,
    organizationId: string,
  ) => Promise<ZohoItemResponse>;

  getItem: (
    itemId: string,
    token: string,
    organizationId: string,
  ) => Promise<ZohoItemResponse>;

  updateItem: (
    body: ZohoItemRequest,
    token: string,
    organizationId: string,
    itemId: string,
  ) => Promise<ZohoItemResponse>;

  markInvoiceAsSent: (
    invoiceId: string,
    token: string,
    organizationId: string,
  ) => Promise<void>;

  recordInvoicePayment: (
    body: ZohoPaymentRequest,
    token: string,
    organizationId: string,
  ) => Promise<void>;

  getInvoicesPdf: (
    invoiceIds: string,
    token: string,
    organizationId: string,
  ) => Promise<any>;
}

export class ZohoInvoiceProvider implements Provider<ZohoInvoice> {
  constructor(
    @inject('datasources.zohoInvoice')
    protected dataSource: ZohoInvoiceDataSource = new ZohoInvoiceDataSource(),
  ) {}

  value(): Promise<ZohoInvoice> {
    return getService(this.dataSource);
  }
}
