/* eslint-disable @typescript-eslint/naming-convention */
export const Zoh<PERSON>InvoiceDataSourceConfig = {
  name: 'zohoInvoice',
  connector: 'rest',
  baseURL: process.env.ZOHO_INVOICE_API_URL,
  crud: false,
  options: {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: `${process.env.ZOHO_INVOICE_API_URL}/invoices?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
        body: '{body}',
      },
      functions: {
        createInvoice: ['body', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'GET',
        url: `${process.env.ZOHO_INVOICE_API_URL}/invoices/{invoiceId}?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
      },
      functions: {
        getInvoice: ['invoiceId', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.ZOHO_INVOICE_API_URL}/contacts?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
        body: '{body}',
      },
      functions: {
        createCustomer: ['body', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'GET',
        url: `${process.env.ZOHO_INVOICE_API_URL}/contacts/{contactId}?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
      },
      functions: {
        getCustomer: ['contactId', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.ZOHO_TOKEN_API_URL}/token`,
        query: {
          refresh_token: '{refreshToken}',
          client_id: '{clientId}',
          client_secret: '{clientSecret}',
          grant_type: 'refresh_token',
        },
      },
      functions: {
        refreshToken: ['refreshToken', 'clientId', 'clientSecret'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.ZOHO_INVOICE_API_URL}/items?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
        body: '{body}',
      },
      functions: {
        createItem: ['body', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'GET',
        url: `${process.env.ZOHO_INVOICE_API_URL}/items/{itemId}?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
      },
      functions: {
        getItem: ['itemId', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'PUT',
        url: `${process.env.ZOHO_INVOICE_API_URL}/items/{itemId}?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
        body: '{body}',
      },
      functions: {
        updateItem: ['body', 'token', 'organizationId', 'itemId'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.ZOHO_INVOICE_API_URL}/invoices/{invoiceId}/status/sent?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
      },
      functions: {
        markInvoiceAsSent: ['invoiceId', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'POST',
        url: `${process.env.ZOHO_INVOICE_API_URL}/customerpayments?organization_id={organizationId}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          Authorization: 'Zoho-oauthtoken {token}',
        },
        body: '{body}',
      },
      functions: {
        recordInvoicePayment: ['body', 'token', 'organizationId'],
      },
    },
    {
      template: {
        method: 'GET',
        url: `${process.env.ZOHO_INVOICE_API_URL}/invoices/pdf?organization_id={organizationId}&invoice_ids={invoiceIds}`,
        headers: {
          accept: 'application/pdf',
          Authorization: 'Zoho-oauthtoken {token}',
        },
      },
      functions: {
        getInvoicesPdf: ['invoiceIds', 'token', 'organizationId'],
      },
    },
  ],
};
