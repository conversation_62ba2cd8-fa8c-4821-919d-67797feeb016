import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {ZohoInvoiceDataSourceConfig} from './configs';

@lifeCycleObserver('datasource')
export class ZohoInvoiceDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static dataSourceName = 'zohoInvoice';
  static readonly defaultConfig = ZohoInvoiceDataSourceConfig;

  constructor(
    @inject('datasources.config.zohoInvoice', {optional: true})
    dsConfig: object = ZohoInvoiceDataSourceConfig,
  ) {
    super(dsConfig);
  }
}
