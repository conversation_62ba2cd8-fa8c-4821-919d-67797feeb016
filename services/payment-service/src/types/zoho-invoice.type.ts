export interface ZohoInvoiceRequest {
  customerId?: string;
  contactPersons?: string[];
  invoiceNumber?: string;
  referenceNumber?: string;
  date: string;
  dueDate?: string;
  paymentTerms?: number;
  paymentTermsLabel?: string;
  currencyId?: string;
  currencyCode?: string;
  exchangeRate?: number;
  discount?: number;
  discountType?: 'entityLevel' | 'itemLevel';
  isDiscountBeforeTax?: boolean;
  discountAccountId?: string;
  lineItems: ZohoLineItem[];
  shippingCharge?: number;
  adjustment?: number;
  adjustmentDescription?: string;
  customFields?: ZohoCustomField[];
  templateId?: string;
  notes?: string;
  terms?: string;
  billingAddress?: ZohoAddress;
  shippingAddress?: ZohoAddress;
  paymentOptions?: ZohoPaymentOptions;
}

export interface ZohoLineItem {
  itemId: string;
  name: string;
  description?: string;
  itemOrder?: number;
  rate: number;
  quantity: number;
  unit?: string;
  discount?: number;
  discountType?: 'itemLevel' | 'entityLevel';
  taxId?: string;
  taxName?: string;
  taxPercentage?: number;
}

export interface ZohoCustomField {
  label: string;
  value: string;
}

export interface ZohoAddress {
  attention?: string;
  address?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  phone?: string;
  fax?: string;
}

export interface ZohoPaymentOptions {
  paymentGateways?: ZohoPaymentGateway[];
}

export interface ZohoPaymentGateway {
  gatewayName: string;
  additionalField1?: string;
  additionalField2?: string;
  additionalField3?: string;
}

export interface ZohoInvoiceResponse {
  code: number;
  message: string;
  invoice?: {
    invoiceId: string;
    invoiceNumber: string;
    date: string;
    status: string;
    customerId: string;
    customerName: string;
    total: number;
    balance: number;
    createdTime: string;
    updatedTime: string;
    invoiceUrl: string;
    referenceNumber: string;
  };
}

export interface ZohoCustomerRequest {
  contactName: string;
  contactPersons: ZohoContactPerson[];
  currencyId?: string;
  currencyCode?: string;
  notes?: string;
  billingAddress?: ZohoAddress;
  shippingAddress?: ZohoAddress;
  customFields?: ZohoCustomField[];
}

export interface ZohoContactPerson {
  firstName: string;
  lastName?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  isPrimaryContact?: boolean;
}

export interface ZohoCustomerResponse {
  code: number;
  message: string;
  contact?: {
    contactId: string;
    contactName: string;
    email: string;
    phone: string;
    status: string;
    createdTime: string;
    updatedTime: string;
  };
}

export interface ZohoTokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  apiDomain: string;
  tokenType: string;
}

export interface ZohoItemRequest {
  name: string;
  rate: number;
}

export interface ZohoItemResponse {
  itemId: number;
  name: string;
  status: string;
  description?: string;
  rate: number;
  unit?: string;
  hsnOrSac?: string;
  sku?: string;
  productType?: string;
  taxId?: number;
  taxName?: string;
  taxPercentage?: string;
  taxType?: string;
  itemTaxPreferences?: {
    taxId: number;
    taxSpecification: string;
  }[];
  locations?: {
    locationId: string;
    locationName?: string;
    status: string;
    isPrimary: boolean;
  }[];
}

export enum PaymentMode {
  CHECK = 'check',
  CASH = 'cash',
  CREDIT_CARD = 'creditcard',
  BANK_TRANSFER = 'banktransfer',
  BANK_REMITTANCE = 'bankremittance',
  AUTO_TRANSACTION = 'autotransaction',
  OTHERS = 'others',
}

export interface ZohoPaymentRequest {
  customerId: string;
  paymentMode: PaymentMode;
  amount: number;
  date: string; // Format: yyyy-mm-dd
  referenceNumber?: string;
  description?: string;
  invoices: {
    invoiceId: string;
    amountApplied: number;
    taxAmountWithheld?: number;
  }[];
  invoiceId: string;
  amountApplied: number;
  exchangeRate?: number; // Default is 1
  paymentForm?: string; // 🇲🇽 only
  bankCharges?: number;
  customFields?: {
    label: string;
    value: string | number | boolean;
  }[];
  locationId?: string;
  accountId?: string;
  contactPersons?: string[];
}
