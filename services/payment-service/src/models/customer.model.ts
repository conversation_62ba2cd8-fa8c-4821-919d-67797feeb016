import {belongsTo, model, property} from '@loopback/repository';
import {UserTenant} from '@sourceloop/authentication-service';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'customers'})
export class Customer extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @property({
    type: 'boolean',
    name: 'email_verified',
    default: false,
  })
  emailVerified?: boolean;

  @property({
    type: 'boolean',
    name: 'phone_verified',
    default: false,
  })
  phoneVerified?: boolean;

  @property({
    type: 'string',
  })
  status?: string;

  @belongsTo(
    () => UserTenant,
    {keyTo: 'id', name: 'userTenant'},
    {name: 'user_tenant_id', required: true},
  )
  userTenantId: string;

  @property({
    type: 'string',
    name: 'zoho_contact_id',
  })
  zohoContactId?: string;

  constructor(data?: Partial<Customer>) {
    super(data);
  }
}
