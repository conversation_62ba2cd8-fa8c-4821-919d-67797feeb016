import {Model, model, property} from '@loopback/repository';
import {Customer} from './customer.model';
import {OrderLineItem} from './order-line-item.model';

@model()
export class Invoice extends Model {
  @property({
    type: 'string',
    required: false,
    name: 'order_id',
  })
  orderId: string;

  @property({
    type: 'number',
    required: true,
    name: 'total_amount',
  })
  totalAmount: number;

  @property({
    type: 'string',
    required: true,
  })
  currency: string;

  @property({
    type: 'string',
  })
  gstNumber?: string;

  @property({
    type: 'string',
  })
  discount?: string;

  @property({type: 'object', required: true})
  customer: Customer;

  @property({type: 'array', itemType: 'object', required: true})
  orderLineItems: OrderLineItem[];

  constructor(data?: Partial<Invoice>) {
    super(data);
  }
}

export interface InvoiceRelations {
  // describe navigational properties here
}

export type InvoiceWithRelations = Invoice & InvoiceRelations;
