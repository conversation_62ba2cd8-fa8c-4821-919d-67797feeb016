import {Model, model, property} from '@loopback/repository';

@model()
export class OrderLineItem extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'number',
    required: true,
  })
  quantity: number;

  @property({
    type: 'number',
    required: true,
    name: 'unit_price',
  })
  unitPrice: number;

  @property({
    type: 'number',
    required: true,
    name: 'total_price',
  })
  totalPrice: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  constructor(data?: Partial<OrderLineItem>) {
    super(data);
  }
}
