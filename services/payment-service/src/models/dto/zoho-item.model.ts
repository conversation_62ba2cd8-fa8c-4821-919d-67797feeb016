import {Model, model, property} from '@loopback/repository';

@model()
export class ZohoItem extends Model {
  @property({type: 'string'})
  itemId?: string;

  @property({type: 'string'})
  name?: string;

  @property({type: 'string'})
  sku?: string;

  @property({type: 'string'})
  status?: string;

  @property({type: 'string'})
  source?: string;

  @property({type: 'string'})
  unit?: string;

  @property({type: 'string'})
  unitId?: string;

  @property({type: 'string'})
  description?: string;

  @property({type: 'number'})
  rate?: number;

  @property({type: 'number'})
  salesRate?: number;

  @property({type: 'boolean'})
  trackInventory?: boolean;

  @property({type: 'string'})
  itemType?: string;

  @property({type: 'string'})
  productType?: string;
  constructor(data?: Partial<ZohoItem>) {
    super(data);
  }
}

@model()
export class ZohoItemRequest extends Model {
  @property({type: 'string', required: true})
  name: string;

  @property({type: 'number', required: true})
  rate: number;

  @property({type: 'string'})
  description?: string;

  @property({type: 'string'})
  taxId?: string;

  @property({type: 'string'})
  sku?: string;

  @property({type: 'string'})
  productType?: string;

  @property({type: 'string'})
  itemType?: string;

  @property({type: 'string'})
  accountId?: string;

  @property({type: 'string'})
  purchaseRate?: string;

  @property({type: 'string'})
  purchaseDescription?: string;

  @property({type: 'string'})
  purchaseAccountId?: string;

  @property({type: 'string'})
  inventoryAccountId?: string;

  @property({type: 'string'})
  unit?: string;

  @property({type: 'boolean'})
  isTaxable?: boolean;

  constructor(data?: Partial<ZohoItemRequest>) {
    super(data);
  }
}

@model()
export class ZohoItemResponse extends Model {
  @property({type: 'number'})
  code?: number;

  @property({type: 'string'})
  message?: string;

  @property({type: 'object'})
  item?: ZohoItem;

  constructor(data?: Partial<ZohoItemResponse>) {
    super(data);
  }
}
