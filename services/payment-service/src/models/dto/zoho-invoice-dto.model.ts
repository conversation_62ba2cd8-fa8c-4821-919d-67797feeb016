import {Entity, model, property} from '@loopback/repository';
import {ZohoInvoiceItem} from './zoho-invoice-item.model';
import {BillingAddress} from './billing-address.model';
import {Payment} from '../payment.model';

@model()
export class CreateZohoInvoiceRequestDto extends Entity {
  @property({
    type: 'object',
    required: true,
  })
  payment: Payment;

  @property({
    type: 'string',
    required: true,
  })
  customerName: string;

  @property({
    type: 'string',
    required: true,
  })
  customerEmail: string;

  @property({
    type: 'string',
    required: true,
  })
  customerPhone: string;

  @property({
    type: 'string',
  })
  zohoContactId?: string;

  @property({
    type: BillingAddress,
    required: false,
  })
  billingAddress?: BillingAddress;

  @property.array(ZohoInvoiceItem, {required: true})
  items: ZohoInvoiceItem[];

  constructor(data?: Partial<CreateZohoInvoiceRequestDto>) {
    super(data);
  }
}
