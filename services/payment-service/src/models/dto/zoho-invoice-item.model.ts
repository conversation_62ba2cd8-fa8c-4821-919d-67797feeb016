import {Model, model, property} from '@loopback/repository';

@model()
export class ZohoInvoiceItem extends Model {
  @property({type: 'string', required: true})
  name: string;

  @property({type: 'string'})
  description?: string;

  @property({type: 'number', required: true})
  quantity: number;

  @property({type: 'number', required: true})
  price: number;

  @property({type: 'string', required: true})
  itemId: string;

  constructor(data?: Partial<ZohoInvoiceItem>) {
    super(data);
  }
}
