import {
  post,
  requestBody,
  response,
  HttpErrors,
  param,
  get,
} from '@loopback/rest';
import {service} from '@loopback/core';
import {InvoiceHelperService} from '../services';
import {CreateZohoInvoiceRequestDto} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/invoices';

export class InvoiceController {
  constructor(
    @service(InvoiceHelperService)
    private readonly invoiceHelperService: InvoiceHelperService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Create Zoho Invoice',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            invoiceId: {type: 'string'},
            invoiceUrl: {type: 'string'},
            contactId: {type: 'string'},
          },
        },
      },
    },
  })
  async createInvoice(
    @requestBody({
      description: 'Zoho Invoice creation request',
      required: true,
      content: {
        'application/json': {
          schema: {'x-ts-type': CreateZohoInvoiceRequestDto},
        },
      },
    })
    invoiceData: CreateZohoInvoiceRequestDto,
  ): Promise<{
    invoiceId: string;
    invoiceUrl: string;
    contactId?: string;
  }> {
    const invoiceDetails =
      await this.invoiceHelperService.createInvoiceForPayment(invoiceData);

    if (!invoiceDetails) {
      throw new HttpErrors.InternalServerError('Failed to create Zoho Invoice');
    }

    return invoiceDetails;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{invoiceId}/download`)
  @response(STATUS_CODE.OK, {
    description: 'Download Zoho Invoice PDF',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadInvoice(
    @param.path.string('invoiceId') invoiceId: string,
  ): Promise<Buffer> {
    const pdfBuffer =
      await this.invoiceHelperService.downloadInvoicePdf(invoiceId);

    if (!pdfBuffer) {
      throw new HttpErrors.InternalServerError(
        'Failed to download invoice PDF',
      );
    }

    return pdfBuffer;
  }
}
