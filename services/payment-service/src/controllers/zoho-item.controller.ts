import {ZohoInvoice} from '../services/zoho-invoice.service';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {PermissionKeys} from '@local/core';
import {inject} from '@loopback/core';
import {ZohoItemRequest, ZohoItemResponse} from '../models';
import {toCamelCase} from '../utils';
import {ZohoTokenResponse} from '../types';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/zoho/items';

export class ZohoItemController {
  constructor(
    @inject('services.ZohoInvoice')
    private readonly zohoInvoiceService: ZohoInvoice,
  ) {}
  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Create Item in Zoho',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            itemId: {type: 'string'},
          },
        },
      },
    },
  })
  async createItem(
    @requestBody({
      description: 'Zoho item creation request',
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ZohoItemRequest),
        },
      },
    })
    itemData: ZohoItemRequest,
  ): Promise<{itemId: string}> {
    const token = await this.zohoInvoiceService.refreshToken(
      process.env.ZOHO_REFRESH_TOKEN ?? '',
      process.env.ZOHO_CLIENT_ID ?? '',
      process.env.ZOHO_CLIENT_SECRET ?? '',
    );

    if (!token) {
      throw new HttpErrors.InternalServerError('Unable to fetch Zoho token');
    }
    const tokenConverted = toCamelCase(token) as ZohoTokenResponse;
    const organizationId = process.env.ZOHO_ORGANIZATION_ID ?? '';
    const zohoItem = await this.zohoInvoiceService.createItem(
      itemData,
      tokenConverted.accessToken,
      organizationId,
    );
    const convertedRes = toCamelCase(zohoItem) as ZohoItemResponse;
    return {itemId: convertedRes.item?.itemId as string};
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{itemId}`)
  @response(STATUS_CODE.OK, {
    description: 'Get Zoho item by ID',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ZohoItemResponse),
      },
    },
  })
  async getItem(
    @param.path.string('itemId') itemId: string,
  ): Promise<ZohoItemResponse> {
    const token = await this.zohoInvoiceService.refreshToken(
      process.env.ZOHO_REFRESH_TOKEN ?? '',
      process.env.ZOHO_CLIENT_ID ?? '',
      process.env.ZOHO_CLIENT_SECRET ?? '',
    );
    if (!token) {
      throw new HttpErrors.InternalServerError('Unable to fetch Zoho token');
    }

    const organizationId = process.env.ZOHO_ORGANIZATION_ID ?? '';
    return this.zohoInvoiceService.getItem(
      itemId,
      token.accessToken,
      organizationId,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @put(`${basePath}/{itemId}`)
  @response(STATUS_CODE.OK, {
    description: 'Update Zoho item',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ZohoItemResponse),
      },
    },
  })
  async updateItem(
    @param.path.string('itemId') itemId: string,
    @requestBody({
      description: 'Zoho item update request',
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ZohoItemRequest, {partial: true}),
        },
      },
    })
    itemData: ZohoItemRequest,
  ): Promise<ZohoItemResponse> {
    const token = await this.zohoInvoiceService.refreshToken(
      process.env.ZOHO_REFRESH_TOKEN ?? '',
      process.env.ZOHO_CLIENT_ID ?? '',
      process.env.ZOHO_CLIENT_SECRET ?? '',
    );
    if (!token) {
      throw new HttpErrors.InternalServerError('Unable to fetch Zoho token');
    }

    const organizationId = process.env.ZOHO_ORGANIZATION_ID ?? '';
    return this.zohoInvoiceService.updateItem(
      itemData,
      token.accessToken,
      organizationId,
      itemId,
    );
  }
}
