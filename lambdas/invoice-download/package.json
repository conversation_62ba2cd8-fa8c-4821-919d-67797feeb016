{"name": "invoice-download", "version": "0.1.0", "bin": {"invoice-download": "bin/invoice-download.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/aws-lambda": "^8.10.149", "@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.1019.2", "aws-lambda": "^1.0.7", "esbuild": "^0.25.5", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.840.0", "@aws-sdk/lib-storage": "^3.840.0", "@aws-sdk/s3-request-presigner": "^3.840.0", "@sparticuz/chromium": "110.0.0", "aws-cdk-lib": "2.201.0", "constructs": "^10.0.0", "dotenv": "^17.0.0", "puppeteer-core": "19.6.0"}}