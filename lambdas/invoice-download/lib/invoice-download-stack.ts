import * as cdk from 'aws-cdk-lib';
import {Duration} from 'aws-cdk-lib';
import {Code, LayerVersion, Runtime} from 'aws-cdk-lib/aws-lambda';
import {Bucket} from 'aws-cdk-lib/aws-s3';
import {NodejsFunction} from 'aws-cdk-lib/aws-lambda-nodejs';
import {Construct} from 'constructs';
import {LambdaIntegration, RestApi} from 'aws-cdk-lib/aws-apigateway';

export class InvoiceDownloadStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Read bucket name from environment variable
    const bucketName = process.env.AWS_S3_BUCKET ?? '';
    const cdnOrigin = process.env.CDN_ORIGIN ?? '';
    // Reference an existing bucket instead of creating a new one
    const pdfsS3Bucket = Bucket.fromBucketAttributes(this, 'PDFsS3Bucket', {
      bucketName,
    });

    const chromeAwsLambdaLayer = new LayerVersion(
      this,
      'ChromeAWSLambdaLayer',
      {
        layerVersionName: 'ChromeAWSLambdaLayer',
        compatibleRuntimes: [Runtime.NODEJS_18_X],
        code: Code.fromAsset('chromium-v110.0.0-layer.zip'),
      },
    );

    const htmlToPdfLambda = new NodejsFunction(this, 'HtmlToPdfLambda', {
      entry: 'lambdas/invoice-conversion/index.ts',
      environment: {
        S3_PDF_BUCKET: bucketName,
        CDN_ORIGIN: cdnOrigin,
      },
      layers: [chromeAwsLambdaLayer],
      bundling: {
        externalModules: ['aws-sdk'],
        nodeModules: ['@sparticuz/chromium'],
      },
      timeout: Duration.seconds(30),
      runtime: Runtime.NODEJS_18_X,
      memorySize: 1024,
    });
    pdfsS3Bucket.grantReadWrite(htmlToPdfLambda);

    const healthcheckLambda = new NodejsFunction(this, 'HealthcheckLambda', {
      entry: 'lambdas/healthcheck-lambda/index.ts',
    });

    const api = new RestApi(this, 'HtmlToPdfRestApi', {
      restApiName: 'HTML PDF API',
    });

    api.root.addMethod(
      'GET',
      new LambdaIntegration(healthcheckLambda, {
        requestTemplates: {'application/json': '{ "statusCode": "200" }'},
      }),
    );
    api.root.addMethod(
      'POST',
      new LambdaIntegration(htmlToPdfLambda, {
        requestTemplates: {'application/json': '{ "statusCode": "200" }'},
      }),
    );
  }
}
