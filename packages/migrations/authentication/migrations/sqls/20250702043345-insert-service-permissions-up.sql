/* Replace with your SQL commands */
UPDATE main.roles
SET permissions = array_append(permissions, 'CreateServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_append(permissions, 'ViewServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_append(permissions, 'UpdateServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_append(permissions, 'DeleteServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_append(permissions, 'CreateEcomdukeService')
WHERE role_type = 0;

UPDATE main.roles
SET permissions = array_append(permissions, 'ViewEcomdukeService')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_append(permissions, 'UpdateEcomdukeService')
WHERE role_type = 0;

UPDATE main.roles
SET permissions = array_append(permissions, 'DeleteEcomdukeService')
WHERE role_type = 0;