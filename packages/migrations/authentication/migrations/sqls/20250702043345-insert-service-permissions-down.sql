/* Replace with your SQL commands */

UPDATE main.roles
SET permissions = array_remove(permissions, 'CreateServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_remove(permissions, 'ViewServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_remove(permissions, 'UpdateServiceRequest')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_remove(permissions, 'DeleteServiceRequest')
WHERE role_type IN (0, 1, 2, 3);
UPDATE main.roles

SET permissions = array_remove(permissions, 'CreateEcomdukeService')
WHERE role_type = 0;

UPDATE main.roles
SET permissions = array_remove(permissions, 'ViewEcomdukeService')
WHERE role_type IN (0, 1, 2, 3);

UPDATE main.roles
SET permissions = array_remove(permissions, 'UpdateEcomdukeService')
WHERE role_type = 0;

UPDATE main.roles
SET permissions = array_remove(permissions, 'DeleteEcomdukeService')
WHERE role_type = 0;