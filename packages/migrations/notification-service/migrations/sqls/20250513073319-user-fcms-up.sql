/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.user_fcms (
    id uuid NOT NULL DEFAULT (
        md5(
            (
                (random()) :: text || (clock_timestamp()) :: text
            )
        ) :: uuid
    ),
    fcm_token text NOT NULL,
    device_id text NOT NULL,
    user_tenant_id UUID NOT NULL,
    created_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by uuid,
    modified_by uuid,
    deleted bool DEFAULT false NOT NULL,
    deleted_by uuid,
    deleted_on timestamptz,
    CONSTRAINT pk_user_fcms_id PRIMARY KEY (id)
);