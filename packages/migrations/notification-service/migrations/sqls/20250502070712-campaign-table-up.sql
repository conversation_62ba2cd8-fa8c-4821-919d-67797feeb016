-- Table: main.campaign_table
CREATE TABLE IF NOT EXISTS main.campaigns (
    id uuid NOT NULL DEFAULT (
        md5(
            (
                (random()) :: text || (clock_timestamp()) :: text
            )
        ) :: uuid
    ),
    name varchar(255) NOT NULL,
    type integer NOT NULL CHECK (type IN (0, 1, 2)),
    -- 0=Push, 1=Email, 2=SMS
    receiver varchar(255),
    individual_users text [] DEFAULT '{}',
    subject varchar(255),
    click_action text,
    body text,
    is_draft boolean DEFAULT false,
    sent timestamptz,
    "options" text,
    group_key varchar(255),
    is_critical boolean DEFAULT false,
    campaign_status varchar(100),
    campaign_key varchar(255),
    created_on timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_on timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_by uuid,
    deleted boolean DEFAULT false,
    deleted_on timestamp with time zone,
    deleted_by uuid,
    CONSTRAINT campaign_table_pkey PRIMARY KEY (id)
);