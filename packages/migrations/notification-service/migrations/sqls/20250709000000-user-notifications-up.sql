/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.user_notifications (
    id UUID NOT NULL DEFAULT (
        md5(
            (
                (random()) :: text || (clock_timestamp()) :: text
            )
        ) :: uuid
    ),
    user_tenant_id UUID NOT NULL,
    notification_id UUID NOT NULL,
    is_read B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE NOT NULL,
    group_id UUID NOT NULL,
    topic_id TEXT NOT NULL,
    list_key TEXT NOT NULL,
    list_name TEXT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by <PERSON><PERSON><PERSON>,
    modified_by <PERSON><PERSON><PERSON>,
    deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_by <PERSON><PERSON><PERSON>,
    deleted_on TIMESTAMPTZ,
    CONSTRAINT pk_user_notifications_id PRIMARY KEY (id)
);

-- <PERSON>reate indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_tenant_id ON main.user_notifications(user_tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_notification_id ON main.user_notifications(notification_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_group_id ON main.user_notifications(group_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_is_read ON main.user_notifications(is_read);

-- Add foreign key constraints if needed
-- ALTER TABLE main.user_notifications ADD CONSTRAINT fk_user_notifications_notification_id 
--     FOREIGN KEY (notification_id) REFERENCES main.notifications(id) ON DELETE CASCADE;
-- ALTER TABLE main.user_notifications ADD CONSTRAINT fk_user_notifications_group_id 
--     FOREIGN KEY (group_id) REFERENCES main.groups(id) ON DELETE CASCADE;
