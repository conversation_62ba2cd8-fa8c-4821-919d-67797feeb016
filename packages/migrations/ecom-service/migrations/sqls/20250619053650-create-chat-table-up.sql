/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.chats (
    id              uuid DEFAULT gen_random_uuid() NOT NULL,
    customer_id     uuid NOT NULL,
    seller_id       uuid NOT NULL,
    status          varchar(50) DEFAULT 'ACTIVE',
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         boolean DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    created_by      uuid,
    modified_by     uuid,
    CONSTRAINT pk_chats PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS main.chat_messages (
    id              uuid DEFAULT gen_random_uuid() NOT NULL,
    chat_id         uuid NOT NULL,
    sender_id       uuid NOT NULL,
    sender_type     varchar(50) NOT NULL,
    message         text NOT NULL,
    read            boolean DEFAULT false NOT NULL,
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         boolean DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    created_by      uuid,
    modified_by     uuid,
    CONSTRAINT pk_chat_messages PRIMARY KEY (id),
    CONSTRAINT fk_chat_messages_chat FOREIGN KEY (chat_id) REFERENCES main.chats(id)
);
