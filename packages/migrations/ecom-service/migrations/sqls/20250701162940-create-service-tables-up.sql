/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.ecomdukeservices (
    id              uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    name            varchar(255) NOT NULL,
    description     text,
    price           numeric(10, 2) NOT NULL DEFAULT 0.00,
    currency        varchar(10) NOT NULL DEFAULT 'INR',
    tax_category_id uuid,
    is_active       boolean NOT NULL DEFAULT true,
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         boolean DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    created_by      uuid,
    modified_by     uuid,
    deleted_by      uuid,
    CONSTRAINT pk_ecomdukeservices PRIMARY KEY (id),
    CONSTRAINT uq_ecomdukeservices_name UNIQUE (name)
);

CREATE TABLE IF NOT EXISTS main.ecomdukeservice_requests (
    id                  uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    seller_id           uuid NOT NULL,
    service_id          uuid NOT NULL,
    status              varchar(50) NOT NULL , 
    payment_reference   numeric,       
    paid_amount         numeric(10, 2),       
    paid_on             timestamptz,         
    notes               text,                
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             boolean DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    created_by          uuid,
    modified_by         uuid,
    deleted_by          uuid,
    CONSTRAINT pk_ecomdukeservice_requests PRIMARY KEY (id)
);
