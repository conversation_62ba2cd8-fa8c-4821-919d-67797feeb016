-- Remove amount column from main.plans
ALTER TABLE main.plans
DROP COLUMN amount;

-- Create main.plan_pricing table
CREATE TABLE main.plan_pricing (
    id              uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL PRIMARY KEY,
    plan_id             uuid NOT NULL REFERENCES main.plans(id) ON DELETE CASCADE,
    min_sales_threshold integer NOT NULL,
    max_sales_threshold integer NOT NULL,
    price               numeric(20,2) NOT NULL,
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by      uuid,
    modified_by     uuid,
    deleted         bool DEFAULT false NOT NULL,
    deleted_by      uuid,
    deleted_on      timestamptz
);
