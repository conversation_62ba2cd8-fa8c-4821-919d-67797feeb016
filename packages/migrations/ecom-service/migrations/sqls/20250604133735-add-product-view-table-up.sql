CREATE TABLE IF NOT EXISTS main.product_views (
    id                  uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    user_id            uuid NOT NULL,
    viewed_at         timestamptz NOT NULL,
    product_variant_id  uuid NOT NULL,
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             bool DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    deleted_by          uuid,
    created_by          uuid,
    modified_by         uuid,
    CONSTRAINT pk_viewed_products PRIMARY KEY (id),
    CONSTRAINT fk_viewed_products_product_variant FOREIGN KEY (product_variant_id)
        REFERENCES main.product_variants (id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_viewed_products_product_variant_id ON main.product_views (product_variant_id);


CREATE TABLE IF NOT EXISTS main.product_view_counts (
    id                  uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    view_count           integer NOT NULL,
    product_variant_id  uuid NOT NULL,
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             bool DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    deleted_by          uuid,
    created_by          uuid,
    modified_by         uuid,
    CONSTRAINT pk_viewed_count_products PRIMARY KEY (id),
    CONSTRAINT fk_viewed_count_products_product_variant FOREIGN KEY (product_variant_id)
        REFERENCES main.product_variants (id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_viewed_count_products_product_variant_id ON main.product_view_counts (product_variant_id);


