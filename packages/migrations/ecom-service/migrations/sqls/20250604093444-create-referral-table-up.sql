/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.referrals (
    id                  uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    referral_code   text NOT NULL UNIQUE,
    type            text NOT NULL,
    status          text NOT NULL,
    referrer_id     uuid NOT NULL,
    referred_id     uuid,
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         bool DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    created_by      uuid,
    modified_by     uuid,
    CONSTRAINT pk_referrals PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS idx_referrals_referral_code ON main.referrals (referral_code);
CREATE INDEX IF NOT EXISTS idx_referrals_referrer_id ON main.referrals (referrer_id);
CREATE INDEX IF NOT EXISTS idx_referrals_referred_id ON main.referrals (referred_id);
