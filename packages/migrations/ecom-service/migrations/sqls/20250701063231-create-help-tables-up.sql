/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.helps (
    id          uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    question    varchar(500) NOT NULL,
    answer      text NOT NULL,
    category    varchar(100) NOT NULL,
    visibility  integer DEFAULT 0 NOT NULL, 
    status      integer DEFAULT 0 NOT NULL,  
    created_on  timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted     boolean DEFAULT false NOT NULL,
    deleted_by  uuid,
    deleted_on  timestamptz,
    created_by  uuid,
    modified_by uuid,
    CONSTRAINT pk_helps_id PRIMARY KEY (id)
);
