/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.duke_coins (
    id                  uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    user_tenant_id      uuid NOT NULL,
    coins               numeric(10, 2) NOT NULL DEFAULT 0,
    referral_id         uuid,
    transaction_type    varchar(20) NOT NULL,
    coins_changed       numeric(10, 2) NOT NULL,
    description         text,
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             bool DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    deleted_by          uuid,
    created_by          uuid,
    modified_by         uuid,
    CONSTRAINT pk_duke_coins PRIMARY KEY (id)
);