/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.legals (
    id          uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    data        text NOT NULL,
    category    text NOT NULL,
    type        text NOT NULL,

    created_on  timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted     bool DEFAULT false NOT NULL,
    deleted_on  timestamptz,
    deleted_by  uuid,
    created_by  uuid,
    modified_by uuid,

    CONSTRAINT pk_legals PRIMARY KEY (id)
);
