/* Replace with your SQL commands */

CREATE TABLE IF NOT EXISTS main.referral_programs (
    id                             uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    name                           text NOT NULL,
    type                           text NOT NULL,
    referrer_coins                 numeric(10, 2) NOT NULL,
    referee_coins                  numeric(10, 2) NOT NULL,
    min_eligibility_order_price    numeric(10, 2) NOT NULL,
    created_on                     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on                    timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                        bool DEFAULT false NOT NULL,
    deleted_on                     timestamptz,
    deleted_by                     uuid,
    created_by                     uuid,
    modified_by                    uuid,
    CONSTRAINT pk_referral_programs PRIMARY KEY (id)
);

-- Create indexes for better performance (optional)
CREATE INDEX IF NOT EXISTS idx_referral_programs_name ON main.referral_programs (name);
CREATE INDEX IF NOT EXISTS idx_referral_programs_type ON main.referral_programs (type);
