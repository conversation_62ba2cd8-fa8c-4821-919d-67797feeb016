/* Replace with your SQL commands */
-- Create shipping methods table
CREATE TABLE IF NOT EXISTS main.shipping_methods (
    id                  UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    name                VARCHAR(255) NOT NULL,
    description         TEXT,
    is_active           BOOLEAN DEFAULT TRUE,
    type                VARCHAR(50) NOT NULL, -- 'ECOMDUKES' or 'SELF_SHIPPING'
    created_on          TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             BOOLEAN DEFAULT FALSE,
    deleted_on          TIMESTAMPTZ,
    deleted_by          <PERSON><PERSON><PERSON>,
    created_by          U<PERSON><PERSON>,
    modified_by         UUI<PERSON>,
    CONSTRAINT pk_shipping_methods PRIMARY KEY (id)
);

-- Create seller shipping profiles table
CREATE TABLE IF NOT EXISTS main.seller_shipping_profiles (
    id                  UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    seller_id           UUID NOT NULL,
    shipping_method_id  UUID NOT NULL,
    name                VARCHAR(255) NOT NULL,
    description         TEXT,
    is_default          BOOLEAN DEFAULT FALSE,
    is_active           BOOLEAN DEFAULT TRUE,
    created_on          TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             BOOLEAN DEFAULT FALSE,
    deleted_on          TIMESTAMPTZ,
    deleted_by          UUID,
    created_by          UUID,
    modified_by         UUID,
    CONSTRAINT pk_seller_shipping_profiles PRIMARY KEY (id),
    CONSTRAINT fk_seller_shipping_profiles_method FOREIGN KEY (shipping_method_id) 
        REFERENCES main.shipping_methods(id) ON DELETE CASCADE
);

-- Create seller shipping charges table for state-to-state shipping
CREATE TABLE IF NOT EXISTS main.seller_shipping_charges (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id     UUID NOT NULL,
    country_code            VARCHAR(10) NOT NULL,
    state_code              VARCHAR(10),
    is_default              BOOLEAN DEFAULT FALSE,
    base_charge             NUMERIC(10, 2) NOT NULL,
    additional_charge       NUMERIC(10, 2) DEFAULT 0,
    free_shipping_threshold NUMERIC(10, 2),
    estimated_days_min      INTEGER,
    estimated_days_max      INTEGER,
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              UUID,
    created_by              UUID,
    modified_by             UUID,
    CONSTRAINT pk_seller_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_seller_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

-- Create product-specific shipping charges table
CREATE TABLE IF NOT EXISTS main.product_shipping_charges (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    product_id              UUID NOT NULL,
    product_variant_id      UUID,
    shipping_profile_id     UUID NOT NULL,
    override_default        BOOLEAN DEFAULT TRUE,
    base_charge             NUMERIC(10, 2) NOT NULL,
    additional_charge       NUMERIC(10, 2) DEFAULT 0,
    free_shipping_threshold NUMERIC(10, 2),
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              UUID,
    created_by              UUID,
    modified_by             UUID,
    CONSTRAINT pk_product_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_product_shipping_charges_product FOREIGN KEY (product_id) 
        REFERENCES main.products(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_shipping_charges_variant FOREIGN KEY (product_variant_id) 
        REFERENCES main.product_variants(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

-- Create weight-based shipping rules table
CREATE TABLE IF NOT EXISTS main.weight_based_shipping_rules (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id     UUID NOT NULL,
    min_weight              NUMERIC(10, 2) NOT NULL,
    max_weight              NUMERIC(10, 2),
    charge                  NUMERIC(10, 2) NOT NULL,
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              UUID,
    created_by              UUID,
    modified_by             UUID,
    CONSTRAINT pk_weight_based_shipping_rules PRIMARY KEY (id),
    CONSTRAINT fk_weight_based_shipping_rules_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

-- Insert default shipping methods
INSERT INTO main.shipping_methods (id, name, description, is_active, type)
VALUES 
(md5(random()::text || clock_timestamp()::text)::uuid, 'ECOMDUKES Shipping', 'Official ECOMDUKES shipping service', TRUE, 'ECOMDUKES'),
(md5(random()::text || clock_timestamp()::text)::uuid, 'Self Shipping', 'Seller managed shipping service', TRUE, 'SELF_SHIPPING');

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_shipping_methods_type ON main.shipping_methods(type);
CREATE INDEX IF NOT EXISTS idx_seller_shipping_profiles_seller_id ON main.seller_shipping_profiles(seller_id);
CREATE INDEX IF NOT EXISTS idx_seller_shipping_profiles_method_id ON main.seller_shipping_profiles(shipping_method_id);
CREATE INDEX IF NOT EXISTS idx_seller_shipping_charges_profile_id ON main.seller_shipping_charges(shipping_profile_id);
CREATE INDEX IF NOT EXISTS idx_seller_shipping_charges_states ON main.seller_shipping_charges(from_state, to_state);
CREATE INDEX IF NOT EXISTS idx_product_shipping_charges_product ON main.product_shipping_charges(product_id);
CREATE INDEX IF NOT EXISTS idx_product_shipping_charges_variant ON main.product_shipping_charges(product_variant_id);
CREATE INDEX IF NOT EXISTS idx_product_shipping_charges_profile ON main.product_shipping_charges(shipping_profile_id);
CREATE INDEX IF NOT EXISTS idx_weight_based_shipping_rules_profile ON main.weight_based_shipping_rules(shipping_profile_id);

-- Create triggers for updating modified_on
CREATE TRIGGER mdt_shipping_methods
BEFORE UPDATE ON main.shipping_methods
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_seller_shipping_profiles
BEFORE UPDATE ON main.seller_shipping_profiles
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_seller_shipping_charges
BEFORE UPDATE ON main.seller_shipping_charges
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_product_shipping_charges
BEFORE UPDATE ON main.product_shipping_charges
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_weight_based_shipping_rules
BEFORE UPDATE ON main.weight_based_shipping_rules
FOR EACH ROW EXECUTE PROCEDURE main.moddatetime();
