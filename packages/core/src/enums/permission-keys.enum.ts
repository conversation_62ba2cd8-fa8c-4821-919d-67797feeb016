export enum PermissionKeys {
  // Seller permissions
  CreateSeller = 'CreateSeller',
  ViewSeller = 'ViewSeller',
  UpdateSeller = 'UpdateSeller',
  DeleteSeller = 'DeleteSeller',

  // Customer permissions
  CreateCustomer = 'CreateCustomer',
  ViewCustomer = 'ViewCustomer',
  UpdateCustomer = 'UpdateCustomer',
  DeleteCustomer = 'DeleteCustomer',

  // Audit permissions
  CreateAudit = 'CreateAudit',
  ViewAudit = 'ViewAudit',
  UpdateAudit = 'UpdateAudit',
  DeleteAudit = 'DeleteAudit',

  // Order permissions
  CreateOrder = 'CreateOrder',
  ViewOrder = 'ViewOrder',
  UpdateOrder = 'UpdateOrder',
  DeleteOrder = 'DeleteOrder',

  // Cart permissions
  CreateCart = 'CreateCart',
  ViewCart = 'ViewCart',
  UpdateCart = 'UpdateCart',
  DeleteCart = 'DeleteCart',

  // Payment permissions
  CreatePayment = 'CreatePayment',
  ViewPayment = 'ViewPayment',
  UpdatePayment = 'UpdatePayment',
  DeletePayment = 'DeletePayment',

  // Product permissions
  CreateProduct = 'CreateProduct',
  ViewProduct = 'ViewProduct',
  UpdateProduct = 'UpdateProduct',
  DeleteProduct = 'DeleteProduct',

  // Category permissions
  CreateCategory = 'CreateCategory',
  ViewCategory = 'ViewCategory',
  UpdateCategory = 'UpdateCategory',
  DeleteCategory = 'DeleteCategory',

  // Facet permissions
  CreateFacet = 'CreateFacet',
  ViewFacet = 'ViewFacet',
  UpdateFacet = 'UpdateFacet',
  DeleteFacet = 'DeleteFacet',

  // Faq permissions
  CreateFaq = 'CreateFaq',
  ViewFaq = 'ViewFaq',
  UpdateFaq = 'UpdateFaq',
  DeleteFaq = 'DeleteFaq',

  CreateSubAdmin = 'CreateSubAdmin',
  UpdateSubAdmin = 'UpdateSubAdmin',
  ViewSubAdmin = 'ViewSubAdmin',
  DeleteSubAdmin = 'DeleteSubAdmin',
  // Configuration permissions
  CreateConfiguration = 'CreateConfiguration',
  ViewConfiguration = 'ViewConfiguration',
  UpdateConfiguration = 'UpdateConfiguration',
  DeleteConfiguration = 'DeleteConfiguration',

  // Plan Permissions
  CreatePlan = 'CreatePlan',
  UpdatePlan = 'UpdatePlan',
  ViewPlan = 'ViewPlan',
  DeletePlan = 'DeletePlan',

  // Feature Permissions
  CreateFeature = 'CreateFeature',
  UpdateFeature = 'UpdateFeature',
  ViewFeature = 'ViewFeature',
  DeleteFeature = 'DeleteFeature',

  // FeatureValue Permissions
  CreateFeatureValue = 'CreateFeatureValue',
  UpdateFeatureValue = 'UpdateFeatureValue',
  ViewFeatureValue = 'ViewFeatureValue',
  DeleteFeatureValue = 'DeleteFeatureValue',

  // PlanFeatureValue Permissions
  CreatePlanFeatureValue = 'CreatePlanFeatureValue',
  UpdatePlanFeatureValue = 'UpdatePlanFeatureValue',
  ViewPlanFeatureValue = 'ViewPlanFeatureValue',
  DeletePlanFeatureValue = 'DeletePlanFeatureValue',

  //Subscription Permissions
  CreateSubscription = 'CreateSubscription',
  UpdateSubscription = 'UpdateSubscription',
  ViewSubscription = 'ViewSubscription',
  DeleteSubscription = 'DeleteSubscription',

  CreateCollection = 'CreateCollection',
  ViewCollection = 'ViewCollection',
  UpdateCollection = 'UpdateCollection',
  DeleteCollection = 'DeleteCollection',

  CreateAsset = 'CreateAsset',
  ViewAsset = 'ViewAsset',
  UpdateAsset = 'UpdateAsset',
  DeleteAsset = 'DeleteAsset',

  CreateWarehouse = 'CreateWarehouse',
  ViewWarehouse = 'ViewWarehouse',
  UpdateWarehouse = 'UpdateWarehouse',
  DeleteWarehouse = 'DeleteWarehouse',

  // TermsAndCondition Permissions
  CreateTermsAndCondition = 'CreateTermsAndCondition',
  ViewTermsAndCondition = 'ViewTermsAndCondition',
  UpdateTermsAndCondition = 'UpdateTermsAndCondition',
  DeleteTermsAndCondition = 'DeleteTermsAndCondition',

  // PrivacyPolicy Permissions
  CreatePrivacyPolicy = 'CreatePrivacyPolicy',
  ViewPrivacyPolicy = 'ViewPrivacyPolicy',
  UpdatePrivacyPolicy = 'UpdatePrivacyPolicy',
  DeletePrivacyPolicy = 'DeletePrivacyPolicy',

  // TaxCategory Permissions
  CreateTaxCategory = 'CreateTaxCategory',
  ViewTaxCategory = 'ViewTaxCategory',
  UpdateTaxCategory = 'UpdateTaxCategory',
  DeleteTaxCategory = 'DeleteTaxCategory',
  // Inventory Permissions
  CreateInventory = 'CreateInventory',
  ViewInventory = 'ViewInventory',
  UpdateInventory = 'UpdateInventory',
  DeleteInventory = 'DeleteInventory',

  // PromoCode Permissions
  CreatePromoCode = 'CreatePromoCode',
  ViewPromoCode = 'ViewPromoCode',
  UpdatePromoCode = 'UpdatePromoCode',
  DeletePromoCode = 'DeletePromoCode',

  ViewNotification = 'ViewNotification',
  CreateNotification = 'CreateNotification',
  UpdateNotification = 'UpdateNotification',
  DeleteNotification = 'DeleteNotification',
  CanGetNotificationAccess = 'CanGetNotificationAccess',

  ViewNotificationNum = '1',
  CreateNotificationNum = '2',
  UpdateNotificationNum = '3',
  DeleteNotificationNum = '4',
  CanGetNotificationAccessNum = '5',

  //Validation Permissions
  ValidatePan = 'ValidatePan',

  //Wishlist permissions

  CreateWishlist = 'CreateWishlist',
  ViewWishlist = 'ViewWishlist',
  UpdateWishlist = 'UpdateWishlist',
  DeleteWishlist = 'DeleteWishlist',
  // Addresses Permissions
  CreateAddress = 'CreateAddress',
  ViewAddress = 'ViewAddress',
  UpdateAddress = 'UpdateAddress',
  DeleteAddress = 'DeleteAddress',

  //Review permissions

  CreateReview = 'CreateReview',
  ViewReview = 'ViewReview',
  UpdateReview = 'UpdateReview',
  DeleteReview = 'DeleteReview',

  CreatePageSection = 'CreatePageSection',
  ViewPageSection = 'ViewPageSection',
  UpdatePageSection = 'UpdatePageSection',
  DeletePageSection = 'DeletePageSection',

  CreateReferral = 'CreateReferral',
  ViewReferral = 'ViewReferral',
  UpdateReferral = 'UpdateReferral',
  DeleteReferral = 'DeleteReferral',

  CreateReferralProgram = 'CreateReferralProgram',
  ViewReferralProgram = 'ViewReferralProgram',
  UpdateReferralProgram = 'UpdateReferralProgram',
  DeleteReferralProgram = 'DeleteReferralProgram',

  CreateDukeCoin = 'CreateDukeCoin',
  ViewDukeCoin = 'ViewDukeCoin',
  UpdateDukeCoin = 'UpdateDukeCoin',
  DeleteDukeCoin = 'DeleteDukeCoin',
  // Campaign Permissions
  CreateCampaign = 'CreateCampaign',
  ViewCampaign = 'ViewCampaign',
  UpdateCampaign = 'UpdateCampaign',
  DeleteCampaign = 'DeleteCampaign',

  CreateUser = 'CreateUser',
  Onboard = 'Onboard',
  ViewUser = 'ViewUser',

  // Chat permissions
  ViewChat = 'ViewChat',
  CreateChat = 'CreateChat',
  UpdateChat = 'UpdateChat',
  DeleteChat = 'DeleteChat',

  ViewChatMessage = 'ViewChatMessage',
  CreateChatMessage = 'CreateChatMessage',
  UpdateChatMessage = 'UpdateChatMessage',
  DeleteChatMessage = 'DeleteChatMessage',
}
