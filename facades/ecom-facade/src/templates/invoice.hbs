{{#each sellerPages}}
  <div
    style='max-width: 800px; margin: auto; font-family: Arial, sans-serif; font-size: 14px; color: #000; border: 1px solid #ccc; page-break-after: always;'
  >
    <!-- Header -->
    <table
      width='100%'
      cellpadding='10'
      cellspacing='0'
      style='border-collapse: collapse;'
    >
      <tr>
        <td style='padding: 20px; max-width: 65%; word-wrap: break-word;'>
          <h2 style='margin: 0;'>{{companyName}}</h2>
          <p style='margin: 4px 0; white-space: normal;'>{{companyAddress}}</p>
          <p style='margin: 4px 0;'>{{companyEmail}}</p>
        </td>
        <td
          style='text-align: right; vertical-align: top; padding: 20px; white-space: nowrap;'
        >
          <h1 style='margin: 0;'>TAX INVOICE</h1>
        </td>
      </tr>
    </table>

    <!-- Invoice Metadata -->
    <table
      width='100%'
      cellpadding='10'
      cellspacing='0'
      style='border-collapse: collapse;'
    >
      <tr>
        <td>
          <strong>Invoice #:</strong>
          {{invoiceNumber}}<br />
          <strong>Invoice Date:</strong>
          {{invoiceDate}}<br />
        </td>
      </tr>
    </table>

    <hr style='border: none; border-top: 1px solid #ccc;' />

    <!-- Customer & Address Section -->
    <table
      width='100%'
      cellpadding='10'
      cellspacing='0'
      style='border-collapse: collapse;'
    >
      <tr><td colspan='2'><strong>Bill To</strong></td></tr>
      <tr>
        <td colspan='2'>
          <a
            href='#'
            style='color: #0066cc; text-decoration: none;'
          >{{customerName}}</a>
        </td>
      </tr>
      <tr>
        {{#if billingAddress.addressLine1}}
          <td width='50%' valign='top'>
            <strong>Billing Address</strong><br />
            {{billingAddress.name}}<br />
            {{billingAddress.addressLine1}}<br />
            {{#if
              billingAddress.addressLine2
            }}{{billingAddress.addressLine2}}<br />{{/if}}
            {{billingAddress.locality}}<br />
            {{#if billingAddress.landmark}}Landmark:
              {{billingAddress.landmark}}<br />{{/if}}
            {{billingAddress.city}},
            {{billingAddress.state}}
            {{billingAddress.zipCode}}<br />
            {{billingAddress.country}}<br />
            Phone:
            {{billingAddress.phoneNumber}}<br />
            {{#if billingAddress.alternativePhoneNumber}}Alt. Phone:
              {{billingAddress.alternativePhoneNumber}}<br />{{/if}}
          </td>
        {{/if}}

        {{#if shippingAddress.addressLine1}}
          <td width='50%' valign='top'>
            <strong>Shipping Address</strong><br />
            {{shippingAddress.name}}<br />
            {{shippingAddress.addressLine1}}<br />
            {{#if
              shippingAddress.addressLine2
            }}{{shippingAddress.addressLine2}}<br />{{/if}}
            {{shippingAddress.locality}}<br />
            {{#if shippingAddress.landmark}}Landmark:
              {{shippingAddress.landmark}}<br />{{/if}}
            {{shippingAddress.city}},
            {{shippingAddress.state}}
            {{shippingAddress.zipCode}}<br />
            {{shippingAddress.country}}<br />
            Phone:
            {{shippingAddress.phoneNumber}}<br />
            {{#if shippingAddress.alternativePhoneNumber}}Alt. Phone:
              {{shippingAddress.alternativePhoneNumber}}<br />{{/if}}
          </td>
        {{/if}}
      </tr>
    </table>

    <!-- Sold By -->
    <p style='margin-top: 10px;'><strong>Sold By:</strong>
      {{seller.name}},
      {{seller.address}}</p>

    <!-- Item Table -->
    <table
      width='100%'
      cellpadding='10'
      cellspacing='0'
      style='border-collapse: collapse; border: 1px solid #ccc; margin-top: 10px;'
    >
      <thead>
        <tr style='background: #f5f5f5;'>
          <th style='border: 1px solid #ccc;'>#</th>
          <th style='border: 1px solid #ccc; text-align: left;'>Item &
            Description</th>
          <th style='border: 1px solid #ccc;'>Qty</th>
          <th style='border: 1px solid #ccc;'>Rate</th>
          <th style='border: 1px solid #ccc;'>Amount</th>
        </tr>
      </thead>
      <tbody>
        {{#each items}}
          <tr>
            <td style='border: 1px solid #ccc; text-align: center;'>{{inc
                @index
              }}</td>
            <td style='border: 1px solid #ccc;'>{{name}}<br /><small
                style='color: #777;'
              >{{description}}</small></td>
            <td
              style='border: 1px solid #ccc; text-align: center;'
            >{{quantity}}</td>
            <td style='border: 1px solid #ccc; text-align: right;'>{{rate}}</td>
            <td
              style='border: 1px solid #ccc; text-align: right;'
            >{{amount}}</td>
          </tr>
        {{/each}}
      </tbody>
    </table>

    <!-- Totals -->
    <table
      width='100%'
      cellpadding='10'
      cellspacing='0'
      style='margin-top: 10px;'
    >
      <tr>
        <td>
          <strong>Total In Words:</strong><br />
          <em>{{amountInWords}}</em><br /><br />
          <strong>Payment Reference:</strong>
          {{paymentReference}}
        </td>
        <td style='text-align: right;'>
          <table cellpadding='6' cellspacing='0' width='100%'>
            <tr>
              <td style='text-align: left;'>Sub Total</td>
              <td style='text-align: right;'>{{subTotal}}</td>
            </tr>
            <tr>
              <td style='text-align: left;'><strong>Total</strong></td>
              <td style='text-align: right;'><strong>{{total}}</strong></td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    {{#if seller.signatureUrl}}
      <!-- Signature -->
      <table
        width='100%'
        cellpadding='10'
        cellspacing='0'
        style='margin-top: 20px;'
      >
        <tr>
          <td style='text-align: right;'>
            <p>Authorised Signature</p>
            <p style='margin-top: 40px;'>
              <img
                src='{{seller.signatureUrl}}'
                alt='Seller Signature'
                style='max-height: 80px;'
              />
            </p>
          </td>
        </tr>
      </table>
    {{/if}}
  </div>
{{/each}}