import {inject} from '@loopback/context';
import {get, param, post, requestBody} from '@loopback/rest';
import {
  CONTENT_TYPE,
  ErrorCodes,
  ILogger,
  LOGGER,
  OPERATION_SECURITY_SPEC,
  restService,
  STATUS_CODE,
  X_TS_TYPE,
} from '@sourceloop/core';
import {authorize} from 'loopback4-authorization';
import {
  AuthRefreshTokenRequest,
  AuthTokenRequest,
  AuthUser,
  LoginRequest,
  TokenResponse,
  CodeResponse,
} from '@sourceloop/authentication-service';
import {
  AuthProxyType,
  CustomerProxyType,
  SellerProxyType,
} from '../datasources/configs';
import * as jwt from 'jsonwebtoken';
import {SellerStatus} from '../enums';
import {ExtendedTokenResponse} from '../models/auth-service/token-response';
import {Customer, Seller} from '../models';
import {CustomerStatus} from '@local/core';
import {CustomerDto} from '../models/ecom-service/dto/customer-dto.model';

export class LoginController {
  constructor(
    @inject(LOGGER.LOGGER_INJECT) public logger: ILogger,
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
  ) {}

  @authorize({permissions: ['*']})
  @post('/auth/login', {
    description:
      'Gets you the code that will be used for getting token (webapps)',
    responses: {
      [STATUS_CODE.OK]: {
        description:
          'Auth Code that you can use to generate access and refresh tokens using the POST /auth/token API',
        content: {
          [CONTENT_TYPE.JSON]: Object,
        },
      },
      ...ErrorCodes,
    },
  })
  async login(
    @requestBody()
    req: LoginRequest,
  ): Promise<CodeResponse> {
    try {
      const result = await this.authProvider.login(req);
      return result;
    } catch (error) {
      this.logger.error('Login request failed', error);
      throw error;
    }
  }

  @authorize({permissions: ['*']})
  @post('/auth/token', {
    description:
      'Send the code received from the POST /auth/login api and get refresh token and access token (webapps)',
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Token Response',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {[X_TS_TYPE]: TokenResponse},
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async getToken(
    @requestBody() req: AuthTokenRequest,
    @param.header.string('x-origin') origin: string,
  ): Promise<ExtendedTokenResponse> {
    try {
      const result = (await this.authProvider.getToken(
        req,
      )) as ExtendedTokenResponse;

      const user = await this.authProvider.getMe(
        `Bearer ${result.accessToken}`,
        origin,
      );

      if (origin === 'ecomdukes-seller') {
        if (user.profileStatus === SellerStatus.INACTIVE) {
          result.message = 'Welcome back! Your account has been reactivated.';
        } else if (!user.vendorId) {
          result.message =
            'Please update your bank details to list your products on the platform.';
        }
      }

      if (origin === 'ecomdukes-customer') {
        if (user.profileStatus === CustomerStatus.INACTIVE) {
          result.message = 'Welcome back! Your account has been reactivated.';
        }
      }

      return result;
    } catch (error) {
      this.logger.error('Token request failed', error);
      throw error;
    }
  }

  @authorize({permissions: ['*']})
  @post('/auth/token-refresh', {
    security: OPERATION_SECURITY_SPEC,
    description:
      'Gets you a new access and refresh token once your access token is expired',
    responses: {
      [STATUS_CODE.OK]: {
        description: 'New Token Response',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {[X_TS_TYPE]: TokenResponse},
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async exchangeToken(
    @requestBody() req: AuthRefreshTokenRequest,
    @param.header.string('device_id') deviceId?: string,
    @param.header.string('Authorization') token?: string,
  ): Promise<TokenResponse> {
    try {
      if (!token) {
        throw new Error('Authorization token is required');
      }
      const result = await this.authProvider.exchangeToken(
        req,
        token,
        deviceId,
      );
      return result;
    } catch (error) {
      this.logger.error('Token refresh failed', error);
      throw error;
    }
  }

  @authorize({permissions: ['*']})
  @get('/auth/me', {
    security: OPERATION_SECURITY_SPEC,
    description: 'To get the user details',
    responses: {
      [STATUS_CODE.OK]: {
        description: 'User Object',
        content: {
          [CONTENT_TYPE.JSON]: AuthUser,
        },
      },
      ...ErrorCodes,
    },
  })
  async me(
    @param.header.string('Authorization') token: string,
    @param.header.string('x-origin') origin: string,
  ): Promise<AuthUser | undefined> {
    try {
      if (!token) {
        throw new Error('Authorization token is required');
      }
      const result = await this.authProvider.getMe(token, origin);
      if (
        origin === 'ecomdukes-seller' &&
        result.profileStatus === SellerStatus.INACTIVE
      ) {
        await this.sellerProxyService.updateById(result.profileId, {
          status: SellerStatus.APPROVED,
        });
      }
      if (
        origin === 'ecomdukes-customer' &&
        result.profileStatus === CustomerStatus.INACTIVE
      ) {
        await this.customerProxy.updateStatusById(
          result.profileId,
          {
            status: CustomerStatus.ACTIVE,
          } as Partial<CustomerDto>,
          token,
        );
      }
      return result;
    } catch (error) {
      this.logger.error('User details request failed', error);
      throw error;
    }
  }

  @authorize({permissions: ['*']})
  @post('/auth/guest', {
    description: 'Generates a token for guest user',
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Token Response for guest',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {[X_TS_TYPE]: TokenResponse},
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async guestLogin(
    @param.header.string('x-origin') xOrigin: string,
  ): Promise<TokenResponse> {
    try {
      const guestUser = {
        permissions: [
          'ViewFacet',
          'ViewProduct',
          'ViewTermsAndCondition',
          'ViewCollection',
          'ViewPrivacyPolicy',
          'ViewFaq',
          'ViewSeller',
          'ViewPageSection',
          'ViewSupport',
          'ViewLegal',
          'ViewEcomdukeService',
        ],
        role: 'Guest',
      };

      if (xOrigin === 'ecomdukes-seller') {
        guestUser.permissions.push(
          'ViewPlanFeatureValue',
          'ViewFeatureValue',
          'ViewPlan',
          'ViewFeature',
        );
      }

      const expiresIn = 24 * 60 * 60;
      const token = jwt.sign(guestUser, process.env.JWT_SECRET as string, {
        expiresIn,
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      });

      return {
        accessToken: token,
        expires: Date.now() + expiresIn * 1000,
      } as TokenResponse;
    } catch (error) {
      this.logger.error('Guest login failed', error);
      throw error;
    }
  }
}
