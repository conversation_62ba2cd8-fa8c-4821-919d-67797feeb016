import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  requestBody,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {ReferralProgram} from '../models/ecom-service/referral-program.model';

const basePath = '/referral-programs';

export class ReferralProgramController {
  constructor(
    @restService(ReferralProgram)
    public referralProgramProxy: ModifiedRestService<ReferralProgram>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateReferralProgram]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ReferralProgram)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ReferralProgram, {
            title: 'NewReferralProgram',
            exclude: ['id'],
          }),
        },
      },
    })
    referralProgram: Omit<ReferralProgram, 'id'>,
  ): Promise<ReferralProgram> {
    return this.referralProgramProxy.create(referralProgram);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferralProgram]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(ReferralProgram) where?: Where<ReferralProgram>,
  ): Promise<Count> {
    return this.referralProgramProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferralProgram]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ReferralProgram model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ReferralProgram, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(ReferralProgram) filter?: Filter<ReferralProgram>,
  ): Promise<ReferralProgram[]> {
    return this.referralProgramProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferralProgram]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ReferralProgram model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(ReferralProgram, {
              includeRelations: true,
            }),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReferralProgram, {exclude: 'where'})
    filter?: FilterExcludingWhere<ReferralProgram>,
  ): Promise<ReferralProgram> {
    return this.referralProgramProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateReferralProgram]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ReferralProgram PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ReferralProgram, {partial: true}),
        },
      },
    })
    referralProgram: Partial<ReferralProgram>,
  ): Promise<void> {
    await this.referralProgramProxy.updateById(id, referralProgram);
  }
}
