// Uncomment these imports to begin using these cool features!

import {PermissionKeys} from '@local/core';
import {get, HttpErrors, param, response} from '@loopback/rest';
import {restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {Payment} from '../models';
import {PaymentProxyType} from '../datasources/configs';

const basePath = 'invoices';

export class InvoiceController {
  constructor(
    @restService(Payment)
    private paymentProxy: PaymentProxyType,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{invoiceId}/download`)
  @response(STATUS_CODE.OK, {
    description: 'Download Zoho Invoice PDF',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadInvoice(
    @param.path.string('invoiceId') invoiceId: string,
    @param.header.string('Authorization') token: string,
  ): Promise<Buffer> {
    const pdfBuffer = await this.paymentProxy.getInvoicesPdf(invoiceId, token);
    if (!pdfBuffer) {
      throw new HttpErrors.InternalServerError(
        'Failed to download invoice PDF',
      );
    }

    return pdfBuffer;
  }
}
