import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {PermissionKeys} from '@local/core';
import {UserNotification} from '../models';

const basePath = '/user-notifications';

export class UserNotificationController {
  constructor(
    @restService(UserNotification)
    private readonly userNotificationService: ModifiedRestService<UserNotification>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.CreateNotification],
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'UserNotification model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(UserNotification)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UserNotification, {
            title: 'NewUserNotification',
            exclude: ['id'],
          }),
        },
      },
    })
    userNotification: Omit<UserNotification, 'id'>,
  ): Promise<UserNotification> {
    return this.userNotificationService.create(userNotification);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.ViewNotification],
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'UserNotification model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(UserNotification) where?: Where<UserNotification>,
  ): Promise<Count> {
    return this.userNotificationService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.ViewNotification],
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of UserNotification model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(UserNotification, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(UserNotification) filter?: Filter<UserNotification>,
  ): Promise<UserNotification[]> {
    return this.userNotificationService.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.UpdateNotification],
  })
  @patch(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'UserNotification PATCH success count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UserNotification, {partial: true}),
        },
      },
    })
    userNotification: UserNotification,
    @param.where(UserNotification) where?: Where<UserNotification>,
  ): Promise<Count> {
    return this.userNotificationService.updateAll(userNotification, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.ViewNotification],
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'UserNotification model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(UserNotification, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserNotification, {exclude: 'where'})
    filter?: FilterExcludingWhere<UserNotification>,
  ): Promise<UserNotification> {
    return this.userNotificationService.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.UpdateNotification],
  })
  @patch(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'UserNotification PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UserNotification, {partial: true}),
        },
      },
    })
    userNotification: UserNotification,
  ): Promise<void> {
    await this.userNotificationService.updateById(id, userNotification);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.UpdateNotification],
  })
  @put(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'UserNotification PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userNotification: UserNotification,
  ): Promise<void> {
    await this.userNotificationService.replaceById(id, userNotification);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.DeleteNotification],
  })
  @del(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'UserNotification DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userNotificationService.deleteById(id);
  }
}
