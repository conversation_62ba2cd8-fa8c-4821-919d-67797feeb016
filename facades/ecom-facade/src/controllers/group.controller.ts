import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {Groups} from '../models/notification/groups.model';

const basePath = '/groups';

export class GroupController {
  constructor(
    @restService(Groups)
    private readonly groupService: ModifiedRestService<Groups>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post(basePath)
  @response(200, {
    description: 'Groups model instance',
    content: {'application/json': {schema: getModelSchemaRef(Groups)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['name'],
            properties: {
              name: {
                type: 'string',
              },
            },
          },
        },
      },
    })
    groups: Omit<Groups, 'id'>,
  ): Promise<Groups> {
    return this.groupService.create(groups);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @get(`${basePath}/count`)
  @response(200, {
    description: 'Groups model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(Groups) where?: Where<Groups>): Promise<Count> {
    return this.groupService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @get(basePath)
  @response(200, {
    description: 'Array of Groups model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Groups, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Groups) filter?: Filter<Groups>): Promise<Groups[]> {
    return this.groupService.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCampaign]})
  @patch(basePath)
  @response(200, {
    description: 'Groups PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Groups,
    @param.where(Groups) where?: Where<Groups>,
  ): Promise<Count> {
    return this.groupService.update(groups, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @get(`${basePath}/{id}`)
  @response(200, {
    description: 'Groups model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Groups, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Groups, {exclude: 'where'})
    filter?: FilterExcludingWhere<Groups>,
  ): Promise<Groups> {
    return this.groupService.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCampaign]})
  @patch(`${basePath}/{id}`)
  @response(204, {
    description: 'Groups PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Groups,
  ): Promise<void> {
    await this.groupService.updateById(id, groups);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCampaign]})
  @put(`${basePath}/{id}`)
  @response(204, {
    description: 'Groups PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groups: Groups,
  ): Promise<void> {
    await this.groupService.replaceById(id, groups);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteCampaign]})
  @del(`${basePath}/{id}`)
  @response(204, {
    description: 'Groups DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupService.deleteById(id);
  }
}
