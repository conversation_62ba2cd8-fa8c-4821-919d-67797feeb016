import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  RestBindings,
  Request,
} from '@loopback/rest';
import {IAuthUserWithTenant, UserFcm} from '../models';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  authenticate,
  AuthenticationBindings,
  STRATEGY,
} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {SubscriptionInput} from '../models/notification/subscription-input.model';
import {Getter, inject} from '@loopback/context';
import {SubscribeProxyType} from '../datasources/configs/subscribe-proxy.config';

export class UserFcmControllerController {
  private token: string;
  constructor(
    @restService(UserFcm)
    public userFcmHelperService: ModifiedRestService<UserFcm>,
    @restService(SubscriptionInput)
    private readonly subscriptionProxyService: SubscribeProxyType,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post('/user-fcms')
  @response(200, {
    description: 'UserFcm model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserFcm)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserFcm, {
            title: 'NewUserFcm',
            exclude: ['id'],
          }),
        },
      },
    })
    userFcm: Omit<UserFcm, 'id'>,
  ): Promise<UserFcm> {
    // For testing purpose , subscribe a topic
    const currentUser = await this.getCurrentUser();
    const fcmTokens = await this.userFcmHelperService.find({
      where: {
        userTenantId: currentUser.userTenantId,
      },
    });

    const fcmToken = fcmTokens[0]?.fcmToken;

    await this.subscriptionProxyService.subscribe(
      {
        topic: 'newUsers',
        email: currentUser.username,
        fcmToken: fcmToken,
        zohoListKey: 'your-zoho-list-key',
        zohoTopicId: 'your-zoho-topic-id',
      },
      this.token,
    );

    return this.userFcmHelperService.create(userFcm);
  }

  @get('/user-fcms/count')
  @response(200, {
    description: 'UserFcm model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(UserFcm) where?: Where<UserFcm>): Promise<Count> {
    return this.userFcmHelperService.count(where);
  }

  @get('/user-fcms')
  @response(200, {
    description: 'Array of UserFcm model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserFcm, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserFcm) filter?: Filter<UserFcm>,
  ): Promise<UserFcm[]> {
    return this.userFcmHelperService.find(filter);
  }

  @patch('/user-fcms')
  @response(200, {
    description: 'UserFcm PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserFcm, {partial: true}),
        },
      },
    })
    userFcm: UserFcm,
    @param.where(UserFcm) where?: Where<UserFcm>,
  ): Promise<Count> {
    return this.userFcmHelperService.update(userFcm, where);
  }

  @get('/user-fcms/{id}')
  @response(200, {
    description: 'UserFcm model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserFcm, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserFcm, {exclude: 'where'})
    filter?: FilterExcludingWhere<UserFcm>,
  ): Promise<UserFcm> {
    return this.userFcmHelperService.findById(id, filter);
  }

  @patch('/user-fcms/{id}')
  @response(204, {
    description: 'UserFcm PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserFcm, {partial: true}),
        },
      },
    })
    userFcm: UserFcm,
  ): Promise<void> {
    await this.userFcmHelperService.updateById(id, userFcm);
  }

  @put('/user-fcms/{id}')
  @response(204, {
    description: 'UserFcm PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userFcm: UserFcm,
  ): Promise<void> {
    await this.userFcmHelperService.replaceById(id, userFcm);
  }

  @del('/user-fcms/{id}')
  @response(204, {
    description: 'UserFcm DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userFcmHelperService.deleteById(id);
  }
}
