import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  patch,
  del,
  requestBody,
  response,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  STATUS_CODE,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';

import {PermissionKeys} from '@local/core';
import {Ticket} from '../models/ecom-service/ticket.model';
import {service} from '@loopback/core';
import {TicketService} from '../services/ticket.service';

const basePath = '/tickets';

export class TicketController {
  constructor(
    @restService(Ticket)
    private readonly ticketProxy: ModifiedRestService<Ticket>,
    @service(TicketService)
    private readonly ticketService: TicketService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSupport]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Ticket model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Ticket)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Ticket, {
            title: 'NewTicket',
            exclude: ['id', 'shortCode'],
          }),
        },
      },
    })
    ticket: Omit<Ticket, 'id'>,
  ): Promise<Ticket> {
    return this.ticketService.createTicket(ticket);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Ticket count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Ticket) where?: Where<Ticket>): Promise<Count> {
    return this.ticketProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Ticket model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Ticket, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Ticket) filter?: Filter<Ticket>): Promise<Ticket[]> {
    return this.ticketProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Ticket model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Ticket, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Ticket, {exclude: 'where'})
    filter?: FilterExcludingWhere<Ticket>,
  ): Promise<Ticket> {
    return this.ticketProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSupport]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Ticket PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Ticket, {partial: true}),
        },
      },
    })
    ticket: Partial<Ticket>,
  ): Promise<void> {
    await this.ticketProxy.updateById(id, ticket);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSupport]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Ticket DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ticketProxy.deleteById(id);
  }
}
