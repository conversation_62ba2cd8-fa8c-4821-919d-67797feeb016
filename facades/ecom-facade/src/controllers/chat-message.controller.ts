import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
  RestBindings,
  Request,
} from '@loopback/rest';
import {ChatMessage} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {inject, service} from '@loopback/core';
import {restService} from '@sourceloop/core';
import {ChatMessageProxyType} from '../datasources/configs/chat-message-proxy.config';
import {ChatMessageService} from '../services/chat-message.service';

const basePath = '/chat-messages';

export class ChatMessageController {
  private token: string;

  constructor(
    @restService(ChatMessage)
    private readonly chatMessageService: ChatMessageProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(ChatMessageService)
    private readonly chatMessagesService: ChatMessageService,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateChatMessage]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ChatMessage)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {
            title: 'NewChatMessage',
            exclude: ['id', 'senderId', 'senderType'],
          }),
        },
      },
    })
    chatMessage: Omit<ChatMessage, 'id'>,
  ): Promise<ChatMessage> {
    return this.chatMessagesService.createMessageWithCurrentUser(chatMessage);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ChatMessage) where?: Where<ChatMessage>,
  ): Promise<Count> {
    return this.chatMessageService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ChatMessage model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChatMessage, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ChatMessage) filter?: Filter<ChatMessage>,
  ): Promise<ChatMessage[]> {
    return this.chatMessageService.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ChatMessage, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ChatMessage, {exclude: 'where'})
    filter?: FilterExcludingWhere<ChatMessage>,
  ): Promise<ChatMessage> {
    return this.chatMessageService.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChatMessage]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ChatMessage PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {partial: true}),
        },
      },
    })
    chatMessage: ChatMessage,
  ): Promise<void> {
    await this.chatMessageService.updateById(id, chatMessage);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteChatMessage]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ChatMessage DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.chatMessageService.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get('{basePath}/my')
  @response(STATUS_CODE.OK, {
    description: 'Array of ChatMessage model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChatMessage, {includeRelations: true}),
        },
      },
    },
  })
  async getMyMessages(): Promise<ChatMessage[]> {
    return this.chatMessagesService.findMessagesBySeller();
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChatMessage]})
  @patch(`${basePath}/mark-read/{chatId}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ChatMessage PATCH success',
  })
  async markAsRead(@param.path.string('chatId') chatId: string): Promise<void> {
    const currentUser = await this.chatMessagesService.getCurrentUsers();
    await this.chatMessagesService.markMessagesAsRead(
      chatId,
      currentUser.userTenantId ?? '',
    );
  }
}
