import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DukeCoin} from '../models';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {DukeCoinService} from '../services/duke-coin.service';
import {service} from '@loopback/core';
import {DukeCoinProxyType} from '../datasources/configs/duke-coin-proxy.config';

const basePath = '/duke-coins';

export class DukeCoinController {
  constructor(
    @service(DukeCoinService)
    private readonly dukeCoinService: DukeCoinService,
    @restService(DukeCoin)
    private readonly dukeProxy: DukeCoinProxyType,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateDukeCoin]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'DukeCoin model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(DukeCoin)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(DukeCoin, {
            title: 'NewDukeCoin',
            exclude: ['id', 'userTenantId'],
            partial: true,
          }),
        },
      },
    })
    dukeCoin: Partial<DukeCoin>,
    @param.header.string('Authorization') token: string,
  ): Promise<DukeCoin> {
    return this.dukeCoinService.createDukeCoin(dukeCoin, token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'DukeCoin model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(DukeCoin) where?: Where<DukeCoin>): Promise<Count> {
    return this.dukeProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of DukeCoin model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DukeCoin, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(DukeCoin) filter?: Filter<DukeCoin>,
  ): Promise<DukeCoin[]> {
    return this.dukeProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'DukeCoin model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(DukeCoin, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DukeCoin, {exclude: 'where'})
    filter?: FilterExcludingWhere<DukeCoin>,
  ): Promise<DukeCoin> {
    return this.dukeProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'DukeCoin PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(DukeCoin, {partial: true}),
        },
      },
    })
    dukeCoin: Partial<DukeCoin>,
  ): Promise<void> {
    await this.dukeProxy.updateById(id, dukeCoin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'DukeCoin PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() dukeCoin: DukeCoin,
  ): Promise<void> {
    await this.dukeProxy.replaceById(id, dukeCoin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteDukeCoin]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'DukeCoin DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.dukeProxy.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @post(`${basePath}/users/{id}/coins/add`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Coins added',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                balance: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  async addCoins(
    @param.path.string('id') userId: string,
    @requestBody() body: {amount: number; description?: string},
    @param.header.string('Authorization') token?: string,
  ): Promise<{balance: number}> {
    if (body.amount <= 0) throw new Error('Amount must be positive.');
    return this.dukeProxy.addCoins(
      userId,
      {amount: body.amount ?? 0, description: body.description ?? ''},
      token ?? '',
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(`${basePath}/users/coins/balance`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'User coin balance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                balance: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  async getCoinBalance(
    // @param.path.string('id') userId: string,
    @param.header.string('Authorization') token: string,
  ): Promise<{balance: number}> {
    return this.dukeProxy.getCoinBalance(token);
  }
}
