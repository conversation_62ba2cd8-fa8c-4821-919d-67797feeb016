import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  RestBindings,
  Request,
} from '@loopback/rest';
import {
  Product,
  ProductDto,
  ProductOptionGroupDto,
  ProductVariant,
  ProductVariantWithRelations,
  ProductWithRelations,
  BulkStatusUpdateDto,
  SingleStatusUpdateDto,
} from '../models';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {ProductProxyType} from '../datasources/configs';
import {inject} from '@loopback/context';
import {ProductService, ProductVariantService} from '../services';
import {service} from '@loopback/core';
const basePath = '/products';
export class ProductController {
  private token: string;
  constructor(
    @restService(Product)
    private readonly productProxy: ProductProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(ProductService)
    private readonly productService: ProductService,
    @service(ProductVariantService)
    private readonly productVariantHelperService: ProductVariantService,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Product)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductDto, {
            title: 'NewProduct',
            exclude: ['id', 'slug'],
          }),
        },
      },
    })
    product: Omit<ProductDto, 'id' | 'slug'>,
  ): Promise<Product> {
    const createdProduct = await this.productProxy.createProduct(
      product,
      this.token,
    );
    // eslint-disable-next-line no-void
    void this.productService.updateVariantsWithZohoItemId(
      createdProduct?.id ?? '',
      this.token,
    );

    return createdProduct;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Product) where?: Where<Product>): Promise<Count> {
    return this.productProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Product model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Product, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.header.string('x-origin') xOrigin: string,
    @param.header.string('Authorization') token?: string,
    @param.filter(Product) filter?: Filter<Product>,
  ): Promise<Product[]> {
    filter = await this.productService.applySellerFilter(
      xOrigin,
      token,
      filter,
    );
    const products = await this.productProxy.find(filter);
    let filteredProducts = products;
    if (xOrigin === 'ecomdukes-customer') {
      filteredProducts =
        await this.productService.filterOutInactiveSellerProducts(
          products as ProductWithRelations[],
        );
    }

    return this.productService.getAllProductsWithPreviewUrl(
      filteredProducts as ProductWithRelations[],
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Product model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Product, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Product, {exclude: 'where'})
    filter?: FilterExcludingWhere<Product>,
  ): Promise<Product> {
    const product = (await this.productProxy.findById(
      id,
      filter,
    )) as ProductWithRelations;
    return this.productService.getProductWithPresignedUrl(product);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductDto, {partial: true}),
        },
      },
    })
    product: Partial<ProductDto>,
  ): Promise<void> {
    await this.productProxy.updateProductById(id, product, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @put(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() product: Product,
  ): Promise<void> {
    await this.productProxy.replaceById(id, product);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteProduct]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.productProxy.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @post(`${basePath}/{id}/option-group`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product option creation success',
      },
    },
  })
  async createProductOptionGroup(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductOptionGroupDto, {
            title: 'ProductOptions',
            exclude: ['id', 'code', 'productId'],
          }),
        },
      },
    })
    productOption: Omit<ProductOptionGroupDto, 'id' | 'code' | 'productId'>,
  ): Promise<void> {
    return this.productProxy.createOptionGroupByProductId(
      id,
      productOption,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @patch(`${basePath}/{id}/option-group/{groupId}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product option update success',
      },
    },
  })
  async updateProductOptionGroup(
    @param.path.string('id') id: string,
    @param.path.string('groupId') groupId: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductOptionGroupDto, {
            title: 'ProductOptions Update',
            partial: true,
          }),
        },
      },
    })
    productOption: Partial<ProductOptionGroupDto>,
  ): Promise<void> {
    return this.productProxy.updateOptionGroupByProductId(
      id,
      groupId,
      productOption,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/recently-viewed`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Recently viewed products',
      },
    },
  })
  async getRecentlyViewedProducts(
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]> {
    const recentlyViewed = await this.productProxy.getRecentlyViewedProducts(
      this.token,
      filter,
    );

    return this.productVariantHelperService.getProductVariantsWithFeaturedPreviewUrls(
      recentlyViewed as ProductVariantWithRelations[],
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/most-viewed`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Most viewed products',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async getMostViewedProducts(
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]> {
    const mostViewedProducts = await this.productProxy.getMostViewedProducts(
      this.token,
      filter,
    );

    return this.productVariantHelperService.getProductVariantsWithFeaturedPreviewUrls(
      mostViewedProducts as ProductVariantWithRelations[],
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}//top-selling`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Most viewed products',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async getTopSellingProducts(
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]> {
    const topSellingProducts = await this.productProxy.getTopSellingProducts(
      this.token,
      filter,
    );
    return this.productVariantHelperService.getProductVariantsWithFeaturedPreviewUrls(
      topSellingProducts as ProductVariantWithRelations[],
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/bulk-status-update`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Bulk product status update success',
      },
    },
  })
  async bulkStatusUpdate(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BulkStatusUpdateDto),
        },
      },
    })
    bulkUpdate: BulkStatusUpdateDto,
  ): Promise<void> {
    await this.productService.bulkStatusUpdate(bulkUpdate);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/{id}/status`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Product status update success',
      },
    },
  })
  async updateProductStatus(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SingleStatusUpdateDto),
        },
      },
    })
    statusUpdate: SingleStatusUpdateDto,
  ): Promise<void> {
    await this.productService.updateProductStatus(id, statusUpdate);
  }
}
