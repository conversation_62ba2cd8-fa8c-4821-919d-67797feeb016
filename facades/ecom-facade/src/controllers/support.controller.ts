import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  patch,
  del,
  requestBody,
  response,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  STATUS_CODE,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';

import {PermissionKeys} from '@local/core';
import {Support} from '../models/ecom-service/support.model';

const basePath = '/support';

export class SupportController {
  constructor(
    @restService(Support)
    private readonly supportProxy: ModifiedRestService<Support>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSupport]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Support model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Support)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Support, {
            title: 'NewSupport',
            exclude: ['id', 'createdOn', 'modifiedOn', 'deleted'],
          }),
        },
      },
    })
    support: Omit<Support, 'id'>,
  ): Promise<Support> {
    return this.supportProxy.create(support);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Support count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Support) where?: Where<Support>): Promise<Count> {
    return this.supportProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Support model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Support, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Support) filter?: Filter<Support>,
  ): Promise<Support[]> {
    return this.supportProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Support model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Support, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Support, {exclude: 'where'})
    filter?: FilterExcludingWhere<Support>,
  ): Promise<Support> {
    return this.supportProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSupport]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Support PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Support, {partial: true}),
        },
      },
    })
    support: Partial<Support>,
  ): Promise<void> {
    await this.supportProxy.updateById(id, support);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSupport]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Support DELETE (soft delete) success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.supportProxy.deleteById(id);
  }
}
