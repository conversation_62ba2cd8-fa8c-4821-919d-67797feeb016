import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
  RestBindings,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {SellerShippingProfile} from '../models/ecom-service/seller-shipping-profile.model';
import {STATUS_CODE, CONTENT_TYPE, restService} from '@sourceloop/core';
import {inject} from '@loopback/core';
import {Request} from 'express';
import {SellerShippingProfileProxyType} from '../datasources/configs/seller-shipping-profile-proxy.config';
import {SellerShippingProfileDto} from '../models';

const basePath = '/seller-shipping-profiles';

export class SellerShippingProfileController {
  private token: string;

  constructor(
    @restService(SellerShippingProfile)
    private readonly sellerShippingProfileProxy: SellerShippingProfileProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile model instance with related entities',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(SellerShippingProfile)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingProfileDto, {
            title: 'NewSellerShippingProfile',
          }),
        },
      },
    })
    sellerShippingProfileDto: SellerShippingProfileDto,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingProfileProxy.createShippingProfile(
      sellerShippingProfileDto,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerShippingProfile) where?: Where<SellerShippingProfile>,
  ): Promise<Count> {
    return this.sellerShippingProfileProxy.count(where, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerShippingProfile model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerShippingProfile, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerShippingProfile) filter?: Filter<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]> {
    return this.sellerShippingProfileProxy.find(filter, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/seller/{sellerId}`)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerShippingProfile model instances for a seller',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerShippingProfile, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async findBySellerId(
    @param.path.string('sellerId') sellerId: string,
    @param.filter(SellerShippingProfile, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]> {
    return this.sellerShippingProfileProxy.getSellerShippingProfiles(
      sellerId,
      this.token,
      filter,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile PATCH success',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerShippingProfile, {
          includeRelations: true,
        }),
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingProfileDto, {partial: true}),
        },
      },
    })
    sellerShippingProfileDto: Partial<SellerShippingProfileDto>,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingProfileProxy.updateShippingProfile(
      id,
      sellerShippingProfileDto,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile model instance with all relations',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerShippingProfile, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingProfileProxy.getShippingProfileWithRelations(
      id,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerShippingProfile DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerShippingProfileProxy.deleteById(id, this.token);
  }
}
