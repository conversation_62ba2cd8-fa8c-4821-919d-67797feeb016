import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  get,
  post,
  patch,
  del,
  param,
  response,
  requestBody,
  getModelSchemaRef,
} from '@loopback/rest';
import {service} from '@loopback/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';

import {
  Customer,
  CustomerWithRelations,
  CustomerStatusUpdateDto,
} from '../models';
import {CustomerDto} from '../models/ecom-service/dto/customer-dto.model';
import {CustomerService} from '../services';
import {CustomerProxyType} from '../datasources/configs/customer-proxy.config';

import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {PermissionKeys} from '@local/core';
import {CustomerAddressService} from '../services/customer-address.service';

const basePath = '/customers';

function mapDtoToCustomer(value: CustomerDto): Customer {
  return new Customer({
    ...value,
    customerId: value.id ?? '',
    userTenantId: '',
  });
}

export class CustomerController {
  constructor(
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
    @service(CustomerService)
    private readonly customerService: CustomerService,
    @service(CustomerAddressService)
    private readonly customerAddressHelperService: CustomerAddressService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCustomer]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Customer model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(CustomerDto)}}, // Use CustomerDto schema
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CustomerDto, {
            title: 'NewCustomer',
            exclude: ['id', 'username'],
          }),
        },
      },
    })
    customer: Omit<CustomerDto, 'id'>,
  ): Promise<CustomerDto> {
    return this.customerService.createCustomer(customer);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCustomer]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Customer model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Customer) where?: Where<Customer>): Promise<Count> {
    return this.customerProxy.count(where as Where<CustomerDto>);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCustomer]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Customer model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Customer),
        },
      },
    },
  })
  async find(
    @param.filter(Customer) filter?: Filter<Customer>,
  ): Promise<Customer[]> {
    const dtos = await this.customerProxy.find(filter as Filter<CustomerDto>);
    return dtos.map(mapDtoToCustomer);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCustomer]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Customer model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Customer, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Customer, {exclude: 'where'})
    filter?: FilterExcludingWhere<Customer>,
  ): Promise<CustomerWithRelations> {
    const {modifiedFilter, hasAddressRelation} =
      this.customerAddressHelperService.modifyFilterForAddressHandling(filter);

    const dto = await this.customerProxy.findById(
      id,
      modifiedFilter as FilterExcludingWhere<CustomerDto>,
    );

    const customer = mapDtoToCustomer(dto) as CustomerWithRelations;

    const customerWithAddressData =
      await this.customerAddressHelperService.getCustomerWithAddresses(
        customer,
      );

    if (hasAddressRelation) {
      return customerWithAddressData;
    }

    return customer;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCustomer]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Customer PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CustomerDto, {partial: true}),
        },
      },
    })
    customer: CustomerDto,
  ): Promise<void> {
    await this.customerService.updateCustomer(id, customer);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteCustomer]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Customer DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.customerProxy.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCustomer]})
  @patch(`${basePath}/{id}/status`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Customer status update success',
  })
  async updateCustomerStatus(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CustomerStatusUpdateDto),
        },
      },
    })
    statusUpdate: CustomerStatusUpdateDto,
  ): Promise<void> {
    await this.customerService.updateCustomerStatus(id, statusUpdate);
  }
}
