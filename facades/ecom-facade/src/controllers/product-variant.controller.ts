import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  RestBindings,
  Request,
} from '@loopback/rest';
import {
  ProductVariant,
  ProductVariantUpdateDto,
  ProductVariantWithRelations,
} from '../models';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys, ProductStatus} from '@local/core';
import {inject, service} from '@loopback/core';
import {ProductVariantService} from '../services';
import {ProductVariantProxyType} from '../datasources/configs';

const basePath = '/product-variants';

export class ProductVariantController {
  private token: string;
  constructor(
    @restService(ProductVariant)
    private readonly productVariantService: ProductVariantProxyType,
    @service(ProductVariantService)
    private readonly productVariantHelperService: ProductVariantService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateProduct]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ProductVariant model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ProductVariant)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductVariant, {
            title: 'NewProductVariant',
            exclude: ['id'],
          }),
        },
      },
    })
    productVariant: Omit<ProductVariant, 'id'>,
  ): Promise<ProductVariant> {
    return this.productVariantService.create(productVariant);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ProductVariant model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(ProductVariant) where?: Where<ProductVariant>,
  ): Promise<Count> {
    return this.productVariantService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ProductVariant model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.header.string('x-origin') xOrigin: string,
    @param.header.string('Authorization') token?: string,
    @param.filter(ProductVariant) filter?: Filter<ProductVariant>,
  ): Promise<ProductVariant[]> {
    filter = await this.productVariantHelperService.applySellerFilter(
      xOrigin,
      token,
      filter,
    );

    const productVariants = await this.productVariantService.find(filter);
    const productVariantsWithPreviews = await Promise.all(
      (productVariants as ProductVariantWithRelations[]).map(variant =>
        this.productVariantHelperService.getProductVariantWithPresignedUrl(
          variant,
        ),
      ),
    );

    return productVariantsWithPreviews;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ProductVariant model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(ProductVariant, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.header.string('x-origin') xOrigin?: string,
    @param.filter(ProductVariant, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant> {
    // Apply customer filtering if needed
    let modifiedFilter = filter;
    if (xOrigin === 'ecomdukes-customer') {
      modifiedFilter = {
        ...filter,
        include: [
          ...(filter?.include || []),
          {
            relation: 'product',
            scope: {
              where: {status: ProductStatus.APPROVED},
            },
          },
        ],
      };
    }

    const {
      modifiedFilter: finalFilter,
      hasSellerRelation,
      hasCustomerRelation,
    } = this.productVariantHelperService.modifyFilterForSensitiveRelations(
      modifiedFilter,
    );

    const productVariant = (await this.productVariantService.findById(
      id,
      finalFilter,
    )) as ProductVariantWithRelations;

    const productVariantWithPresignedUrl =
      await this.productVariantHelperService.getProductVariantWithPresignedUrl(
        productVariant,
      );

    // Add customer and/or seller relations conditionally
    let finalVariant = productVariantWithPresignedUrl;

    if (hasCustomerRelation) {
      finalVariant =
        await this.productVariantHelperService.getProductVariantWithCustomer(
          finalVariant,
        );
    }

    if (hasSellerRelation) {
      finalVariant =
        await this.productVariantHelperService.getProductVariantWithSeller(
          finalVariant,
        );
    }

    return finalVariant;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateProduct]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ProductVariant PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductVariantUpdateDto, {partial: true}),
        },
      },
    })
    productVariant: ProductVariantUpdateDto,
  ): Promise<void> {
    await this.productVariantService.updateProductVariantById(
      id,
      productVariant,
      this.token,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteProduct]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ProductVariant DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.productVariantService.deleteById(id);
  }
}
