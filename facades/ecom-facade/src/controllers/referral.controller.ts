import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  requestBody,
  RestBindings,
  RequestContext,
} from '@loopback/rest';
import {inject, service} from '@loopback/core';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {Referral} from '../models/ecom-service/referral.model';
import {ReferralHelperService} from '../services/referral-helper.service';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';

const basePath = '/referrals';

export class ReferralController {
  constructor(
    @restService(Referral)
    public referralProxy: ModifiedRestService<Referral>,
    @service(ReferralHelperService)
    public referralHelperService: ReferralHelperService,
    @inject(RestBindings.Http.CONTEXT)
    private requestContext: RequestContext,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateReferral]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Referral model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Referral)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Referral, {
            title: 'NewReferral',
            exclude: ['id', 'referralCode', 'status', 'type'],
          }),
        },
      },
    })
    referral: Omit<Referral, 'id' | 'referralCode'>,
  ): Promise<Referral> {
    const xOrigin = this.requestContext.request.headers['x-origin'] as string;
    const newReferral = this.referralHelperService.createNewReferral(
      referral,
      xOrigin,
    );
    return this.referralProxy.create(newReferral);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferral]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Referral count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Referral) where?: Where<Referral>): Promise<Count> {
    return this.referralProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferral]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Referral model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Referral, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Referral) filter?: Filter<Referral>,
  ): Promise<Referral[]> {
    return this.referralProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewReferral]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Referral model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Referral, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Referral, {exclude: 'where'})
    filter?: FilterExcludingWhere<Referral>,
  ): Promise<Referral> {
    return this.referralProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateReferral]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Referral PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Referral, {partial: true}),
        },
      },
    })
    referral: Partial<Referral>,
  ): Promise<void> {
    await this.referralProxy.updateById(id, referral);
  }
}
