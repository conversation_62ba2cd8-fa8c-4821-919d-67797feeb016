import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Ecomdukeservice} from './ecomdukeservice.model';
import {Seller, SellerWithRelations} from '../auth-service/seller.model';

@model({name: 'ecomdukeservice_requests'})
export class EcomdukeserviceRequest extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @belongsTo(() => Seller, {keyTo: 'id'}, {name: 'seller_id'})
  sellerId: string;

  @property({
    type: 'string',
    required: true,
  })
  status: string;

  @property({
    type: 'number',
    name: 'payment_reference',
  })
  paymentReference?: number;

  @property({
    type: 'number',
    name: 'paid_amount',
  })
  paidAmount?: number;

  @property({
    type: 'date',
    name: 'paid_on',
  })
  paidOn?: string;

  @property({
    type: 'string',
  })
  notes?: string;

  @belongsTo(() => Ecomdukeservice, {keyTo: 'id'}, {name: 'service_id'})
  ecomdukeserviceId: string;

  constructor(data?: Partial<EcomdukeserviceRequest>) {
    super(data);
  }
}

export interface EcomDukeserviceRequestRelations {
  seller?: SellerWithRelations; // describe navigational properties here
}

export type EcomDukeserviceRequestWithRelations = EcomdukeserviceRequest &
  EcomDukeserviceRequestRelations;
