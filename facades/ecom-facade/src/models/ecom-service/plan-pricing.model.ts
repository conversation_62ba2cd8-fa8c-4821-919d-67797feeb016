import {model, property, belongsTo} from '@loopback/repository';
import {Plan} from './plan.model';
import {UserModifiableEntity} from '@sourceloop/core';

@model({settings: {strict: false}, name: 'plan_pricing'})
export class PlanPricing extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @belongsTo(() => Plan, {name: 'plan_id'}, {name: 'plan_id'})
  planId: string;

  @property({
    type: 'number',
    required: true,
    name: 'min_sales_threshold',
  })
  minSalesThreshold: number;

  @property({
    type: 'number',
    required: true,
    name: 'max_sales_threshold',
  })
  maxSalesThreshold: number;

  @property({
    type: 'number',
    required: true,
  })
  price: number;

  constructor(data?: Partial<PlanPricing>) {
    super(data);
  }
}

export interface PlanPricingRelations {}

export type PlanPricingWithRelations = PlanPricing & PlanPricingRelations;
