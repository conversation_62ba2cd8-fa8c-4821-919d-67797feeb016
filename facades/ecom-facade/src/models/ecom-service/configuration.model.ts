import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ConfigurationLabel} from '../../constants';

@model({settings: {strict: false}, name: 'configurations'})
export class Configuration extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  key: string;

  @property({
    type: 'string',
    required: false,
  })
  label?: ConfigurationLabel;

  @property({
    type: 'string',
    required: true,
  })
  value: string;

  constructor(data?: Partial<Configuration>) {
    super(data);
  }
}

export interface ConfigurationRelations {
  // describe navigational properties here
}

export type ConfigurationWithRelations = Configuration & ConfigurationRelations;
