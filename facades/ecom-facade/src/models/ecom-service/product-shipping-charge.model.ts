import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';
import {Product} from './product.model';
import {ProductVariant} from './product-variant.model';

@model({
  name: 'product_shipping_charges',
})
export class ProductShippingCharge extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'boolean',
    default: true,
    name: 'override_default',
  })
  overrideDefault?: boolean;

  @property({
    type: 'number',
    required: true,
    name: 'base_charge',
  })
  baseCharge: number;

  @property({
    type: 'number',
    default: 0,
    name: 'additional_charge',
  })
  additionalCharge?: number;

  @property({
    type: 'number',
    name: 'free_shipping_threshold',
  })
  freeShippingThreshold?: number;

  @belongsTo(() => Product, {keyTo: 'id'}, {name: 'product_id'})
  productId: string;

  @belongsTo(() => ProductVariant, {keyTo: 'id'}, {name: 'product_variant_id'})
  productVariantId?: string;

  @belongsTo(
    () => SellerShippingProfile,
    {keyTo: 'id'},
    {name: 'shipping_profile_id'},
  )
  shippingProfileId: string;

  constructor(data?: Partial<ProductShippingCharge>) {
    super(data);
  }
}

export interface ProductShippingChargeRelations {
  product?: Product;
  productVariant?: ProductVariant;
  shippingProfile?: SellerShippingProfile;
}

export type ProductShippingChargeWithRelations = ProductShippingCharge &
  ProductShippingChargeRelations;
