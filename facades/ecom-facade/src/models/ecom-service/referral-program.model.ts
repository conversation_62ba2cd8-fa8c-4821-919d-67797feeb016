import {ReferralType} from '@local/core';
import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({
  settings: {strict: false},
  name: 'referral_programs',
})
export class ReferralProgram extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'name',
  })
  name: string;

  @property({
    type: 'string',
    required: true,
    name: 'type',
  })
  type: ReferralType;

  @property({
    type: 'number',
    required: true,
    name: 'referrer_coins',
  })
  referrerCoins: number;

  @property({
    type: 'number',
    required: true,
    name: 'referee_coins',
  })
  refereeCoins: number;

  @property({
    type: 'number',
    required: true,
    name: 'min_eligibility_order_price',
  })
  minEligibilityOrderPrice: number;

  constructor(data?: Partial<ReferralProgram>) {
    super(data);
  }
}

export interface ReferralProgramRelations {
  // describe navigational properties here
}

export type ReferralProgramWithRelations = ReferralProgram &
  ReferralProgramRelations;
