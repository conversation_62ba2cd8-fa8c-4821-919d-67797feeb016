import {TransactionType} from '@local/core';
import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Referral} from './referral.model';

@model({name: 'duke_coins'})
export class DukeCoin extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'user_tenant_id',
  })
  userTenantId: string;

  @property({
    type: 'number',
    required: true,
  })
  coins: number;

  @belongsTo(() => Referral, {keyTo: 'id'}, {name: 'referral_id'})
  referralId: string;

  @property({
    type: 'string',
    name: 'transaction_type',
  })
  transactionType: TransactionType;

  @property({
    type: 'number',
    required: true,
    name: 'coins_changed',
  })
  coinsChanged: number;

  @property({
    type: 'string',
  })
  description?: string;

  constructor(data?: Partial<DukeCoin>) {
    super(data);
  }
}

export interface DukeCoinRelations {
  // describe navigational properties here
}

export type DukeCoinWithRelations = DukeCoin & DukeCoinRelations;
