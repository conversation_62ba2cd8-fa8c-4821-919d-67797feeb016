import {model, property, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ChatMessage} from './chat-message.model';
import {ChatStatus} from '@local/core';
import {CustomerWithRelations} from '../auth-service/customer.model';
import {SellerWithRelations} from '../auth-service/seller.model';

@model()
export class Chat extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @property({
    type: 'string',
    required: true,
    name: 'seller_id',
  })
  sellerId: string;

  @property({
    type: 'string',
    jsonSchema: {
      enum: Object.values(ChatStatus),
    },
    default: ChatStatus.ACTIVE,
  })
  status?: string;

  @hasMany(() => ChatMessage)
  messages?: ChatMessage[];

  constructor(data?: Partial<Chat>) {
    super(data);
  }
}
export interface ChatRelations {
  customer?: CustomerWithRelations;
  seller?: SellerWithRelations;
  messages?: ChatMessage[];
}

export type ChatWithRelations = Chat & ChatRelations;
