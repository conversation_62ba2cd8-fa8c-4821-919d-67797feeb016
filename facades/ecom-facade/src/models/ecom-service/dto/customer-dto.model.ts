import {model, property} from '@loopback/repository';
import {User} from '@sourceloop/authentication-service';
import {Address} from '../address.model';

@model()
export class CustomerDto extends User {
  @property({
    type: 'array',
    itemType: 'object',
    required: true,
  })
  addresses: Address[];

  @property({
    type: 'string',
  })
  userTenantId?: string;

  constructor(data?: Partial<CustomerDto>) {
    super(data);
  }
}

export interface CustomerDtoRelations {}

export type CustomerDtoWithRelations = CustomerDto & CustomerDtoRelations;
