import {model, property} from '@loopback/repository';
import {Product} from '../product.model';
import {OptionDto} from './option-dto.model';
import {ProductBoxContent} from '../product-box-content.model';
import {ProductDetail} from '../product-detail.model';
import {ProductDisclaimer} from '../product-disclaimer.model';
import {ProductReturnPolicy} from '../product-return-policy.model';
import {ProductSpecification} from '../product-speicification.model';
import {ProductSuitability} from '../product-suitability.model';
import {ProductUniqueness} from '../product-uniqueness.model';
import {ProductTermsAndCondition} from '../product-terms-and-condition.model';
import {OptionVariantDto} from './option-variant-dto.model';
import {ProductMoreInfo} from '../product-more-info.model';
import {ProductCustomizationField} from '../product-customization-field.model';
import {ProductPersonalWork} from '../product-personal-work.model';

@model()
export class ProductDto extends Product {
  @property({
    type: 'array',
    itemType: OptionVariantDto,
  })
  variants?: OptionVariantDto[];

  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  assets: string[];

  @property({
    type: 'array',
    itemType: OptionDto,
    required: true,
  })
  options?: OptionDto[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  facets?: string[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  boxContents?: ProductBoxContent[];

  @property({
    type: ProductDetail,
  })
  details?: ProductDetail;

  @property({
    type: ProductDisclaimer,
  })
  disclaimer?: ProductDisclaimer;

  @property({
    type: ProductMoreInfo,
  })
  moreInfo?: ProductMoreInfo;

  @property({
    type: ProductReturnPolicy,
  })
  returnPolicy?: ProductReturnPolicy;

  @property({
    type: 'array',
    itemType: 'object',
  })
  specifications?: ProductSpecification[];

  @property({
    type: ProductSuitability,
  })
  suitability?: ProductSuitability;

  @property({
    type: ProductTermsAndCondition,
  })
  terms?: ProductTermsAndCondition;

  @property({
    type: ProductUniqueness,
  })
  uniqueness?: ProductUniqueness;

  @property({
    type: ProductPersonalWork,
  })
  personalWork?: ProductPersonalWork;

  @property({
    type: 'array',
    itemType: 'object',
  })
  customizations?: ProductCustomizationField[];

  @property({
    type: 'string',
    required: true,
    default: 'PENDING',
  })
  status: string;

  @property({
    type: 'string',
    required: false,
  })
  rejectedReason?: string;

  @property({
    type: 'number',
    required: false,
  })
  averageWeight?: number;

  constructor(data?: Partial<ProductDto>) {
    super(data);
  }
}
