import {model, property} from '@loopback/repository';
import {ProductStatus} from '@local/core';

@model()
export class BulkStatusUpdateDto {
  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  productIds: string[];

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(ProductStatus),
    },
  })
  status: ProductStatus;

  @property({
    type: 'string',
    required: false,
  })
  rejectedReason?: string;

  constructor(data?: Partial<BulkStatusUpdateDto>) {
    Object.assign(this, data);
  }
}

@model()
export class SingleStatusUpdateDto {
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(ProductStatus),
    },
  })
  status: ProductStatus;

  @property({
    type: 'string',
    required: false,
  })
  rejectedReason?: string;

  constructor(data?: Partial<SingleStatusUpdateDto>) {
    Object.assign(this, data);
  }
}
