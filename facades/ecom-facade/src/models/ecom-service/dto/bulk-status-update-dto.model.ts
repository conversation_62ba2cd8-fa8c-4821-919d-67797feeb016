import {model, property} from '@loopback/repository';

@model()
export class BulkStatusUpdateDto {
  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  productIds: string[];

  @property({
    type: 'string',
    required: true,
  })
  status: string;

  @property({
    type: 'string',
    required: false,
  })
  rejectedReason?: string;

  constructor(data?: Partial<BulkStatusUpdateDto>) {
    Object.assign(this, data);
  }
}

@model()
export class SingleStatusUpdateDto {
  @property({
    type: 'string',
    required: true,
  })
  status: string;

  @property({
    type: 'string',
    required: false,
  })
  rejectedReason?: string;

  constructor(data?: Partial<SingleStatusUpdateDto>) {
    Object.assign(this, data);
  }
}
