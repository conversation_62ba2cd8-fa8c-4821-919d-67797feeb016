import {
  model,
  property,
  belongsTo,
  hasMany,
  hasOne,
} from '@loopback/repository';
import {Asset} from './asset.model';
import {UserModifiableEntity} from '@sourceloop/core';
import {ProductSpecification} from './product-speicification.model';
import {ProductDetail} from './product-detail.model';
import {ProductMoreInfo} from './product-more-info.model';
import {ProductDisclaimer} from './product-disclaimer.model';
import {ProductReturnPolicy} from './product-return-policy.model';
import {ProductSuitability} from './product-suitability.model';
import {ProductTermsAndCondition} from './product-terms-and-condition.model';
import {ProductBoxContent} from './product-box-content.model';
import {ProductUniqueness} from './product-uniqueness.model';
import {ProductPersonalWork} from './product-personal-work.model';
import {ProductVariant} from './product-variant.model';
import {Collection} from './collection.model';
import {ProductCustomizationField} from './product-customization-field.model';
import {ProductAsset} from './product-asset.model';
import {AssetResponseDto} from './asset-response-dto.model';
import {TaxCategory} from './tax-category.model';
import {FacetValue} from './facet-value.model';
import {ProductFacetValue} from './product-facet-value.model';
import {Seller} from '../auth-service';

@model({name: 'products'})
export class Product extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  description: string;

  @property({
    type: 'string',
    required: false,
    name: 'product_id',
  })
  productId: string;

  @property({
    type: 'string',
    required: true,
  })
  slug: string;

  @property({
    type: 'boolean',
    required: true,
  })
  enabled: boolean;

  @property({
    type: 'boolean',
    name: 'is_gift_wrap_available',
    default: false,
    required: true,
  })
  isGiftWrapAvailable: boolean;

  @property({
    type: 'number',
    name: 'is_gift_wrap_charge',
    default: false,
  })
  isGiftWrapCharge?: number;

  @property({
    type: 'string',
    required: true,
    default: 'PENDING',
  })
  status: string;

  @property({
    type: 'string',
    required: false,
    name: 'rejected_reason',
  })
  rejectedReason?: string;

  @property({
    type: 'number',
    required: false,
    name: 'average_weight',
  })
  averageWeight?: number;

  @belongsTo(() => Seller, {keyTo: 'id'}, {name: 'seller_id'})
  sellerId: string;

  @belongsTo(() => Asset, {keyTo: 'id'}, {name: 'featured_asset_id'})
  featuredAssetId: string;

  @belongsTo(() => TaxCategory, {keyTo: 'id'}, {name: 'tax_category_id'})
  taxCategoryId: string;

  @hasMany(() => ProductSpecification, {keyTo: 'productId'})
  productSpecifications: ProductSpecification[];

  @hasMany(() => ProductBoxContent, {keyTo: 'productId'})
  productBoxContents: ProductBoxContent[];

  @hasOne(() => ProductDetail, {keyTo: 'productId'})
  productDetail: ProductDetail;

  @hasOne(() => ProductDisclaimer, {keyTo: 'productId'})
  productDisclaimer: ProductDisclaimer;

  @hasOne(() => ProductReturnPolicy, {keyTo: 'productId'})
  productReturnPolicy: ProductReturnPolicy;

  @hasOne(() => ProductUniqueness, {keyTo: 'productId'})
  productUniqueness: ProductUniqueness;

  @hasOne(() => ProductSuitability, {keyTo: 'productId'})
  productSuitability: ProductSuitability;

  @hasOne(() => ProductPersonalWork, {keyTo: 'productId'})
  productPersonalWork: ProductPersonalWork;

  @hasOne(() => ProductMoreInfo, {keyTo: 'productId'})
  productMoreInfo: ProductMoreInfo;

  @hasOne(() => ProductTermsAndCondition, {keyTo: 'productId'})
  productTermsAndCondition: ProductTermsAndCondition;

  @hasMany(() => ProductVariant, {keyTo: 'productId'})
  productVariants: ProductVariant[];

  @belongsTo(() => Collection, {keyTo: 'id'}, {name: 'collection_id'})
  collectionId: string;

  @hasMany(() => ProductCustomizationField, {keyTo: 'productId'})
  productCustomizationFields: ProductCustomizationField[];

  @hasMany(() => ProductAsset, {keyTo: 'productId'})
  productAssets: ProductAsset[];

  @hasMany(() => FacetValue, {
    through: {
      model: () => ProductFacetValue,
      keyFrom: 'productId', // FK from ProductFacetValue to Product
      keyTo: 'facetValueId', // FK from ProductFacetValue to FacetValue
    },
  })
  facetValues: FacetValue[];

  @hasMany(() => ProductFacetValue, {keyTo: 'productId'})
  productFacetValues: ProductFacetValue[];

  constructor(data?: Partial<Product>) {
    super(data);
  }
}

export interface ProductRelations {
  featuredAsset: AssetResponseDto;
  collection: Collection;
}

export type ProductWithRelations = Product & ProductRelations;
