import {model, property} from '@loopback/repository';
import {CustomerStatus} from '@local/core';

@model()
export class CustomerStatusUpdateDto {
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(CustomerStatus),
    },
  })
  status: CustomerStatus;

  @property({
    type: 'string',
    required: false,
  })
  reason?: string;

  constructor(data?: Partial<CustomerStatusUpdateDto>) {
    Object.assign(this, data);
  }
}
