import {
  belongsTo,
  hasMany,
  hasOne,
  model,
  property,
} from '@loopback/repository';
import {
  UserTenant,
  UserTenantWithRelations,
} from '@sourceloop/authentication-service';
import {UserModifiableEntity} from '@sourceloop/core';
import {SampleProductImage} from './sample-product-image.model';
import {SellerStore} from './seller-store.model';

@model({settings: {strict: false}, name: 'sellers'})
export class Seller extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'seller_id',
  })
  sellerId: string;

  @property({
    type: 'boolean',
    name: 'email_verified',
    default: false,
  })
  emailVerified?: boolean;

  @property({
    type: 'boolean',
    name: 'phone_verified',
    default: false,
  })
  phoneVerified?: boolean;

  @property({
    type: 'string',
    name: 'verification_code',
  })
  verificationCode?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
    name: 'rejection_reason',
  })
  rejectionReason?: string;

  @property({
    type: 'string',
    name: 'vendor_id',
  })
  vendorId?: string;

  @belongsTo(
    () => UserTenant,
    {keyTo: 'id', name: 'userTenant'},
    {name: 'user_tenant_id', required: true},
  )
  userTenantId: string;

  @hasMany(() => SampleProductImage, {keyTo: 'sellerId'})
  sampleProductImages: SampleProductImage[];

  @hasOne(() => SellerStore, {keyTo: 'sellerId'})
  sellerStore: SellerStore;

  constructor(data?: Partial<Seller>) {
    super(data);
  }
}

export interface SellerRelations {
  userTenant?: UserTenantWithRelations;
  preSignedPhotoUrl?: string | null;
  // describe navigational properties here
}

export type SellerWithRelations = Seller & SellerRelations;
