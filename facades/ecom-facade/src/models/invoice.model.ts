import {Model, model, property} from '@loopback/repository';
import {Customer} from './auth-service';
import {OrderLineItem} from './ecom-service';

@model()
export class Invoice extends Model {
  @property({
    type: 'string',
    required: false,
    name: 'order_id',
  })
  orderId: string;

  @property({
    type: 'number',
    required: true,
    name: 'total_amount',
  })
  totalAmount: number;

  @property({
    type: 'string',
    required: true,
  })
  currency: string;

  @property({
    type: 'string',
  })
  gstNumber?: string;

  @property({
    type: 'string',
  })
  discount?: string;

  @property({type: 'object', required: true})
  customer: Customer;

  @property({type: 'array', itemType: 'object', required: true})
  orderLineItems: OrderLineItem[];

  constructor(data?: Partial<Invoice>) {
    super(data);
  }
}
