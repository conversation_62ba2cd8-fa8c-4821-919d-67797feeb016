import {Model, model, property} from '@loopback/repository';

@model()
export class ZohoItemRequest extends Model {
  @property({type: 'string', required: true})
  name: string;

  @property({type: 'number', required: true})
  rate: number;

  @property({type: 'string'})
  description?: string;

  @property({type: 'string'})
  taxId?: string;

  @property({type: 'string'})
  sku?: string;

  @property({type: 'string'})
  productType?: string;

  @property({type: 'string'})
  itemType?: string;

  @property({type: 'string'})
  accountId?: string;

  @property({type: 'string'})
  purchaseRate?: string;

  @property({type: 'string'})
  purchaseDescription?: string;

  @property({type: 'string'})
  purchaseAccountId?: string;

  @property({type: 'string'})
  inventoryAccountId?: string;

  @property({type: 'string'})
  unit?: string;

  @property({type: 'boolean'})
  isTaxable?: boolean;

  constructor(data?: Partial<ZohoItemRequest>) {
    super(data);
  }
}

@model()
export class ZohoItemResponse extends Model {
  @property({type: 'string'})
  itemId?: string;

  @property({type: 'string'})
  name?: string;

  @property({type: 'string'})
  status?: string;

  @property({type: 'string'})
  description?: string;

  @property({type: 'number'})
  rate?: number;

  @property({type: 'string'})
  unit?: string;

  @property({type: 'string'})
  sku?: string;

  @property({type: 'string'})
  productType?: string;

  @property({type: 'string'})
  taxId?: string;

  @property({type: 'string'})
  taxName?: string;

  @property({type: 'string'})
  taxPercentage?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  itemTaxPreferences?: {
    taxId: string;
    taxSpecification: string;
  }[];

  constructor(data?: Partial<ZohoItemResponse>) {
    super(data);
  }
}
