import {model, property, Model} from '@loopback/repository';

@model()
export class <PERSON><PERSON><PERSON>ddress extends Model {
  @property({type: 'string'})
  address?: string;

  @property({type: 'string'})
  city?: string;

  @property({type: 'string'})
  state?: string;

  @property({type: 'string'})
  zip?: string;

  @property({type: 'string'})
  country?: string;

  constructor(data?: Partial<BillingAddress>) {
    super(data);
  }
}
