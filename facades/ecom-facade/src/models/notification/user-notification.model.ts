import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'user_notifications'})
export class UserNotification extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'user_tenant_id',
  })
  userTenantId: string;

  @property({
    type: 'string',
    required: true,
    name: 'notification_id',
  })
  notificationId: string;

  @property({
    type: 'boolean',
    required: true,
    name: 'is_read',
    default: false,
  })
  isRead: boolean;

  @property({
    type: 'string',
    required: true,
    name: 'group_id',
  })
  groupId: string;

  @property({
    type: 'string',
    required: true,
    name: 'topic_id',
  })
  topicId: string;

  @property({
    type: 'string',
    required: true,
    name: 'list_key',
  })
  listKey: string;

  @property({
    type: 'string',
    required: true,
    name: 'list_name',
  })
  listName: string;

  constructor(data?: Partial<UserNotification>) {
    super(data);
  }
}

export interface UserNotificationRelations {
  // describe navigational properties here
}

export type UserNotificationWithRelations = UserNotification & UserNotificationRelations;
