import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'groups'})
export class Groups extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
    name: 'topic_id',
  })
  topicId: string;

  @property({
    type: 'string',
    required: true,
    name: 'list_key',
  })
  listKey: string;

  @property({
    type: 'string',
    required: true,
    name: 'list_name',
  })
  listName: string;

  constructor(data?: Partial<Groups>) {
    super(data);
  }
}

export interface GroupsRelations {
  // describe navigational properties here
}

export type GroupsWithRelations = Groups & GroupsRelations;
