import {Entity, model, property} from '@loopback/repository';
import {MessageOptions, MessageType, Receiver} from 'loopback4-notifications';
import {Msg91SMSType} from '../../types';

@model({
  name: 'notifications',
})
export class Notification extends Entity {
  @property({
    type: 'string',
    id: true,
  })
  id?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
  })
  subject?: string;

  @property({
    type: 'string',
    required: true,
  })
  body: string;

  @property({
    type: 'object',
    required: false,
  })
  receiver: Receiver;

  @property({
    type: 'number',
    required: true,
  })
  type: MessageType;

  @property({
    type: 'date',
    name: 'sent',
  })
  sentDate: Date;

  @property({
    type: 'object',
  })
  options?: MessageOptions;

  @property({
    name: 'is_draft',
  })
  isDraft?: boolean;

  @property({
    name: 'group_key',
  })
  groupKey?: string;

  @property({
    type: 'boolean',
    name: 'is_critical',
  })
  isCritical?: boolean;

  @property({
    type: 'string',
  })
  smsType?: Msg91SMSType;
}

export interface NotificationRelations {}

export type NotificationWithRelations = Notification & NotificationRelations;
