import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {AuthenticationComponent} from 'loopback4-authentication';
import {
  AuthorizationBindings,
  AuthorizationComponent,
} from 'loopback4-authorization';
import {RateLimitSecurityBindings} from 'loopback4-ratelimiter';
import {
  CoreComponent,
  SecureSequence,
  rateLimitKeyGen,
  AuthCacheSourceName,
  SFCoreBindings,
  BearerVerifierBindings,
  BearerVerifierComponent,
  BearerVerifierConfig,
  BearerVerifierType,
  SECURITY_SCHEME_SPEC,
  ProxyBuilderBindings,
  ProxyBuilderComponent,
} from '@sourceloop/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import * as openapi from './openapi.json';
import {
  Admin,
  Collection,
  Facet,
  Faq,
  FirmDetails,
  FirmDocuments,
  PrivacyPolicy,
  TermsAndConditions,
  Seller,
  SellerStore,
  Subscription,
  Plan,
  Warehouse,
  Feature,
  Notification,
  Profile,
  Product,
  Asset,
  ProductVariant,
  ProductFacetValue,
  ProductVariantFacetValue,
  FacetValue,
  InventoryItem,
  InventoryMovement,
  Cart,
  Order,
  Customer,
  CartItem,
  Payment,
  PromoCode,
  ProductCustomizationField,
  CustomizationValue,
  Wishlist,
  Address,
  OrderLineItem,
  Review,
  PageSection,
  PromoUsage,
  DukeCoin,
  Campaign,
  UserFcm,
  ShippingMethod,
  Chat,
  ChatMessage,
} from './models';
import {
  AuthCodeBindings,
  AuthUser,
  CodeWriterProvider,
} from '@sourceloop/authentication-service';
import {AuthCodeGeneratorProvider} from './providers';
import {
  AuthProxyConfig,
  SellerProxyConfig,
  SellerStoreProxyConfig,
  PlanProxyConfig,
  FirmProxyConfig,
  profileProxyConfig,
  ProductProxyConfig,
  OrderProxyConfig,
  PaymentProxyConfig,
  ProductVariantProxyConfig,
  PageSectionProxyConfig,
  PromoUsageProxyConfig,
  GroupProxyConfig,
} from './datasources/configs';
import {multerMiddleware} from './middlewares';
import {FacetProxyConfig} from './datasources/configs/facet-proxy.config';
import {AdminProxyConfig} from './datasources/configs/admin-proxy.config';
import {TaxCategory} from './models/ecom-service/tax-category.model';
import {PinnedProduct} from './models/ecom-service/pinned-product.model';
import {Referral} from './models/ecom-service/referral.model';
import {ReferralProgram} from './models/ecom-service/referral-program.model';
import {Configuration} from './models/ecom-service/configuration.model';
import {DukeCoinProxyConfig} from './datasources/configs/duke-coin-proxy.config';
import {SubscriptionInput} from './models/notification/subscription-input.model';
import {SubscribeProxyConfig} from './datasources/configs/subscribe-proxy.config';
import {Groups} from './models/notification/groups.model';
import {List} from './models/notification/list.model';
import {ListProxyConfig} from './datasources/configs/list-proxy.config';
import {SellerShippingProfile} from './models/ecom-service/seller-shipping-profile.model';
import {SellerShippingProfileProxyConfig} from './datasources/configs/seller-shipping-profile-proxy.config';
import {CampaignProxyConfig} from './datasources/configs/campaign-proxy.config';
import {Legal} from './models/ecom-service/legal.model';
import {Support} from './models/ecom-service/support.model';
import {Help} from './models/ecom-service/help.model';
import {Ticket} from './models/ecom-service/ticket.model';
import {Ecomdukeservice} from './models/ecom-service/ecomdukeservice.model';
import {EcomdukeserviceRequest} from './models/ecom-service/ecomdukeserice-request.model';

export {ApplicationConfig};

export class EcomFacadeApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    const port = 3000;
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });
    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };

    super(options);

    // To check if monitoring is enabled from env or not
    const enableObf = !!+(process.env.ENABLE_OBF ?? 0);
    // To check if authorization is enabled for swagger stats or not
    const authentication =
      process.env.SWAGGER_USER && process.env.SWAGGER_PASSWORD ? true : false;
    const obj = {
      enableObf,
      obfPath: process.env.OBF_PATH ?? '/obf',
      openapiSpec: openapi,
      authentication: authentication,
      swaggerUsername: process.env.SWAGGER_USER,
      swaggerPassword: process.env.SWAGGER_PASSWORD,
    };
    this.bind(SFCoreBindings.config).to(obj);
    this.component(CoreComponent);

    // Set up the custom sequence
    this.sequence(SecureSequence);

    // Add authentication component
    this.component(AuthenticationComponent);

    // Add bearer verifier component
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.facade,
      useSymmetricEncryption: true,
    } as BearerVerifierConfig);
    this.component(BearerVerifierComponent);

    this.bind(AuthCodeBindings.AUTH_CODE_GENERATOR_PROVIDER.key).toProvider(
      AuthCodeGeneratorProvider,
    );
    this.bind(AuthCodeBindings.CODEWRITER_PROVIDER.key).toProvider(
      CodeWriterProvider,
    );

    this.bind(ProxyBuilderBindings.CONFIG).to([
      {
        baseUrl: process.env.NOTIFICATION_SERVICE_URL as string,
        configs: [
          {
            model: Notification,
            basePath: '/notifications',
          },
          {
            model: Campaign,
            basePath: '/campaigns',
            restOperations: CampaignProxyConfig,
          },
          {
            model: UserFcm,
            basePath: '/user-fcms',
          },
          {
            model: SubscriptionInput,
            basePath: '/subscribe',
            restOperations: SubscribeProxyConfig,
          },
          {
            model: Groups,
            basePath: '/groups',
            restOperations: GroupProxyConfig,
          },
          {
            model: List,
            basePath: '/lists',
            restOperations: ListProxyConfig,
          },
        ],
      },
      {
        baseUrl: process.env.AUTH_SERVICE_BASE_URL as string,
        configs: [
          {
            model: AuthUser,
            basePath: '/auth',
            restOperations: AuthProxyConfig,
          },
          {
            model: Faq,
            basePath: '/faqs',
          },
          {
            model: Warehouse,
            basePath: '/warehouses',
          },
          {
            model: Seller,
            basePath: '/sellers',
            restOperations: SellerProxyConfig,
          },
          {
            model: SellerStore,
            basePath: '/seller-stores',
            restOperations: SellerStoreProxyConfig,
          },
          {
            model: FirmDetails,
            basePath: '/firm-details',
            restOperations: FirmProxyConfig,
          },
          {
            model: FirmDocuments,
            basePath: '/firm-documents',
          },
          {
            model: Profile,
            basePath: '/profile',
            restOperations: profileProxyConfig,
          },
          {
            model: TermsAndConditions,
            basePath: '/terms-and-conditions',
          },
          {
            model: PrivacyPolicy,
            basePath: '/privacy-policies',
          },
          {
            model: Admin,
            basePath: '/admins',
            restOperations: AdminProxyConfig,
          },
          {
            model: Customer,
            basePath: '/customers',
          },
        ],
      },
      {
        baseUrl: process.env.ECOM_SERVICE_URL as string,
        configs: [
          {
            model: Collection,
            basePath: '/collections',
          },
          {
            model: Subscription,
            basePath: '/subscriptions',
          },
          {
            model: Plan,
            basePath: '/plans',
            restOperations: PlanProxyConfig,
          },
          {
            model: Feature,
            basePath: '/features',
          },
          {
            model: Facet,
            basePath: '/facets',
            restOperations: FacetProxyConfig,
          },
          {
            model: TermsAndConditions,
            basePath: '/terms-and-conditions',
          },
          {
            model: PrivacyPolicy,
            basePath: '/privacy-policies',
          },
          {
            model: Product,
            basePath: '/products',
            restOperations: ProductProxyConfig,
            relations: [
              {
                name: 'seller',
                modelClass: Seller,
              },
            ],
          },
          {
            model: Asset,
            basePath: '/assets',
          },
          {
            model: ProductVariant,
            basePath: '/product-variants',
            restOperations: ProductVariantProxyConfig,
          },
          {
            model: ProductFacetValue,
            basePath: '/product-facet-values',
          },
          {
            model: ProductVariantFacetValue,
            basePath: '/product-variant-facet-values',
          },
          {
            model: FacetValue,
            basePath: '/facet-values',
          },
          {
            model: TaxCategory,
            basePath: '/tax-categories',
          },
          {
            model: InventoryItem,
            basePath: '/inventory-items',
          },
          {
            model: InventoryMovement,
            basePath: '/inventory-movements',
          },
          {
            model: Cart,
            basePath: '/carts',
            relations: [{modelClass: Customer, name: 'customer'}],
          },
          {
            model: Order,
            basePath: '/orders',
            restOperations: OrderProxyConfig,
          },
          {
            model: CartItem,
            basePath: '/cart-items',
          },
          {
            model: PromoCode,
            basePath: '/promo-codes',
          },
          {
            model: ProductCustomizationField,
            basePath: '/product-customization-fields',
          },
          {
            model: CustomizationValue,
            basePath: '/customization-values',
          },
          {
            model: Wishlist,
            basePath: '/wishlists',
          },
          {
            model: Address,
            basePath: '/addresses',
          },
          {
            model: OrderLineItem,
            basePath: '/order-line-items',
            relations: [
              {
                name: 'seller',
                modelClass: Seller,
              },
            ],
          },
          {
            model: Review,
            basePath: '/reviews',
            relations: [
              {
                name: 'customer',
                modelClass: Customer,
              },
            ],
          },
          {
            model: PageSection,
            basePath: '/page-sections',
            restOperations: PageSectionProxyConfig,
          },
          {
            model: PromoUsage,
            basePath: '/promo-usages',
            restOperations: PromoUsageProxyConfig,
          },
          {
            model: PinnedProduct,
            basePath: '/pinned-products',
          },
          {
            model: Referral,
            basePath: '/referrals',
          },
          {
            model: ReferralProgram,
            basePath: '/referral-programs',
          },
          {
            model: Configuration,
            basePath: '/configurations',
          },
          {
            model: DukeCoin,
            basePath: '/duke-coins',
            restOperations: DukeCoinProxyConfig,
          },
          {
            model: SellerShippingProfile,
            basePath: '/seller-shipping-profiles',
            restOperations: SellerShippingProfileProxyConfig,
          },
          {
            model: ShippingMethod,
            basePath: '/shipping-methods',
          },
          {
            model: Chat,
            basePath: '/chats',
          },
          {
            model: ChatMessage,
            basePath: '/chat-messages',
          },
          {
            model: Legal,
            basePath: '/legals',
          },
          {
            model: Support,
            basePath: '/support',
          },
          {
            model: Help,
            basePath: '/helps',
          },
          {
            model: Ticket,
            basePath: '/tickets',
          },
          {
            model: Ecomdukeservice,
            basePath: '/ecomdukeservices',
          },
          {
            model: EcomdukeserviceRequest,
            basePath: '/ecomdukeservice-requests',
            relations: [
              {
                name: 'seller',
                modelClass: Seller,
              },
            ],
          },
        ],
      },
      {
        baseUrl: process.env.PAYMENT_SERVICE_URL as string,
        configs: [
          {
            model: Payment,
            basePath: '/payments',
            restOperations: PaymentProxyConfig,
          },
        ],
      },
    ]);
    this.component(ProxyBuilderComponent);

    this.bind(RateLimitSecurityBindings.CONFIG).to({
      name: AuthCacheSourceName,
      max: parseInt(process.env.RATE_LIMIT_REQUEST_CAP ?? '100'),
      keyGenerator: rateLimitKeyGen,
    });

    // Add authentication component
    this.component(AuthenticationComponent);

    // Add bearer verifier component
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.facade,
      useSymmetricEncryption: true,
    } as BearerVerifierConfig);
    this.component(BearerVerifierComponent);
    // Add authorization component
    this.bind(AuthorizationBindings.CONFIG).to({
      allowAlwaysPaths: ['/explorer', '/openapi.json'],
    });
    this.component(AuthorizationComponent);

    this.expressMiddleware(
      'middleware.multer',
      multerMiddleware.any(), // this allows dynamic field names
      {
        injectConfiguration: false,
        key: 'middleware.multer',
        chain: 'upload',
      },
    );

    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });

    this.component(RestExplorerComponent);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };

    this.api({
      openapi: '3.0.0',
      info: {
        title: 'ecom-facade',
        version: '1.0.0',
      },
      paths: {},
      components: {
        securitySchemes: SECURITY_SCHEME_SPEC,
      },
      servers: [{url: '/'}],
    });
  }
}
