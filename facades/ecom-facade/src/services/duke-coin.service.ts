import {injectable, BindingScope} from '@loopback/core';
import {restService} from '@sourceloop/core';
import {Duke<PERSON>oi<PERSON>} from '../models';
import {HttpErrors} from '@loopback/rest';
import {DukeCoinProxyType} from '../datasources/configs/duke-coin-proxy.config';
import { Filter } from '@loopback/repository';

@injectable({scope: BindingScope.TRANSIENT})
export class DukeCoinService {
  constructor(
    @restService(DukeCoin)
    private readonly dukeProxy: DukeCoinProxyType,
  ) {}

  async createDukeCoin(
    dukeCoin: Partial<DukeCoin>,
    token: string,
  ): Promise<DukeCoin> {
    const userTenantId = dukeCoin.userTenantId;
    if (!userTenantId) {
      throw new HttpErrors.BadRequest(
        'userTenantId is required in DukeCoin object.',
      );
    }
    const newDukeCoin: Partial<DukeCoin> = {
      ...dukeCoin,
    };

    return this.dukeProxy.create(newDukeCoin, token);
  }

    async updateById(
    id: string,
    data: Partial<DukeCoin>,
    token: string,
  ): Promise<void> {
    return this.dukeProxy.updateById(id, data, token);
  }

  async findOne(
    filter: Filter<DukeCoin>,
    token: string,
  ): Promise<DukeCoin | null> {
    const result = await this.dukeProxy.find(filter, token);
    return result?.[0] ?? null;
  }

}
