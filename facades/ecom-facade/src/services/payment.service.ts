import {BindingScope, Getter, injectable, inject} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {
  CashfreeCreatePaymentLinkRequestDto,
  CustomerDetails,
  LinkNotify,
  IAuthUserWithTenant,
  Payment,
  LinkMeta,
} from '../models';
import {PaymentProxyType} from '../datasources/configs/payment-proxy.config';
import {restService} from '@sourceloop/core';
import {CashfreePaymentLinkResponse} from '@local/core/dist/types';

@injectable({scope: BindingScope.TRANSIENT})
export class PaymentService {
  constructor(
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,

    @restService(Payment)
    private readonly paymentProxy: PaymentProxyType,
  ) {}

  async createCashfreePaymentLinkForCustomer(
    amount: number,
    linkNotes: Record<string, string>,
    token: string,
  ): Promise<CashfreePaymentLinkResponse> {
    const user = await this.getCurrentUser();

    if (!user?.email || !user?.phone || !user?.id) {
      throw new HttpErrors.BadRequest('Incomplete customer details.');
    }

    const request = new CashfreeCreatePaymentLinkRequestDto({
      linkId: `cf-link-${Date.now()}`,
      linkAmount: amount,
      linkCurrency: 'INR',
      linkPurpose: 'Service Payment',
      customerDetails: new CustomerDetails({
        customerId: user.id,
        customerEmail: user.email,
        customerPhone: user.phone,
        customerName: `${user.firstName ?? ''} ${user.lastName ?? ''}`.trim(),
      }),
      linkNotify: new LinkNotify({
        sendEmail: true,
        sendSms: true,
      }),
      linkNotes: {
        ...linkNotes,
        createdBy: user.email,
      },
      linkAutoReminders: true,
      linkPartialPayments: false,
      linkMeta: {
        returnUrl: `${process.env.SELLER_WEB_URL}/ecomduke-service`,
      } as LinkMeta,
    });

    return this.paymentProxy.createPaymentLink(request, token);
  }
}
