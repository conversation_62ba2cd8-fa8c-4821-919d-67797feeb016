import {injectable, BindingScope} from '@loopback/core';
import {Filter} from '@loopback/repository';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {AuthUser} from '@sourceloop/authentication-service';
import {AuthProxyType} from '../datasources/configs';
import {Ticket} from '../models/ecom-service/ticket.model';
import {HttpErrors} from '@loopback/rest';

const TICKET_PREFIX = 'TICKET';

@injectable({scope: BindingScope.TRANSIENT})
export class TicketService {
  constructor(
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,

    @restService(Ticket)
    private readonly ticketProxy: ModifiedRestService<Ticket>,
  ) {}

  async applySellerFilter(
    xOrigin: string,
    token?: string,
    filter?: Filter<Ticket>,
  ): Promise<Filter<Ticket>> {
    if (xOrigin === 'ecomdukes-seller') {
      const result = await this.authProvider.getMe(token ?? '', xOrigin);
      filter = filter ?? {};
      filter.where = filter.where
        ? {
            ...filter.where,
            createdBy: result.userTenantId,
          }
        : {createdBy: result.userTenantId};
    }
    return filter ?? {};
  }

  async generateShortCode(): Promise<string> {
    const result = await this.ticketProxy.find({
      order: ['created_on DESC'],
      fields: {shortCode: true},
      limit: 1,
    });

    const last = result[0];

    if (!last || !last.shortCode) {
      return `${TICKET_PREFIX}-000001`;
    }

    const parts = last.shortCode.split('-');
    const sequence = Number(parts[1]);

    if (!sequence || isNaN(sequence)) {
      throw new HttpErrors.BadRequest('Invalid ticket short code format');
    }

    const next = (sequence + 1).toString().padStart(6, '0');
    return `${TICKET_PREFIX}-${next}`;
  }

  async createTicket(
    ticket: Omit<Ticket, 'id' | 'shortCode'>,
  ): Promise<Ticket> {
    const shortCode = await this.generateShortCode();
    return this.ticketProxy.create({...ticket, shortCode});
  }
}
