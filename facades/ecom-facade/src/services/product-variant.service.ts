import {injectable, BindingScope, service} from '@loopback/core';
import {AssetService} from './asset.service';
import {
  Customer,
  ProductVariant,
  ProductVariantAssetWithRelations,
  ProductVariantWithRelations,
  ReviewWithRelations,
  Seller,
} from '../models';
import {
  Filter,
  FilterExcludingWhere,
  InclusionFilter,
} from '@loopback/repository';
import {AuthUser} from '@sourceloop/authentication-service';
import {restService} from '@sourceloop/core';
import {SellerStatus} from '../enums';
import {
  AuthProxyType,
  CustomerProxyType,
  SellerProxyType,
} from '../datasources/configs';
import {fieldsExcludeMetaFields} from '../constants';
import {ProductStatus} from '@local/core';

@injectable({scope: BindingScope.TRANSIENT})
export class ProductVariantService {
  constructor(
    @service(AssetService)
    private readonly assetService: AssetService,
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
  ) {}

  private async getProductVariantAssetWithPresigned(
    productAsset: ProductVariantAssetWithRelations,
  ) {
    if (productAsset.asset?.preview) {
      productAsset.asset = this.assetService.getAssetWithPreview(
        productAsset.asset,
      );
    }
    return productAsset;
  }

  async getProductVariantWithPresignedUrl(
    variant: ProductVariantWithRelations,
  ): Promise<ProductVariantWithRelations> {
    const cdnOrigin = process.env.CDN_ORIGIN ?? '';

    if (variant.featuredAssetId && variant?.featuredAsset?.preview) {
      variant.featuredAsset = this.assetService.getAssetWithPreview(
        variant.featuredAsset,
      );
    }
    if (variant.productVariantAssets?.length) {
      variant.productVariantAssets = await Promise.all(
        variant.productVariantAssets.map(item =>
          this.getProductVariantAssetWithPresigned(
            item as ProductVariantAssetWithRelations,
          ),
        ),
      );
    }

    if (variant.reviews?.length) {
      variant.reviews = variant.reviews.map(review => {
        if (review.reviewAssets?.length) {
          review.previewAssets = review.reviewAssets.map(
            key => `${cdnOrigin}/${key}`,
          );
        }
        return review;
      });
    }
    return variant;
  }

  async getProductVariantsWithFeaturedPreviewUrls(
    variants: ProductVariantWithRelations[],
  ): Promise<ProductVariantWithRelations[]> {
    return Promise.all(
      variants.map(async variant => {
        if (variant.featuredAssetId && variant?.featuredAsset?.preview) {
          variant.featuredAsset = this.assetService.getAssetWithPreview(
            variant.featuredAsset,
          );
        }
        return variant;
      }),
    );
  }

  async applySellerFilter(
    xOrigin: string,
    token: string | undefined,
    filter?: Filter<ProductVariant>,
  ): Promise<Filter<ProductVariant>> {
    filter = filter ?? {};

    if (xOrigin === 'ecomdukes-seller') {
      const result = await this.authProvider.getMe(token ?? '', xOrigin);

      filter.include = filter.include
        ? ([
            ...filter.include,
            {
              relation: 'product',
              scope: {
                where: {sellerId: result.profileId},
              },
            } as InclusionFilter,
          ] as InclusionFilter[])
        : [
            {
              relation: 'product',
              scope: {
                where: {sellerId: result.profileId},
              },
            } as InclusionFilter,
          ];
    } else if (xOrigin === 'ecomdukes-customer') {
      // For customer requests, only show variants of approved products
      filter.include = filter.include
        ? ([
            ...filter.include,
            {
              relation: 'product',
              scope: {
                where: {status: ProductStatus.APPROVED},
              },
            } as InclusionFilter,
          ] as InclusionFilter[])
        : [
            {
              relation: 'product',
              scope: {
                where: {status: ProductStatus.APPROVED},
              },
            } as InclusionFilter,
          ];
    }
    return filter;
  }

  async getProductVariantWithSeller(
    variant: ProductVariantWithRelations,
  ): Promise<ProductVariantWithRelations> {
    const sellerId = variant.product!.sellerId;
    const filter: Filter<Seller> = {
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [{relation: 'user'}],
          },
        },
      ],
    };

    const seller = await this.sellerProxyService.findById(sellerId, filter);
    return {
      ...variant,
      seller,
    } as ProductVariantWithRelations;
  }

  async getProductVariantWithCustomer(
    variant: ProductVariantWithRelations,
  ): Promise<ProductVariantWithRelations> {
    const reviews = (variant.reviews as ReviewWithRelations[]) ?? [];

    // Extract unique customer IDs
    const customerIds = Array.from(
      new Set(reviews.map(r => r.customerId).filter(Boolean)),
    );

    if (!customerIds.length) {
      return variant;
    }

    // Fetch customers in bulk using the customer proxy
    const customers = await this.customerProxy.find({
      where: {
        id: {inq: customerIds},
      },
      fields: fieldsExcludeMetaFields,
      include: [
        {
          relation: 'userTenant',
          scope: {
            fields: {id: true},
            include: [
              {
                relation: 'user',
                scope: {
                  fields: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            ],
          },
        },
      ],
    });

    // Attach the customer info to each review
    const customerMap = new Map(customers.map(c => [c.id, c]));
    const reviewsWithCustomers = reviews.map(r => ({
      ...r,
      customer: customerMap.get(r.customerId) ?? null,
    })) as ReviewWithRelations[];

    return {
      ...variant,
      reviews: reviewsWithCustomers,
    } as ProductVariantWithRelations;
  }

  modifyFilterForSensitiveRelations(
    filter?: FilterExcludingWhere<ProductVariant>,
  ): {
    modifiedFilter: FilterExcludingWhere<ProductVariant>;
    hasSellerRelation: boolean;
    hasCustomerRelation: boolean;
  } {
    const result = {
      modifiedFilter: filter ? {...filter} : {include: []},
      hasSellerRelation: false,
      hasCustomerRelation: false,
    };

    if (!result.modifiedFilter.include) {
      result.modifiedFilter.include = [];
      return result;
    }

    // Filter top-level includes (e.g. seller)
    result.modifiedFilter.include = result.modifiedFilter.include.filter(
      include => {
        if (typeof include === 'string') {
          return include !== 'seller';
        }

        if (include.relation === 'seller') {
          result.hasSellerRelation = true;
          return false;
        }

        // Filter nested customer relation under reviews
        if (include.relation === 'reviews' && include.scope?.include) {
          include.scope.include = include.scope.include.filter(revInclude => {
            if (typeof revInclude === 'string') {
              return revInclude !== 'customer';
            }

            if (revInclude.relation === 'customer') {
              result.hasCustomerRelation = true;
              return false;
            }

            return true;
          });
        }

        // Also filter seller inside product relation like before
        if (include.relation === 'product' && include.scope?.include) {
          include.scope.include = include.scope.include.filter(
            productInclude => {
              if (typeof productInclude === 'string') {
                return productInclude !== 'seller';
              }

              if (productInclude.relation === 'seller') {
                result.hasSellerRelation = true;
                return false;
              }

              return true;
            },
          );
        }

        return true;
      },
    );

    return result;
  }
  async filterOutInactiveSellerProductvariants(
    productVariants: ProductVariantWithRelations[],
  ): Promise<ProductVariantWithRelations[]> {
    const filteredProducts: ProductVariantWithRelations[] = [];

    for (const productVariant of productVariants) {
      // eslint-disable-next-line no-await-in-loop
      const seller = await this.sellerProxyService.findById(
        productVariant.product.sellerId,
      );

      if (seller?.status !== SellerStatus.INACTIVE && seller.vendorId) {
        filteredProducts.push(productVariant);
      }
    }

    return filteredProducts;
  }
}
