import {injectable, BindingScope, inject} from '@loopback/core';
import {RestBindings, Response, Request, HttpErrors} from '@loopback/rest';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {CustomerProxyType} from '../datasources/configs/customer-proxy.config';
import {Address, Customer, CustomerStatusUpdateDto} from '../models';
import {CustomerDto} from '../models/ecom-service/dto/customer-dto.model';
import {CustomerStatus} from '@local/core';

@injectable({scope: BindingScope.TRANSIENT})
export class CustomerService {
  private token: string;

  constructor(
    @inject(RestBindings.Http.RESPONSE)
    private readonly response: Response,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
    @restService(Address)
    private readonly addressProxy: ModifiedRestService<Address>,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async createCustomer(customer: CustomerDto): Promise<CustomerDto> {
    customer.username = customer.email ?? '';

    if (customer.dob) {
      customer.dob = new Date(customer.dob);
    }

    if (typeof customer.addresses === 'string') {
      try {
        customer.addresses = JSON.parse(customer.addresses);
      } catch (e) {
        throw new HttpErrors.UnprocessableEntity('Invalid addresses format');
      }
    }

    if (!Array.isArray(customer.addresses)) {
      throw new HttpErrors.UnprocessableEntity('addresses must be an array');
    }

    const {addresses, ...customerData} = customer;

    const createdCustomer = await this.customerProxy.create(
      customerData as CustomerDto,
      this.token,
    );

    if (!createdCustomer.id) {
      throw new HttpErrors.InternalServerError(
        'Customer creation failed: missing ID',
      );
    }

    if (addresses.length > 0) {
      await Promise.all(
        addresses.map(addr =>
          this.addressProxy.create({...addr, customerId: createdCustomer.id!}),
        ),
      );
    }

    return Object.assign(createdCustomer, {addresses});
  }

  async updateCustomer(
    id: string,
    customer: Partial<CustomerDto>,
  ): Promise<void> {
    if (customer.email) {
      customer.username = customer.email;
    }
    if (customer.dob) {
      customer.dob = new Date(customer.dob);
    }

    if (typeof customer.addresses === 'string') {
      try {
        customer.addresses = JSON.parse(customer.addresses);
      } catch (e) {
        throw new HttpErrors.UnprocessableEntity('Invalid addresses format');
      }
    }

    if (customer.addresses && !Array.isArray(customer.addresses)) {
      throw new HttpErrors.UnprocessableEntity('addresses must be an array');
    }
    const {addresses = [], ...customerData} = customer;
    await this.customerProxy.updateById(id, customerData);
    if (addresses.length > 0) {
      await Promise.all(
        addresses.map(async (addr: Address) => {
          if (addr.id) {
            return this.addressProxy.updateById(addr.id, addr);
          } else {
            return this.addressProxy.create({...addr, customerId: id});
          }
        }),
      );
    }
  }

  async deleteCustomer(id: string): Promise<void> {
    const customerAddresses = await this.addressProxy.find({
      where: {customerId: id},
    });
    if (customerAddresses.length > 0) {
      await Promise.all(
        customerAddresses.map(addr => {
          if (!addr.id) {
            throw new HttpErrors.UnprocessableEntity('Address id is missing');
          }
          return this.addressProxy.deleteById(addr.id);
        }),
      );
    }
    await this.customerProxy.deleteById(id);
  }

  async updateCustomerStatus(
    id: string,
    statusUpdate: CustomerStatusUpdateDto,
  ): Promise<void> {
    // Update customer status via proxy
    await this.customerProxy.updateById(
      id,
      {
        status: statusUpdate.status,
      } as Partial<CustomerDto>,
      this.token,
    );
  }
}
