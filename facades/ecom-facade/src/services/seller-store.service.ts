import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {Seller, SellerStore} from '../models';
import {FilterExcludingWhere} from '@loopback/repository';
import {restService} from '@sourceloop/core';
import {SellerProxyType} from '../datasources/configs/seller.proxy.config';
import {SellerStoreProxyType} from '../datasources/configs';

@injectable({scope: BindingScope.TRANSIENT})
export class SellerStoreExtendedService {
  constructor(
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @restService(SellerStore)
    private sellerStoreProxyService: SellerStoreProxyType,
  ) {}

  async getStoreWithUserDetails(
    token: string,
    sellerId: string,
    filter?: FilterExcludingWhere<SellerStore>,
  ): Promise<SellerStore & {email: string; phoneNumber: string}> {
    const seller = await this.sellerProxyService.findById(sellerId, {
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [{relation: 'user'}],
          },
        },
      ],
    });

    if (!seller) {
      throw new HttpErrors.NotFound('Seller not found');
    }

    const userTenant = (seller as any)['userTenant'];
    const user = userTenant?.['user'];

    const email = user?.email ?? '';
    const phoneNumber = user?.phone ?? '';

    const store = await this.sellerStoreProxyService.getStoreBySellerId(
      token,
      sellerId,
      filter,
    );

    if (!store) {
      throw new HttpErrors.NotFound('Seller store not found');
    }

    const fileKeys = {
      logo: store.logo,
      dp: store.dp,
      banner: store.banner,
      signature: store.signature,
    };

    const fileInfo: Record<string, string> = Object.fromEntries(
      Object.entries(fileKeys)
        .filter(([_, value]) => Boolean(value))
        .map(([key, value]) => [key, `${process.env.CDN_ORIGIN}/${value}`]),
    );

    const enrichedStore = new SellerStore({
      ...store,
      logo: fileInfo.logo ?? store.logo,
      dp: fileInfo.dp ?? store.dp,
      banner: fileInfo.banner ?? store.banner,
      signature: fileInfo.signature ?? store.signature,
    }) as SellerStore & {email: string; phoneNumber: string};

    enrichedStore.email = email;
    enrichedStore.phoneNumber = phoneNumber;
    return enrichedStore;
  }
}
