import {injectable, inject, Getter} from '@loopback/core';
import {Chat, ChatMessage, IAuthUserWithTenant} from '../models';
import {HttpErrors} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {restService} from '@sourceloop/core';
import {ChatMessageProxyType} from '../datasources/configs/chat-message-proxy.config';
import {SenderType} from '@local/core';
import {Filter, Where} from '@loopback/repository';
import {ChatProxyType} from '../datasources/configs/chat-proxy.config';

@injectable()
export class ChatMessageService {
  constructor(
    @restService(ChatMessage)
    private readonly chatMessageProxy: ChatMessageProxyType,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Chat)
    private readonly chatProxy: ChatProxyType,
  ) {}

  async createMessageWithCurrentUser(
    data: Omit<ChatMessage, 'id' | 'senderId' | 'senderType'>,
  ): Promise<ChatMessage> {
    const currentUser = await this.getCurrentUser();

    if (!currentUser?.id || !currentUser?.role?.length) {
      throw new HttpErrors.Unauthorized('User info missing');
    }

    const senderType = this.mapRoleToSenderType(currentUser.role[0]);
    return this.chatMessageProxy.create({
      ...data,
      senderId: currentUser.id,
      senderType,
    });
  }

  async findMessagesBySeller(): Promise<ChatMessage[]> {
    const currentUser = await this.getCurrentUser();
    if (!currentUser?.id) {
      throw new HttpErrors.Unauthorized('Missing current user');
    }

    const chats = await this.chatProxy.find({
      where: {sellerId: currentUser.id},
      fields: {id: true},
    });

    const chatIds = chats
      .map(chat => chat.id)
      .filter((id): id is string => typeof id === 'string');

    if (!chatIds.length) return [];

    return this.chatMessageProxy.find({
      where: {
        chatId: {inq: chatIds},
      },
      order: ['createdOn ASC'],
    });
  }

  private mapRoleToSenderType(role: string): SenderType {
    switch (role.toLowerCase()) {
      case 'customer':
      case 'c':
        return SenderType.CUSTOMER;

      case 'seller':
      case 's':
        return SenderType.SELLER;

      case 'system':
      case 'sys':
        return SenderType.SYSTEM;

      default:
        throw new HttpErrors.BadRequest(`Unsupported sender role: ${role}`);
    }
  }

  async getCurrentUsers(): Promise<IAuthUserWithTenant> {
    const currentUser = await this.getCurrentUser();
    if (!currentUser?.id || !currentUser?.role?.length) {
      throw new HttpErrors.Unauthorized('User info missing');
    }
    return currentUser;
  }

  async markMessagesAsRead(chatId: string, receiverId: string): Promise<void> {
    await this.chatMessageProxy.update(
      {read: true},
      {
        chatId,
        senderId: {neq: receiverId}, // Mark as read only if not sent by the receiver
        read: false,
      },
    );
  }
}
