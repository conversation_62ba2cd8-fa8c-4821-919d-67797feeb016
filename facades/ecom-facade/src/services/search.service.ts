import {injectable, BindingScope, service} from '@loopback/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  Collection,
  CollectionWithRelations,
  FacetValue,
  FilterGroup,
  FilterValue,
  Product,
  ProductFacetValue,
  ProductVariant,
  ProductVariantFacetValue,
  ProductVariantWithRelations,
} from '../models';
import {Filter, repository, Where} from '@loopback/repository';
import {ProductVariantService} from './product-variant.service';
import {SearchFilterRepository} from '../repositories';
import {FILTER_CACHE_TTL, MINIMUM_DISCOUNT_THRESHOLD} from '../constants';
import {getDiscountLabel, getProductSearchFilter} from '../utils/search.util';
import {ProductStatus} from '@local/core';

@injectable({scope: BindingScope.TRANSIENT})
export class SearchService {
  constructor(
    @restService(Product)
    private readonly productProxy: ModifiedRestService<Product>,
    @restService(ProductVariant)
    private readonly productVariantProxy: ModifiedRestService<ProductVariant>,
    @restService(ProductVariantFacetValue)
    private readonly productVariantFacetValueProxy: ModifiedRestService<ProductVariantFacetValue>,
    @restService(ProductFacetValue)
    private readonly productFacetValueProxy: ModifiedRestService<ProductFacetValue>,
    @restService(FacetValue)
    private readonly facetValueProxy: ModifiedRestService<FacetValue>,
    @restService(Collection)
    private readonly collectionProxy: ModifiedRestService<Collection>,
    @service(ProductVariantService)
    private readonly productVariantService: ProductVariantService,
    @repository(SearchFilterRepository)
    private readonly searchFilterRepository: SearchFilterRepository,
  ) {}

  private async prepareCondition(
    keyword: string,
  ): Promise<Where<ProductVariant>> {
    const cachedFilter = await this.searchFilterRepository.get(keyword);
    if (cachedFilter?.where) {
      return cachedFilter.where;
    }
    const facetValues = await this.facetValueProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {name: true, id: true},
    });

    const collections = await this.collectionProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {name: true, id: true},
      include: [
        {relation: 'childrens', scope: {fields: {name: true, id: true}}},
      ],
    });

    const productFacetValues = await this.productFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productId: true},
    });

    const productVariantFacets = await this.productVariantFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productVariantId: true},
    });
    const collectionIds = (collections as CollectionWithRelations[]).reduce(
      (acc: string[], collection) => {
        acc.push(collection.id ?? ''); // add parent ID
        if (collection.childrens && Array.isArray(collection.childrens)) {
          acc.push(...collection.childrens.map(child => child.id ?? '')); // add children IDs
        }
        if (collection.parentId) {
          acc.push(collection.parentId);
        }
        return acc;
      },
      [],
    );

    const products = await this.productProxy.find({
      where: {
        or: [
          {collectionId: {inq: collectionIds}},
          {name: {ilike: `%${keyword}%`}},
        ],
      },
    });

    const productIds = [
      ...products.map(p => p.id ?? ''),
      ...productFacetValues.map(pf => pf.productId ?? ''),
    ];
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (productIds.length > 0) {
      where.or.push({productId: {inq: productIds}});
    }
    if (productVariantFacets.length) {
      where.or.push({
        id: {inq: productVariantFacets.map(pvf => pvf.productVariantId ?? '')},
      });
    }
    await this.searchFilterRepository.set(
      keyword,
      {keyword, where},
      {ttl: FILTER_CACHE_TTL},
    );
    return where;
  }

  private async prepareConditionFromFacetOrCollection({
    keyword,
    facetValueIds,
    collectionIds,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
  }): Promise<Where<ProductVariant>> {
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (collectionIds?.length) {
      const products = await this.productProxy.find({
        where: {
          collectionId: {inq: collectionIds},
        },
        fields: {id: true},
      });
      const productIds = products.map(p => p.id ?? '');
      where.or.push({productId: {inq: productIds}});
    }

    if (facetValueIds?.length) {
      const facetVariantMappings =
        await this.productVariantFacetValueProxy.find({
          where: {
            facetValueId: {inq: facetValueIds},
          },
          fields: {productVariantId: true},
        });
      const variantIds = facetVariantMappings.map(
        m => m.productVariantId ?? '',
      );
      if (variantIds.length) {
        where.or.push({id: {inq: variantIds}});
      }
    }

    return where;
  }

  async searchSuggestions(keyword: string): Promise<Partial<ProductVariant[]>> {
    let where = await this.prepareCondition(keyword);

    // Filter to only show approved products for customers
    const approvedProductIds = await this.getApprovedProductIds();
    if (approvedProductIds.length > 0) {
      where = {
        ...where,
        productId: {inq: approvedProductIds},
      };
    } else {
      // If no approved products, return empty result
      where = {
        ...where,
        id: {inq: []},
      };
    }

    const products = await this.productVariantProxy.find({
      where,
      fields: {id: true, name: true},
      include: [
        {
          relation: 'product',
          scope: {fields: {sellerId: true}},
        },
      ],
    });

    const filteredProductsFromInactiveSeller =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        products as ProductVariantWithRelations[],
      );
    return filteredProductsFromInactiveSeller;
  }

  async search({
    keyword,
    facetValueIds,
    collectionIds,
    filter,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
    filter?: Filter<ProductVariant>;
  }): Promise<ProductVariant[]> {
    let where: Where<ProductVariant> = filter?.where || {};

    if (keyword) {
      const keywordWhere = await this.prepareCondition(keyword);
      where = {...where, ...keywordWhere};
    }

    if (facetValueIds?.length) {
      const pvFacetMatches = await this.productVariantFacetValueProxy.find({
        where: {facetValueId: {inq: facetValueIds}},
        fields: {productVariantId: true},
      });
      const matchedVariantIds = pvFacetMatches.map(
        v => v.productVariantId ?? '',
      );
      where = {
        ...where,
        id: {inq: matchedVariantIds},
      };
    }

    if (collectionIds?.length) {
      const productMatches = await this.productProxy.find({
        where: {collectionId: {inq: collectionIds}},
        fields: {id: true},
      });
      const productIds = productMatches.map(p => p.id ?? '');
      where = {
        ...where,
        productId: {inq: productIds},
      };
    }

    // Filter to only show approved products for customers
    const approvedProductIds = await this.getApprovedProductIds();
    if (approvedProductIds.length > 0) {
      where = {
        ...where,
        productId: where.productId
          ? {
              inq:
                (where.productId as any).inq?.filter((id: string) =>
                  approvedProductIds.includes(id),
                ) || approvedProductIds,
            }
          : {inq: approvedProductIds},
      };
    } else {
      // If no approved products, return empty result
      where = {
        ...where,
        id: {inq: []},
      };
    }

    const mergedIncludes = [
      ...(filter?.include ?? []),
      {
        relation: 'product',
        scope: {fields: {sellerId: true}},
      },
    ];

    const variants = await this.productVariantProxy.find({
      ...filter,
      where,
      include: mergedIncludes,
    });

    const filtered =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        variants as ProductVariantWithRelations[],
      );

    return Promise.all(
      filtered.map(item =>
        this.productVariantService.getProductVariantWithPresignedUrl(item),
      ),
    );
  }

  async getFilters(
    keyword?: string,
    facetValueIdsStr?: string,
    collectionIdsStr?: string,
  ): Promise<FilterGroup[]> {
    const facetValueIds = facetValueIdsStr?.split(',').filter(Boolean);
    const collectionIds = collectionIdsStr?.split(',').filter(Boolean);

    let where: Where<ProductVariant>;

    if (facetValueIds?.length || collectionIds?.length) {
      // build filter conditions manually
      where = await this.prepareConditionFromFacetOrCollection({
        keyword,
        facetValueIds,
        collectionIds,
      });
    } else {
      // fallback to keyword-only search
      where = await this.prepareCondition(keyword ?? '');
    }

    // Filter to only show approved products for customers
    const approvedProductIds = await this.getApprovedProductIds();
    if (approvedProductIds.length > 0) {
      where = {
        ...where,
        productId: where.productId
          ? {
              inq:
                (where.productId as any).inq?.filter((id: string) =>
                  approvedProductIds.includes(id),
                ) || approvedProductIds,
            }
          : {inq: approvedProductIds},
      };
    } else {
      // If no approved products, return empty result
      where = {
        ...where,
        id: {inq: []},
      };
    }

    const variants = await this.productVariantProxy.find({
      ...getProductSearchFilter(),
      where,
    });

    return this.buildFilters(variants as ProductVariantWithRelations[]);
  }

  private async buildFilters(
    variants: ProductVariantWithRelations[],
  ): Promise<FilterGroup[]> {
    const collectionMap = new Map<
      string,
      {variantIds: string[]; name: string}
    >();

    const discountMap = new Map<string, {name: string; variantIds: string[]}>();
    const customMap = new Map<string, {name: string; variantIds: string[]}>();

    for (const variant of variants) {
      const variantId = variant.id!;

      // Collection filter
      if (variant.product?.collection) {
        const object = collectionMap.get(variant.product.collectionId) ?? {
          variantIds: [],
          name: variant.product?.collection?.name,
        };
        object.variantIds.push(variantId);
        collectionMap.set(variant.product.collectionId, object);
      }

      // Discount filter
      if (variant.productVariantPrice) {
        const {mrp, price} = variant.productVariantPrice;
        const discount = ((mrp - price) / mrp) * 100;
        if (discount >= MINIMUM_DISCOUNT_THRESHOLD) {
          const discountLabel = getDiscountLabel(discount);
          if (discountLabel) {
            const object = discountMap.get(discountLabel) ?? {
              variantIds: [],
              name: discountLabel,
            };
            object.variantIds.push(variantId);
            discountMap.set(discountLabel, object);
          }
        }
      }

      // Custom filter
      if (variant.product?.productCustomizationFields) {
        const object = customMap.get('Personalized') ?? {
          variantIds: [],
          name: 'Personalized',
        };
        object.variantIds.push(variantId);
        customMap.set('Personalized', object);
      }
    }

    // Map to filter output
    const filters: FilterGroup[] = [];

    filters.push(
      new FilterGroup({
        label: 'Pick a Category',
        values: Array.from(collectionMap.entries()).map(
          ([colId, value]) =>
            new FilterValue({
              label: value.name,
              value: colId,
              productVariantIds: value.variantIds,
            }),
        ),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Discounts',
        values: Array.from(discountMap.entries()).map(
          ([label, value]) =>
            new FilterValue({
              label: label,
              value: label,
              productVariantIds: value.variantIds,
            }),
        ),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Custom',
        values: Array.from(customMap.entries()).map(
          ([label, value]) =>
            new FilterValue({
              label: label,
              value: label,
              productVariantIds: value.variantIds,
            }),
        ),
      }),
    );

    return filters;
  }

  /**
   * Get IDs of products that have APPROVED status
   * Only approved products should be visible to customers
   */
  private async getApprovedProductIds(): Promise<string[]> {
    const approvedProducts = await this.productProxy.find({
      where: {status: ProductStatus.APPROVED},
      fields: {id: true},
    });
    return approvedProducts.map(p => p.id ?? '').filter(Boolean);
  }
}
