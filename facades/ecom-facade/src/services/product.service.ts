import {injectable, BindingScope, service, inject} from '@loopback/core';
import {AssetService} from './asset.service';
import {
  Payment,
  Product,
  ProductAssetWithRelations,
  ProductVariant,
  ProductVariantWithRelations,
  ProductWithRelations,
  Seller,
  ZohoItemRequest,
  BulkStatusUpdateDto,
  SingleStatusUpdateDto,
} from '../models';
import {Filter} from '@loopback/repository';
import {restService} from '@sourceloop/core';
import {AuthUser} from '@sourceloop/authentication-service';
import {
  AuthProxyType,
  PaymentProxyType,
  ProductVariantProxyType,
  SellerProxyType,
  ProductProxyType,
} from '../datasources/configs';
import {ProductVariantService} from './product-variant.service';
import {SellerStatus} from '../enums';
import {NotificationHelperService} from './notification-helper.service';
import {ProductStatus} from '@local/core';
import {Request, RestBindings} from '@loopback/rest';

interface EmailData {
  productName: string;
  supportId: string;
  brand: string;
  [key: string]: string;
}

@injectable({scope: BindingScope.TRANSIENT})
export class ProductService {
  private token: string;

  constructor(
    @service(AssetService)
    private readonly assetService: AssetService,
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @service(ProductVariantService)
    private readonly productVariantService: ProductVariantService,
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @restService(ProductVariant)
    private readonly productVariantProxy: ProductVariantProxyType,
    @restService(Payment)
    private paymentProxy: PaymentProxyType,
    @restService(Product)
    private readonly productProxy: ProductProxyType,
    @service(NotificationHelperService)
    private readonly notificationHelper: NotificationHelperService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  private async getProductAssetWithPresigned(
    productAsset: ProductAssetWithRelations,
  ) {
    if (productAsset.asset?.preview) {
      productAsset.asset = this.assetService.getAssetWithPreview(
        productAsset.asset,
      );
    }
    return productAsset;
  }

  async getProductWithPresignedUrl(
    product: ProductWithRelations,
  ): Promise<ProductWithRelations> {
    if (product.featuredAssetId && product?.featuredAsset?.preview) {
      product.featuredAsset = this.assetService.getAssetWithPreview(
        product.featuredAsset,
      );
    }
    if (product.productAssets?.length) {
      product.productAssets = await Promise.all(
        product.productAssets.map(item =>
          this.getProductAssetWithPresigned(item as ProductAssetWithRelations),
        ),
      );
    }
    if (product.productVariants?.length) {
      product.productVariants = await Promise.all(
        product.productVariants.map(variant =>
          this.productVariantService.getProductVariantWithPresignedUrl(
            variant as ProductVariantWithRelations,
          ),
        ),
      );
    }
    return product;
  }

  async applySellerFilter(
    xOrigin: string,
    token: string | undefined,
    filter?: Filter<Product>,
  ): Promise<Filter<Product>> {
    if (xOrigin === 'ecomdukes-seller') {
      const result = await this.authProvider.getMe(token ?? '', xOrigin);
      filter = filter ?? {};

      filter.where = filter.where
        ? {
            ...filter.where,
            sellerId: result.profileId,
          }
        : {sellerId: result.profileId};
    }
    return filter ?? {};
  }
  async getAllProductsWithPreviewUrl(
    products: ProductWithRelations[],
  ): Promise<ProductWithRelations[]> {
    const enrichedProducts = await Promise.all(
      products.map(product => this.getProductWithPresignedUrl(product)),
    );

    return enrichedProducts;
  }
  async filterOutInactiveSellerProducts(
    products: ProductWithRelations[],
  ): Promise<ProductWithRelations[]> {
    const filteredProducts: ProductWithRelations[] = [];

    for (const product of products) {
      // eslint-disable-next-line no-await-in-loop
      const seller = await this.sellerProxyService.findById(product.sellerId);

      if (seller?.status !== SellerStatus.INACTIVE && seller.vendorId) {
        filteredProducts.push(product);
      }
    }

    return filteredProducts;
  }

  async updateVariantsWithZohoItemId(productId: string, token: string) {
    const productVariants = await this.productVariantProxy.find({
      where: {productId},
      include: [
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {id: true, price: true},
          },
        },
      ],
    });

    if (!productVariants.length) {
      return;
    }

    await Promise.all(
      productVariants.map(async variant => {
        const itemPayload = {
          name: variant.name,
          rate: parseFloat(
            (variant.productVariantPrice?.price as unknown as string) ?? '0',
          ), // Ensure it's number

          sku: variant.sku,
          productType: 'goods',
        } as ZohoItemRequest;

        const createdItem = await this.paymentProxy.createItem(
          itemPayload,
          token,
        );
        console.log(
          '🚀 ~ ProductService ~ updateVariantsWithZohoItemId ~ createdItem:',
          createdItem,
        );

        if (createdItem?.itemId) {
          await this.productVariantProxy.updateById(variant?.id ?? '', {
            zohoItemId: createdItem.itemId,
          });
        }
      }),
    );
  }

  async bulkStatusUpdate(bulkUpdate: BulkStatusUpdateDto): Promise<void> {
    // Update product statuses via backend service
    await this.productProxy.bulkStatusUpdate(bulkUpdate, this.token);

    // Send email notifications for each product
    await Promise.all(
      bulkUpdate.productIds.map(async productId => {
        await this.sendProductStatusNotification(
          productId,
          bulkUpdate.status,
          bulkUpdate.rejectedReason,
        );
      }),
    );
  }

  async updateProductStatus(
    id: string,
    statusUpdate: SingleStatusUpdateDto,
  ): Promise<void> {
    // Update product status via backend service
    await this.productProxy.updateProductStatus(id, statusUpdate, this.token);

    // Send email notification
    await this.sendProductStatusNotification(
      id,
      statusUpdate.status,
      statusUpdate.rejectedReason,
    );
  }

  private async sendProductStatusNotification(
    productId: string,
    status: ProductStatus,
    rejectedReason?: string,
  ): Promise<void> {
    try {
      // Get product details
      const product = await this.productProxy.findById(productId);

      // Get user details from seller
      const user = await this.authProvider.getMe(
        this.token,
        'ecomdukes-seller',
      );

      // Skip email if status is PENDING
      if (status === ProductStatus.PENDING) {
        return;
      }

      // Prepare email template data
      const emailData: EmailData = {
        productName: product.name,
        supportId: '<EMAIL>',
        brand: 'Ecomdukes',
      };

      let templateName = '';
      let subject = '';

      if (status === ProductStatus.APPROVED) {
        templateName = 'product-approved-email.hbs';
        subject = `Your Product "${product.name}" Has Been Approved!`;
      } else if (status === ProductStatus.REJECTED) {
        emailData['rejectionReason'] = rejectedReason || 'No reason provided';
        templateName = 'product-rejection-email.hbs';
        subject = `Your Product "${product.name}" Was Rejected`;
      }

      if (templateName && subject) {
        const formattedEmailData: {[key: string]: string} = Object.fromEntries(
          Object.entries(emailData).filter(([_, value]) => value !== undefined),
        );

        await this.notificationHelper.sendEmail(
          templateName,
          subject,
          formattedEmailData,
          user.email ?? '',
          `${user.firstName} ${user.lastName}`,
        );
      }
    } catch (error) {
      console.error('Failed to send product status notification:', error);
      // Don't throw error to avoid breaking the status update process
    }
  }
}
