import {injectable, BindingScope, inject} from '@loopback/core';
import {
  ModifiedRestService,
  restService,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {MessageType} from 'loopback4-notifications';
import {
  AuthCodeBindings,
  CodeWriterFn,
} from '@sourceloop/authentication-service';
import * as jwt from 'jsonwebtoken';
import {Notification} from '../models';
import {Groups} from '../models/notification/groups.model';
import {PermissionKey} from '../enums';
import {systemUser} from '../constants';

@injectable({scope: BindingScope.TRANSIENT})
export class GroupHelperService {
  constructor(
    @restService(Groups)
    private readonly groupService: ModifiedRestService<Groups>,
    @restService(Notification)
    private readonly notification: ModifiedRestService<Notification>,
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriterFn: CodeWriterFn,
  ) {}

  async createGroup(groupData: Omit<Groups, 'id'>): Promise<Groups> {
    const createdGroup = await this.groupService.create(groupData);

    try {
      await this.createGroupNotification(createdGroup);
    } catch (error) {
      console.error('Failed to create notification for group:', error);
    }

    return createdGroup;
  }

  private async createGroupNotification(group: Groups): Promise<void> {
    const token = await this.getAuthToken();

    const notificationData = {
      subject: 'New Group Created',
      body: `A new group "${group.name}" has been created successfully.`,
      type: MessageType.Push,
      receiver: {
        to: [
          {
            id: '<EMAIL>',
            name: 'Admin',
          },
        ],
      },
      groupKey: group.id,
      isCritical: false,
    };

    await this.notification.create(notificationData, `Bearer ${token}`);
  }

  private async getAuthToken(): Promise<string> {
    const payload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKey.ViewNotification,
        PermissionKey.CreateNotification,
        PermissionKey.UpdateNotification,
        PermissionKey.DeleteNotification,
      ],
    };

    return this.codeWriterFn(
      jwt.sign(payload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
  }
}
