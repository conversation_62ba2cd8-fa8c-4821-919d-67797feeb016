import {injectable, BindingScope, inject} from '@loopback/core';
import {
  ModifiedRestService,
  restService,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {MessageType} from 'loopback4-notifications';
import {
  AuthCodeBindings,
  CodeWriterFn,
} from '@sourceloop/authentication-service';
import * as jwt from 'jsonwebtoken';
import {Notification, UserNotification} from '../models';
import {UserTenant} from '@sourceloop/authentication-service';
import {Groups} from '../models/notification/groups.model';
import {PermissionKey} from '../enums';
import {systemUser} from '../constants';

@injectable({scope: BindingScope.TRANSIENT})
export class GroupHelperService {
  constructor(
    @restService(Groups)
    private readonly groupService: ModifiedRestService<Groups>,
    @restService(Notification)
    private readonly notification: ModifiedRestService<Notification>,
    @restService(UserNotification)
    private readonly userNotificationService: ModifiedRestService<UserNotification>,
    @restService(UserTenant)
    private readonly userTenantService: ModifiedRestService<UserTenant>,
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriterFn: CodeWriterFn,
  ) {}

  async createGroup(groupData: Omit<Groups, 'id'>): Promise<Groups> {
    const createdGroup = await this.groupService.create(groupData);

    // Run notification and user notification creation in background
    this.createGroupNotificationAndUserNotifications(createdGroup).catch(
      error => {
        console.error('Failed to create notifications for group:', error);
      },
    );

    return createdGroup;
  }

  private async createGroupNotificationAndUserNotifications(
    group: Groups,
  ): Promise<void> {
    try {
      const createdNotification = await this.createGroupNotification(group);
      if (createdNotification?.id) {
        await this.createUserNotifications(group, createdNotification.id);
      }
    } catch (error) {
      console.error('Failed to create notification for group:', error);
    }
  }

  private async createGroupNotification(group: Groups): Promise<Notification> {
    const token = await this.getAuthToken();

    const notificationData = {
      subject: 'New Group Created',
      body: `A new group "${group.name}" has been created successfully.`,
      type: MessageType.Push,
      receiver: {
        to: [
          {
            id: '<EMAIL>',
            name: 'Admin',
          },
        ],
      },
      groupKey: group.id,
      isCritical: false,
    };

    return this.notification.create(notificationData, `Bearer ${token}`);
  }

  private async createUserNotifications(
    group: Groups,
    notificationId: string,
  ): Promise<void> {
    try {
      const token = await this.getAuthToken();

      // Get all users (customers, sellers, admins) from UserTenant
      const userTenants = await this.userTenantService.find(
        {},
        `Bearer ${token}`,
      );

      if (userTenants.length === 0) {
        console.log('No users found to create user notifications');
        return;
      }

      // Create UserNotification entries for each user
      const userNotifications = userTenants.map((userTenant: UserTenant) => ({
        userTenantId: userTenant.id!,
        notificationId: notificationId,
        isRead: false,
        groupId: group.id!,
        topicId: group.topicId!,
        listKey: group.listKey!,
        listName: group.listName!,
      }));

      // Bulk create user notifications
      await Promise.all(
        userNotifications.map(userNotification =>
          this.userNotificationService.create(
            userNotification,
            `Bearer ${token}`,
          ),
        ),
      );

      console.log(
        `Created ${userNotifications.length} user notifications for group: ${group.name}`,
      );
    } catch (error) {
      console.error('Failed to create user notifications:', error);
      // Don't throw error to avoid breaking group creation
    }
  }

  private async getAuthToken(): Promise<string> {
    const payload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKey.ViewNotification,
        PermissionKey.CreateNotification,
        PermissionKey.UpdateNotification,
        PermissionKey.DeleteNotification,
      ],
    };

    return this.codeWriterFn(
      jwt.sign(payload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
  }
}
