import {injectable, BindingScope, inject, Getter} from '@loopback/core';
import {service} from '@loopback/core';
import {AssetService} from './asset.service';
import {
  Collection,
  CollectionWithRelations,
  IAuthUserWithTenant,
} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {HttpErrors} from '@loopback/rest';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {CollectionStatus} from '../enums/collection-status.enum';

@injectable({scope: BindingScope.TRANSIENT})
export class CollectionService {
  constructor(
    @service(AssetService)
    private readonly assetService: AssetService,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Collection)
    private readonly collectionProxy: ModifiedRestService<Collection>,
  ) {}

  async getCollectionWithPresignedUrl(
    collection: CollectionWithRelations,
  ): Promise<CollectionWithRelations> {
    if (collection.featuredAssetId && collection?.featuredAsset?.preview) {
      collection.featuredAsset = this.assetService.getAssetWithPreview(
        collection.featuredAsset,
      );
    }
    return collection;
  }

  async createCollectionWithCurrentUser(
    data: Omit<Collection, 'id' | 'status'>,
  ): Promise<Collection> {
    const currentUser = await this.getCurrentUser();
    if (!currentUser?.id || !currentUser?.role?.length) {
      throw new HttpErrors.Unauthorized('User info missing');
    }

    // Check for existing collection with same name (not deleted)
    const existing = await this.collectionProxy.find({
      where: {
        name: data.name,
        deleted: false,
      },
    });

    if (existing.length > 0) {
      throw new HttpErrors.BadRequest(
        `Collection with name "${data.name}" already exists`,
      );
    }

    const role = currentUser.role[0].toLowerCase();
    const status = this.mapRoleToStatus(role);

    return this.collectionProxy.create({
      ...data,
      status,
    });
  }

  private mapRoleToStatus(role: string): CollectionStatus {
    switch (role) {
      case 'Seller':
      case 's':
        return CollectionStatus.PENDING;
      case 'Admin':
      default:
        return CollectionStatus.ACTIVE;
    }
  }
}
