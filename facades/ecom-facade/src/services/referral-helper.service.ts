import {BindingScope, injectable} from '@loopback/core';
import {ReferralStatus, ReferralType} from '@local/core';
import {Referral} from '../models/ecom-service/referral.model';

@injectable({scope: BindingScope.TRANSIENT})
export class ReferralHelperService {
  constructor() {}

  generateReferralCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  }

  /**
   * Determine referral type based on X-Origin header
   * @param xOrigin - The X-Origin header value
   */
  determineReferralType(xOrigin?: string): ReferralType {
    if (xOrigin === 'ecomdukes-seller') {
      return ReferralType.SELLER_TO_SELLER;
    }
    return ReferralType.CUSTOMER_TO_CUSTOMER;
  }

  /**
   * Generate a new referral with unique ID and code
   * @param referrerId - ID of the referrer
   * @param xOrigin - The X-Origin header value
   */

  createNewReferral(
    referral: Omit<Referral, 'id' | 'referralCode' | 'status'>,
    xOrigin?: string,
  ): Referral {
    return {
      ...referral,
      referralCode: this.generateReferralCode(),
      type: this.determineReferralType(xOrigin),
      status: ReferralStatus.OPEN,
    } as Referral;
  }
}
