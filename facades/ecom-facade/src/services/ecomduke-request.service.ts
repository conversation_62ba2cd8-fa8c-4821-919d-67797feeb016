// src/services/seller-user.service.ts
import {inject, Getter, BindingScope, injectable} from '@loopback/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithTenant, Seller} from '../models';
import {restService} from '@sourceloop/core';
import {SellerProxyType} from '../datasources/configs';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class SellerUserService {
  constructor(
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Seller)
    private readonly sellerProxy: SellerProxyType,
  ) {}

  async getSellerId(): Promise<string> {
    const user = await this.getCurrentUser();

    const sellers = await this.sellerProxy.find({
      where: {
        userTenantId: user.userTenantId ?? '',
      },
      limit: 1,
    });

    if (!sellers.length) {
      throw new HttpErrors.BadRequest(
        'Seller account not found or not approved',
      );
    }

    return sellers[0].id!;
  }
}
