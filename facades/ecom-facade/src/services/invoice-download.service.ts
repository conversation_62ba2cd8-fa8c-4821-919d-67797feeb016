import {inject, Provider} from '@loopback/core';
import {InvoiceDowloadDataSource} from '../datasources';
import {getService} from '@loopback/service-proxy';

export interface InvoiceDownload {
  generatePdf: (body: {html: string}) => Promise<{pdfUrl: string}>;
}

export class InvoiceDownloadService implements Provider<InvoiceDownload> {
  constructor(
    @inject('datasources.invoiceDownload')
    private datasource: InvoiceDowloadDataSource = new InvoiceDowloadDataSource(),
  ) {}

  async value(): Promise<InvoiceDownload> {
    return getService<InvoiceDownload>(this.datasource);
  }
}
