import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {SellerShippingProfile, SellerShippingProfileDto} from '../../models';
import {FilterExcludingWhere} from '@loopback/repository';

export type SellerShippingProfileProxyType = {
  createShippingProfile(
    data: SellerShippingProfileDto,
    token?: string,
  ): Promise<SellerShippingProfile>;
  updateShippingProfile(
    id: string,
    data: Partial<SellerShippingProfileDto>,
    token?: string,
  ): Promise<SellerShippingProfile>;
  getSellerShippingProfiles(
    sellerId: string,
    token?: string,
    filter?: FilterExcludingWhere<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]>;
  getShippingProfileWithRelations(
    id: string,
    token?: string,
  ): Promise<SellerShippingProfile>;
} & ModifiedRestService<SellerShippingProfile>;

export const SellerShippingProfileProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/seller-shipping-profiles',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createShippingProfile: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/seller-shipping-profiles/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateShippingProfile: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/seller-shipping-profiles/seller/{sellerId}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      query: {filter: '{filter}'},
    },
    functions: {
      getSellerShippingProfiles: ['sellerId', 'token', 'filter'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/seller-shipping-profiles/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getShippingProfileWithRelations: ['id', 'token'],
    },
  },
];
