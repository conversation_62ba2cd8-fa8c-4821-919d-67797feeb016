import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {Admin, AdminDto} from '../../models';

export type AdminProxyType = {
  createAdmin(credentials: Omit<AdminDto, 'id'>, token: string): Promise<Admin>;
  updateAdminById(
    id: string,
    admin: Partial<AdminDto>,
    token: string,
  ): Promise<void>;
} & ModifiedRestService<Admin>;

export const AdminProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/admins',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{admin}',
    },
    functions: {
      createAdmin: ['admin', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/admins/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{admin}',
    },
    functions: {
      updateAdminById: ['id', 'admin', 'token'],
    },
  },
];
