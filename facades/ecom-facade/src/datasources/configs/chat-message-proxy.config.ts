import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {ChatMessage} from '../../models';

export interface ChatMessageProxyType extends ModifiedRestService<ChatMessage> {
  createChatMessage(
    chatMessage: Partial<ChatMessage>,
    token: string,
  ): Promise<ChatMessage>;
}

export const ChatMessageProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/chat-messages',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createChatMessage: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/chat-messages',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      find: ['filter', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/chat-messages/count',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        where: '{where}',
      },
    },
    functions: {
      count: ['where', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/chat-messages/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      findById: ['id', 'filter', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/chat-messages/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateById: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/chat-messages/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteById: ['id', 'token'],
    },
  },
];
