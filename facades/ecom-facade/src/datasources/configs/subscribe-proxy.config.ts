import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {SubscriptionInput} from '../../models/notification/subscription-input.model';
export type SubscribeProxyType = {
  subscribe(
    body: {
      topic: string;
      email?: string;
      fcmToken?: string;
      zohoListKey?: string;
      zohoTopicId?: string;
    },
    token: string,
  ): Promise<void>;
} & ModifiedRestService<SubscriptionInput>;

export const SubscribeProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/subscribe',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      subscribe: ['body', 'token'],
    },
  },
];
