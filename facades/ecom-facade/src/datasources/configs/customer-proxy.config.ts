import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {CustomerDto} from '../../models/ecom-service/dto/customer-dto.model';
import {CustomerStatusUpdateDto} from '../../models/auth-service/dto/customer-status-update-dto.model';

export type CustomerProxyType = {
  updateStatusById(
    id: string,
    customer: Partial<CustomerDto>,
    token: string,
  ): Promise<void>;
} & ModifiedRestService<CustomerDto>;

export const CustomerProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'PATCH',
      url: '/customers/{id}/status',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateStatusById: ['id', 'body', 'token'],
    },
  },
];
