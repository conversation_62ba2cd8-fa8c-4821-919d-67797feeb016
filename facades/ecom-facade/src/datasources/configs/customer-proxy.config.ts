import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {CustomerDto} from '../../models/ecom-service/dto/customer-dto.model';
import {CustomerStatusUpdateDto} from '../../models/auth-service/dto/customer-status-update-dto.model';

export type CustomerProxyType = {
  updateCustomerById(
    id: string,
    customer: Partial<CustomerDto>,
    token: string,
  ): Promise<void>;
  updateStatusById(
    id: string,
    customer: Partial<CustomerDto>,
    token: string,
  ): Promise<void>;
} & ModifiedRestService<CustomerDto>;

export const CustomerProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'PATCH',
      url: '/customers/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateCustomerById: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/customers',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      find: ['filter', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/customers/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      findById: ['id', 'filter', 'token'],
    },
  },
  {
    template: {
      method: 'POST',
      url: '/customers',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      create: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PUT',
      url: '/customers/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      replaceById: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/customers/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteById: ['id', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/customers/{id}/status',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateStatusById: ['id', 'body', 'token'],
    },
  },
];
