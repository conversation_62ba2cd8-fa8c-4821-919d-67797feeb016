export const InvoiceDownloadDataSourceConfig = {
  name: 'invoiceDownload',
  connector: 'rest',
  baseURL: process.env.INVOICE_CDK_BASE_URL,
  crud: false,
  options: {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
    timeout: 10000,
    responseType: 'arraybuffer',
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: `${process.env.INVOICE_CDK_BASE_URL}/prod`,
        body: '{body}',
      },
      functions: {
        generatePdf: ['body'],
      },
    },
  ],
};
