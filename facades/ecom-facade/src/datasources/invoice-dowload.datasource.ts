import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {InvoiceDownloadDataSourceConfig} from './configs';

// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
@lifeCycleObserver('datasource')
export class InvoiceDowloadDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static dataSourceName = 'invoiceDownload';
  static readonly defaultConfig = InvoiceDownloadDataSourceConfig;

  constructor(
    @inject('datasources.config.invoiceDownload', {optional: true})
    dsConfig: object = InvoiceDownloadDataSourceConfig,
  ) {
    super(dsConfig);
  }
}
